from .baseDAO import BaseMongoDAO, BaseMongoQueryFields, BaseMongoSort, SortOrder
from ..DataModels.locker import Locker
from fastapi import Query
from enum import Enum
from pydantic import BaseModel, root_validator


class LockerFilterQueryFields(BaseMongoQueryFields):
    caseId: str = Query(None)
    fileName: str = Query(None)
    fileTag: str = Query(None)
    isRequested: bool = Query(None)
    isShared: bool = Query(None)
    isViewed: bool = Query(None)
    is_master_record: bool = Query(None)
    patient_id: str = Query(None)
    requestId: str | None = Query(None)
    userid: str = None
    uploadedBy: str | None = Query(None)

    
    def apply_filters(self, query_object, dao, *args, **kwargs):
        d  = super().apply_filters(query_object, dao, *args, **kwargs)
        #print("Before: ", d)
        #print("VM!!!!!!!!!!")
        if d.get("is_master_record") is None:
            return d
        if not d.get("is_master_record"):
            d["$or"] = []
            d["$or"].append({"isRequested": True})
            d["$or"].append({"isRequested": False, "isShared": True})
            del d['is_master_record']
            del d['isRequested']
            del d['isShared']
        elif d.get("is_master_record"):
            del d['is_master_record']
            d['isRequested'] = False
            d['isShared'] = False
        #print("After: ", d)
        return d



    
    @root_validator(pre = False)
    @classmethod
    def master_record_validator(cls, values):
        #print("Before: ", values)
        #print("VM!!!!!!!!!!")
        if values["is_master_record"] is None:
            return values
        if not values.get("is_master_record"):
            values["$or"] = []
            values["$or"].append({"isRequested": True})
            values["$or"].append({"isRequested": False, "isShared": True})
            del values['is_master_record']
            del values['isRequested']
            del values['isShared']
        elif values.get("is_master_record"):
            del values['is_master_record']
            values['isRequested'] = False
            values['isShared'] = False
        #print("After: ", values)
        return values

    def set_userid(self, user_id, *args, **kwargs):
        self.userid = user_id

class LockerSort(BaseMongoSort):
    sort_by_recordId: SortOrder = Query(SortOrder.desc)
    sort_by_requestDate: SortOrder = Query(None)


class LockerListingGroups(Enum):
    fileTag = "fileTag"
    doctorId = "doctorId"

class LockerGroup(BaseModel):
    group_by: LockerListingGroups | None = Query(None)


class LockerDAO(BaseMongoDAO):

    collection = "UserLocker"
    _model_class = Locker



locker_set_aggregation = [
    {
        '$match': {
            'userid': '18b5afe3-22e9-47c2-bcdb-1a589793a821',
            'fileTag': 'temp_file_tag'
        }
    }, {
        '$addFields': {
            'type': {
                '$cond': [
                    {
                        '$eq': [
                            '$isShared', False
                        ]
                    }, 'master', {
                        '$cond': [
                            {
                                '$eq': [
                                    '$isRequested', True
                                ]
                            }, 'requested', 'fulfilled'
                        ]
                    }
                ]
            }, 
            'finalRecordId': {
                '$cond': [
                    {
                        '$eq': [
                            '$isShared', False
                        ]
                    }, '$recordId', {
                        '$cond': [
                            {
                                '$eq': [
                                    '$isRequested', True
                                ]
                            }, '$recordId', '$masterRecordId'
                        ]
                    }
                ]
            }
        }
    }, {
        '$group': {
            '_id': '$finalRecordId', 
            'records': {
                '$addToSet': '$$ROOT'
            }, 
            'types': {
                '$addToSet': '$type'
            }
        }
    }, {
        '$unwind': {
            'path': '$records'
        }
    }, {
        '$match': {
            '$or': [
                {
                    '$expr': {
                        '$gte': [
                            {
                                '$size': '$types'
                            }, 2
                        ]
                    }, 
                    'records.isShared': True
                }, {
                    'types': {
                        '$size': 1
                    }
                }
            ]
        }
    }, {
        '$replaceRoot': {
            'newRoot': '$records'
        }
    }
]