import json

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from sqlalchemy.orm import scoped_session
import datetime
from datetime import timed<PERSON><PERSON>

from .api_configs import URL_SHORTENER_PATH, WEB_URL_PATH
from .aws_msg_email_generator import AWSEmailAndMsgSender
from .dbmodels import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DBR<PERSON>tives
from .doctor_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .fire_base_notifications_v1.DOAs.firebase_notification_models_v1 import Message, Data, Notification
from .firebase_models import UserDeviceInfoAdd, DeviceInfoAdd, UserDeviceInfoView, UserActiveDeviceView, \
    FirebaseData, FirebaseMessage, NotificationCategory, NotificationSubCategory, EventMessage, AppointmentEvent, \
    FirebaseMessageDataSave, FamilyMemberAddEvent, Channels, PrescriptionEvent, FamilyDoctorEvent, SubscriptionEvent, \
    MedicationEvent, MentalHealthGroupAppointmentEvent, MentalHealthOthersAppointmentEvent, ChatEvent, \
    FamilyMemberRequestEvent, ConsentEvent, BroadCastMsg
from .patient_models import Notification<PERSON>ushMessage
from .text_local_service.text_local_controller import <PERSON><PERSON><PERSON>al<PERSON><PERSON>roll<PERSON>
from .views import logger, loggers
import pyshorteners

# from ayoo_backend.api.firebase_notification import FireBaseNotification
# notif_ctrl = FireBaseNotification()

from .fire_base_notifications_v1.firebase_notifications_controller_v1 import FireBaseNotificationV1

notif_ctrl = FireBaseNotificationV1()


class FireBaseDeviceController:

    # logger.info(appointment_event.get_msg())
    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def __get_existing_user(self, user_id: str, user_type: str):
        try:
            user = self.mongo_db['UserDeviceInfo'].find(
                dict(user_id=str(user_id), user_type=str(user_type)))

            user_info = []
            if len(list(user.clone())):
                for devices in user:
                    user_info.append({
                        "device_id": devices["device_id"],
                        "device_type": devices["device_type"]
                    })
            # logger.info(user_info)
            return user_info
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting User Id {user_id}'

    def __get_existing_device(self, device_id: str, device_type: str):
        try:
            return self.mongo_db['UserDeviceInfo'].find_one(dict(device_id=device_id, device_type=device_type))
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting Device Id {device_id}'

    def __get_active_devices(self, user_id: str, user_info: UserDeviceInfoAdd):
        try:
            return self.mongo_db['UserDeviceInfo'].find_one(
                dict(user_id=user_id, user_type=user_info.user_type.value, device_id=user_info.device_id,
                     device_type=user_info.device_type))
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting Devices for ID {user_id}'

    def add_device(self, device_info: DeviceInfoAdd):
        try:
            device_exist = self.__get_existing_device(device_id=str(device_info.device_id),
                                                      device_type=str(device_info.device_type.value))

            if device_exist:

                device_data = DeviceInfoAdd(device_id=device_info.device_id,
                                            device_type=device_info.device_type,
                                            fcm_token=device_info.fcm_token
                                            )

                self.mongo_db['UserDeviceInfo'].find_one_and_update(
                    dict(device_id=device_info.device_id, device_type=device_info.device_type), {
                        "$set": dict(device_data)
                    })
            else:

                device_data = DeviceInfoAdd(device_id=device_info.device_id,
                                            device_type=device_info.device_type,
                                            fcm_token=device_info.fcm_token
                                            )
                self.mongo_db['UserDeviceInfo'].insert_one(dict(device_data))
            return dict(device_data), 'device added successfully'


        except Exception as e:
            return None, f'error occurred as {str(e)} while adding device {device_info.device_id}'

    def add_device_user(self, device_info: UserDeviceInfoAdd, user_id: str):
        try:
            device_exist = self.__get_existing_device(device_id=str(device_info.device_id),
                                                      device_type=str(device_info.device_type.value))

            if device_exist:
                # logger.info(device_exist)

                device_data = UserDeviceInfoView(device_id=device_info.device_id,
                                                 device_type=device_info.device_type,
                                                 fcm_token=device_info.fcm_token,
                                                 user_id=user_id,
                                                 user_type=device_info.user_type,
                                                 last_login=datetime.datetime.now()
                                                 )

                self.mongo_db['UserDeviceInfo'].find_one_and_update(
                    dict(device_id=device_info.device_id, device_type=device_info.device_type), {
                        "$set": dict(device_data)
                    })

            else:
                device_data = UserDeviceInfoView(device_id=device_info.device_id,
                                                 device_type=device_info.device_type,
                                                 fcm_token=device_info.fcm_token,
                                                 user_id=user_id,
                                                 user_type=device_info.user_type,
                                                 last_login=datetime.datetime.now()
                                                 )
                self.mongo_db['UserDeviceInfo'].insert_one(dict(device_data))
            return dict(device_data), 'device added successfully'

        except Exception as e:
            return None, f'error occurred as {str(e)} while adding device {device_info.device_id}'

    def get_active_device(self, user_id: str, user_type: str):
        try:
            user_exist = self.__get_existing_user(user_id=str(user_id),
                                                  user_type=str(user_type)
                                                  )

            # logger.info(user_exist)
            if not user_exist:
                return None, f'User {user_id} has no active devices'
            else:
                # logger.info('in else')
                device_data = UserActiveDeviceView(user_id=user_id,
                                                   user_type=user_type,
                                                   device_info=user_exist
                                                   )
                # logger.info(device_data)
            return dict(device_data), 'active device found'

        except Exception as e:
            return None, f'error occurred as {str(e)} while finding device for user with user Id: {user_id}'

    def remove_active_device(self, user_info: UserDeviceInfoAdd, user_id: str):

        try:
            user_exist = self.__get_active_devices(user_id=str(user_id),
                                                   user_info=user_info
                                                   )

            # logger.info(user_exist)
            if not user_exist:
                return None, f'User {user_id} has no active devices'
            else:
                # logger.info('in else')
                device_data = UserActiveDeviceView(user_id=user_id,
                                                   user_type=user_info.user_type,
                                                   device_info=[{"device_id": user_info.device_id,
                                                                 "device_type": user_info.device_type}]
                                                   )
                # logger.info(device_data)
                self.mongo_db['UserDeviceInfo'].find_one_and_update(
                    dict(device_id=user_info.device_id, device_type=user_info.device_type), {
                        "$set": dict(user_id=None, user_type=None, last_login=None)
                    })
            return dict(device_data), 'active device found and removed'

        except Exception as e:
            return None, f'error occurred as {str(e)} while finding device for user with user Id: {user_id}'
    def broadcast_msg(self, msg_data: BroadCastMsg):
        try:
            #print('broadcast msg')
            stored_users = list(self.mongo_db['UserDeviceInfo'].find({'user_type': 'User'}))
            tokens = [user.get('fcm_token') for user in stored_users if user.get('fcm_token')]
            #print('tokens: ', tokens)
            # from ayoo_backend.api.fire_base_notifications_v1.fcm_v1_utility import subscribe_tokens_to_topic, send_fcm_message_to_topic
            # subscribe_tokens_to_topic(tokens=tokens, topic="all_users")
            # response = send_fcm_message_to_topic(topic="all_users", title=data.title, body=data.msg)

            response = []

            for device_token in tokens:
                custom_payload = {
                    'refreshPage':msg_data.title
                }

                # notification_request = dict(
                #     message=dict(
                #         token=device_token,
                #         notification=dict(
                #             title=msg_data.title,
                #             body=msg_data.msg,
                #         ),
                #         data=dict(
                #         title = msg_data.title,
                #         body = msg_data.msg
                #         )
                #     )
                # )

                notification_request = dict(
                message=dict(
                    token=device_token,
                    notification=dict(
                        body=msg_data.msg,
                        title=msg_data.title
                    ),
                    android=dict(
                        notification=dict(
                            click_action="FLUTTER_NOTIFICATION_CLICK"
                        )
                    ),
                    apns=dict(
                        payload=dict(
                            aps=dict(
                                category="FLUTTER_NOTIFICATION_CLICK"
                            )
                        )
                    ),
                    data=dict(
                        payload= json.dumps(custom_payload)
                    )
                )
            )
                # response = send_fcm_message(notification_request)

                from ayoo_backend.api.fire_base_notifications_v1.fcm_v1_utility import get_access_token, send_fcm_message_to_firebase_multi
                headers = {
                    'Authorization': 'Bearer ' + get_access_token(),
                    'Content-Type': 'application/json',
                }

                resp = send_fcm_message_to_firebase_multi(data=notification_request, headers=headers)
                response.append(resp)

            #print(response)
            return response
        except Exception as e:
            raise HTTPException(status_code=409, detail=str(e))

    def get_fcm_access_token(self):
        try:
            from ayoo_backend.api.fire_base_notifications_v1.fcm_v1_utility import get_access_token
            return f"Bearer {get_access_token()}",
        except Exception as e:
            raise HTTPException(status_code=409, detail=str(e))

class FireBaseNotificationController:
    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def __get_all_active_devices(self, user_id: str):
        try:
            return self.mongo_db['UserDeviceInfo'].find(dict(user_id=user_id))
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting active Device Id for {user_id}'

    def __get_patient_details(self, patientid: str):
        from ayoo_backend.api import dbmodels

        patient_data = {}
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            patient_data['firstname'] = resp_user.firstname
            patient_data['lastname'] = resp_user.lastname
            patient_data['dob'] = resp_user.birthdate
            patient_data['email'] = resp_user.email
            patient_data['mobile'] = resp_user.mobile
            patient_data['gender'] = resp_user.gender

            return patient_data

        resp_guest: DBGuest = self.db.query(dbmodels.DBGuest).filter(
            DBGuest.guestid == patientid).one_or_none()
        if resp_guest:
            patient_data['firstname'] = resp_guest.firstname
            patient_data['lastname'] = resp_guest.lastname
            patient_data['dob'] = resp_guest.birthdate
            patient_data['email'] = resp_guest.email
            patient_data['mobile'] = resp_guest.mobile
            patient_data['gender'] = resp_guest.gender

            return patient_data

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).first()
        if resp_relative:
            patient_data['firstname'] = resp_relative.firstname
            patient_data['lastname'] = resp_relative.lastname
            patient_data['dob'] = resp_relative.birthdate
            patient_data['email'] = resp_relative.email
            patient_data['mobile'] = resp_relative.mobile
            patient_data['gender'] = resp_relative.gender

            return patient_data
        else:
            return None

    def __check_user_or_doctor(self, id: str):
        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
        patient_details = self.__get_patient_details(patientid=id)
        doctor_details, msg = doctor_ctrl.get_by_id(
            doctorid=id)
        return patient_details, doctor_details

    def appointment_booking(self, appointment_event: AppointmentEvent):
        loggers['logger6'].info("Notification - Appointment Booked : request :" + str(dict(appointment_event)))
        try:
            id_to_notify = [appointment_event.user_id, appointment_event.doctor_id,
                            None if appointment_event.patient_id == appointment_event.user_id else appointment_event.patient_id]

            msgs = [
                f'{appointment_event.user_name}, you have confirmed the appointment with {appointment_event.doctor_name} for your {(appointment_event.appointment_for).lower()} on {appointment_event.event_date} ',
                f'Dr.{appointment_event.doctor_name}, your slot has been booked with {appointment_event.patient_name} on {appointment_event.event_date}',
                f'{appointment_event.patient_name}, your appointment is scheduled with {appointment_event.doctor_name} on {appointment_event.event_date}']

            # logger.info('id_to_notify: ', id_to_notify)
            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentBook,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Booked', msg=firebase_message)

                    event_message = EventMessage(title='Appointment Booked', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    loggers['logger6'].info(
                        "Notification - Appointment Booked : { msg :" + str(msgs[id_to_notify.index(id)]) + "}")
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentReminder,
                                                           object_id=appointment_event.appointment_id,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=None,
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=appointment_event.jitsi_link)

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])
                        loggers['logger6'].info("Notification - Appointment Booked : fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))
                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])

                        # data_to_save.device_token = fcm_token['fcm_token']
                        # logger.info('notif_status: ', notif_status)
                        # print('notif_status: ', notif_status)
                        loggers['logger6'].info("Notification - Appointment Booked : status :" + str(
                            dict(notification_status=notif_status)))
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.sent_on = datetime.datetime.now()
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            loggers['logger7'].error("Notification - Appointment Booked : error occured as : " + str(e))
            logger.info("Exception: " + str(e))

    # duplicate function of appointment_booking, includes changes required - complete appointment structure
    def appointment_booking_notifs(self, appointment_event: AppointmentEvent,
                                   data_for_patient: NotificationPushMessage,
                                   data_for_doctor: NotificationPushMessage,
                                   data_for_care_taker: NotificationPushMessage = None,
                                   is_rescheduled_appointment: bool = False):
        loggers['logger6'].info("In App Notification - Appointment Booked : request :" + str(dict(appointment_event)))
        try:
            id_to_notify = [appointment_event.user_id, appointment_event.doctor_id,
                            None if appointment_event.patient_id == appointment_event.user_id else appointment_event.patient_id]

            appointment_status = 'rescheduled' if is_rescheduled_appointment is True else 'confirmed'

            msgs = [
                f'{appointment_event.user_name}, you have {appointment_status} the appointment with {appointment_event.doctor_name} for your {appointment_event.appointment_for.lower()} on {appointment_event.event_date}',
                f'Dr.{appointment_event.doctor_name}, your slot has been {appointment_status} with {appointment_event.patient_name} on {appointment_event.event_date}',
                f'{appointment_event.patient_name}, your appointment is {appointment_status} with {appointment_event.doctor_name} on {appointment_event.event_date}']

            for id in id_to_notify:
                if id is not None:

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentBook,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Booked', msg=firebase_message)

                    event_message = EventMessage(title='Appointment Booked', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    loggers['logger6'].info(
                        " In APP Notification - Appointment Booked : { msg :" + str(msgs[id_to_notify.index(id)]) + "}")
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    if id_to_notify.index(id) == 2:
                        meta_data = data_for_patient
                        reminder_msg = f'{meta_data.patient.get("firstname")}, your consultation will start at {meta_data.appointment_slot}'
                    elif id_to_notify.index(id) == 1:
                        meta_data = data_for_doctor
                        reminder_msg = f'{meta_data.doctor.get("name")}, your case will start at {meta_data.appointment_slot}'
                    else:
                        if id_to_notify.index(id) == 0 and id_to_notify[2] is not None:
                            meta_data = data_for_care_taker
                            reminder_msg = f'The consultation for {meta_data.patient.get("firstname")} will start at {meta_data.appointment_slot}'
                        else:
                            meta_data = data_for_patient
                            reminder_msg = f'{meta_data.patient.get("firstname")}, your consultation will start at {meta_data.appointment_slot}'

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentReminder,
                                                           object_id=appointment_event.appointment_id,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=None,
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=appointment_event.jitsi_link,
                                                           notif_msg=msgs[id_to_notify.index(id)],
                                                           reminder_msg=reminder_msg,
                                                           meta_data=meta_data
                                                           )

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    loggers['logger6'].info(" In App Notification - Appointment Booked : " + str(
                        dict(msg_to_broadcast=dict(data_to_save.meta_data))))
                    for fcm_token in active_devices:

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        loggers['logger6'].info("In App Notification - Appointment Booked : fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))

                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast=dict(
                                                                               data_to_save.meta_data))
                        loggers['logger6'].info("In App Notification - Appointment Booked : status :" + str(
                            dict(notification_status=notif_status)))
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.sent_on = datetime.datetime.now()
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            loggers['logger7'].error(" In App Notification - Appointment Booked : error occured as : " + str(e))
            # logger.info("Exception: ", str(e))
            print("Exception: ", str(e))

    def appointment_cancel(self, appointment_event: AppointmentEvent):
        loggers['logger6'].info(f"Notification - Appointment Cancelled : request : {str(dict(appointment_event))}")
        try:
            id_to_notify = [appointment_event.patient_id, appointment_event.doctor_id]

            msgs = [
                f'{appointment_event.patient_name}, your appointment with {appointment_event.doctor_name} on {appointment_event.event_date} has been cancelled.',
                f'Dr.{appointment_event.doctor_name}, your slot booking with {appointment_event.patient_name} on {appointment_event.event_date} has been cancelled.']

            role = ['Patient', 'Doctor']

            for id in id_to_notify:
                if id:
                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentCancel,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Cancelled', msg=firebase_message)
                    loggers['logger6'].info(
                        "Notification - Appointment Cancelled : { msg :" + str(msgs[id_to_notify.index(id)]) + "}")
                    event_message = EventMessage(title='Appointment Cancelled', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentCancel,
                                                           object_id=appointment_event.appointment_id,
                                                           object_active_status=False,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0)
                    self.mongo_db['Notifications'].find_one_and_delete(
                        {"sub_category": NotificationSubCategory.AppointmentReminder,
                         "object_id": appointment_event.appointment_id})
                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        loggers['logger6'].info("Notification - Appointment Cancelled : fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        loggers['logger6'].info(
                            f"Notification - Appointment Cancelled : status :{str(dict(notification_status=notif_status))}")
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    mobile = None
                    email = None
                    aws_mail_ctrl = AWSEmailAndMsgSender()
                    text_local_controller = TextLocalController()
                    if id_to_notify.index(id) == 0:
                        patient_details = self.__get_patient_details(patientid=appointment_event.patient_id)
                        mobile = patient_details['mobile']
                        email = patient_details['email']

                    if id_to_notify.index(id) == 1:
                        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                        appointment_doctor_details, msg = doctor_ctrl.get_by_id(doctorid=appointment_event.doctor_id)
                        mobile = appointment_doctor_details['mobile']

                    if mobile is not None:
                        channel_type.append(Channels.Sms)
                        channel_id.append(str(mobile))

                    if email is not None:
                        channel_type.append(Channels.Email)
                        channel_id.append(str(email))

                    mail_sms_status = aws_mail_ctrl.send_appointment_confirmation(
                        meeting_message=msgs[id_to_notify.index(id)], mobile=mobile, email=email,
                        subject='Appointment Cancelled')

                    data_to_save.notification_status = mail_sms_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    self.mongo_db['Notifications'].find_one_and_update({"object_id": appointment_event.appointment_id,
                                                                        "sub_category": NotificationSubCategory.AppointmentBook,
                                                                        "sent_to": id},
                                                                       {"$set": {"object_active_status": False}})

        except Exception as e:
            loggers['logger6'].info(f"Notification - Appointment Cancelled error: {str(e)}")
            logger.info("Exception: " + str(e))

    def appointment_update(self, appointment_event: AppointmentEvent):
        loggers['logger6'].info("Notification - Appointment Updated : request :" + str(dict(appointment_event)))
        try:
            id_to_notify = [appointment_event.patient_id, appointment_event.doctor_id,
                            None if appointment_event.remarks == appointment_event.doctor_id else appointment_event.remarks]

            msgs = [
                f'{appointment_event.patient_name}, your updated appointment is scheduled on {appointment_event.event_date} with {appointment_event.doctor_name}',
                f'Dr.{appointment_event.doctor_name}, your slot booking with {appointment_event.patient_name} on {appointment_event.event_date} has been confirmed.',
                f'Your slot booking with {appointment_event.patient_name} on {appointment_event.event_date} has been cancelled.'
            ]

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)
                    # logger.info(id+ "line 328")
                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentReschedule,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Updated', msg=firebase_message)
                    loggers['logger6'].info(
                        "Notification - Appointment Updated : { msg :" + str(msgs[id_to_notify.index(id)]) + "}")
                    event_message = EventMessage(title='Appointment Updated', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    a = self.mongo_db['Notifications'].find_one_and_update(
                        {"object_id": appointment_event.appointment_id,
                         "sub_category": NotificationSubCategory.AppointmentReminder, "sent_to": id},
                        {"$set": {
                            "event_datetime": appointment_event.event_date,
                            "sent_on": datetime.datetime.now(),
                            "sent_to": id,
                            "channel_type": [],
                            "channel_id": [],
                            "notification_status": False,
                            "retry_count": 0
                        }})
                    # logger.info(a)
                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentReschedule,
                                                           object_id=appointment_event.appointment_id,
                                                           object_active_status=True,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0)

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])
                        loggers['logger6'].info("Notification - Appointment Updated : fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))
                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        loggers['logger6'].info("Notification - Appointment Updated : status :" + str(
                            dict(notification_status=notif_status)))
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    # data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))


        except Exception as e:
            loggers['logger7'].error("Notification - Appointment Updated : error occured as : " + str(e))
            logger.info("Exception: " + str(e))

    def appointment_booking_email_and_msg_notification(self, appointment_details, service_provider, patient_details,
                                                       booking_data, jitsi_link, doctor_details):
        try:
            # print('Send appointment mail, msg and notif to patients')
            created_at = datetime.datetime.now()

            formatted_date_time = created_at.strftime("%Y-%m-%d %H:%M:%S")
            date_time_now = datetime.datetime.strptime(formatted_date_time, "%Y-%m-%d %H:%M:%S")

            patient_name = patient_details.firstname + ' ' + patient_details.lastname
            # send email
            aws_mail_ctrl = AWSEmailAndMsgSender()

            appointment_date = appointment_details.appointment_slot.strftime("%d %B %Y")
            appointment_time = appointment_details.appointment_slot.strftime("%I:%M %p")

            doctor_name = service_provider[0]['doctor_firstname'] + ' ' + service_provider[0]['doctor_lastname']
            doctor_specialization = service_provider[0]['specialization']

            patient_gender = (patient_details.gender)[0]
            patient_dob = patient_details.birthdate
            current_date = datetime.datetime.today()
            patient_age = current_date.year - patient_dob.year
            if current_date.month < patient_dob.month or (
                    current_date.month == patient_dob.month and current_date.day < patient_dob.day):
                patient_age -= 1

            msg_for_patient = f'Your video consultation with {doctor_name} ({doctor_specialization}) is confirmed for {appointment_date} at {appointment_time}. You will receive an OTP 30 minutes before the start of your consultation.'

            if booking_data.appointment_type == "InClinic":
                msg_for_patient = f'Your consultation with {doctor_name} ({doctor_specialization}) is confirmed for {appointment_date} at {appointment_time}. Please reach at the clinic 15 minutes before the appointment time.'

            msg_for_doctor = f'{patient_name} ({patient_gender}, {patient_age} Yrs) has booked an appointment with you for {appointment_date} at {appointment_time}'

            if appointment_details.appointment_slot > date_time_now:

                time_to_start_appointment = appointment_details.appointment_slot - date_time_now

                if time_to_start_appointment < datetime.timedelta(
                        minutes=30) and booking_data.appointment_type == "Virtual":
                    time_in_minutes = int(time_to_start_appointment.total_seconds() // 60)
                    if jitsi_link:
                        meeting_id = jitsi_link.split('/')[-1]
                        meeting_code = meeting_id[-4:]

                        msg_for_patient = f'Your AYOO Care Video Consultation with {doctor_name} ({doctor_specialization}) will start in {time_in_minutes} minutes. Please join on App or {WEB_URL_PATH} with meeting code- {meeting_code}.\n\nYou will not need meeting code if you are signed in on AYOO Care App or {WEB_URL_PATH} website.'
                elif time_to_start_appointment < datetime.timedelta(
                        minutes=15) and booking_data.appointment_type == "InClinic":
                    time_in_minutes = int(time_to_start_appointment.total_seconds() // 60)
                    msg_for_patient = f'Your AYOO Care Consultation with {doctor_name} ({doctor_specialization}) will start in {time_in_minutes} minutes. Please be available at the clinic.'
            loggers['logger6'].info(
                "Email and Message Notification - Appointment Booked  : patient message :" + str(msg_for_patient))
            loggers['logger6'].info(
                "Email and Message Notification - Appointment Booked : doctor message :" + str(msg_for_doctor))
            aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_patient,
                                                        mobile=patient_details.mobile,
                                                        email=patient_details.email)
            aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_doctor,
                                                        mobile=doctor_details['mobile'],
                                                        email=None)
        except Exception as e:
            loggers['logger7'].error(
                "Email and Message Notification - Appointment Booked : error occured as : " + str(e))
            logger.info("Exception: " + str(e))

    def appointment_reminder(self):
        loggers['logger6'].info("Notification - Appointment Reminder : request :")
        try:
            aws_ctrl = AWSEmailAndMsgSender()
            text_local_controller = TextLocalController()

            active_appointments = self.mongo_db['Notifications'].find(
                {"sub_category": NotificationSubCategory.AppointmentReminder, "notification_status": False, "object_active_status": True})


            appointments = list(active_appointments.clone())
            loggers['logger10'].info(f'Count of appointments: {len(appointments)}')
            #print(f'Count of appointments: {len(appointments)}')
            date_now = datetime.datetime.now()
            for index in appointments:

                # if virtual consultation, remind 15 mints before, else 30 mints before
                # minutes_to_remind = 30 if index['remarks'] == "None" else 15
                minutes_to_remind = 30

                time_delta = date_now + timedelta(minutes=minutes_to_remind)
                time_delta = time_delta.replace(second=0, microsecond=0)
                # logger.info(index['event_datetime'], time_delta)
                if index['event_datetime'] == time_delta:
                    # if True:
                    user_details, doctor_details = self.__check_user_or_doctor(str(index['sent_to']))

                    user_name = ""
                    doctor_name = ""
                    if user_details is not None:
                        user_name = user_details['firstname']

                    if doctor_details is not None:
                        doctor_name = doctor_details['firstname']

                    loggers['logger10'].info(
                        f'\nindex[event_datetime] == time_delta, Appointment id: {index["object_id"]}')
                    loggers['logger10'].info(f'\nUser details: {user_name}')
                    loggers['logger10'].info(f'\nDoctor details: {doctor_name}')

                    msgs = [f'{user_name}, your consultation will start at {index["event_datetime"]}',
                            f'{doctor_name}, you have a case to attend at {index["event_datetime"]}']

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentReminder,
                                                       object_id=index['object_id'],
                                                       event_datetime=index['event_datetime'],
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=index['sent_to'],
                                                       remarks=index['remarks']
                                                       )

                    firebase_data_object = FirebaseData(title=NotificationSubCategory.AppointmentReminder,
                                                        msg=firebase_message)

                    reminder_msg = index.get('reminder_msg')
                    if reminder_msg is None:
                        reminder_msg = msgs[0] if user_details else msgs[1]

                    loggers['logger6'].info(f"Notification - Appointment Reminder : {reminder_msg}")

                    event_message = EventMessage(title=NotificationSubCategory.AppointmentReminder,
                                                 msg=reminder_msg,
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=index['sent_to'])

                    active_devices = list(active_devices.clone())
                    loggers['logger10'].info(f'\n active devices count: {len(active_devices)}')
                    channel_type = []
                    channel_id = []
                    notification_status = False
                    for fcm_token in active_devices:
                        loggers['logger10'].info(f'\n{Channels.Mobile}: fcm_token["fcm_token"]')
                        loggers['logger6'].info("Notification - Appointment Reminder : fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))
                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        # notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                        #                                      device_token=fcm_token['fcm_token'])
                        msg_to_broadcast = dict(index['meta_data']) if 'meta_data' in index else {}
                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast=msg_to_broadcast)
                        loggers['logger6'].info("Notification - Appointment Reminder : status :" + str(
                            dict(notification_status=notif_status)))
                        if notif_status:
                            notification_status = True

                    mobile_num = user_details['mobile'] if user_details is not None else doctor_details['mobile']
                    email_id = user_details['email'] if user_details is not None else doctor_details['email']

                    if index['remarks'] == "None":
                        # Send reminder for in clinic meeting
                        appointment_data = self.mongo_db['Appointments'].find_one(
                            {'appointment_id': index['object_id']})

                        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                        appointment_doctor_details, msg = doctor_ctrl.get_by_id(doctorid=appointment_data['doctorid'])

                        appointment_doctor_specialization = appointment_doctor_details['specialization']
                        appointment_doctor_name = appointment_doctor_details['firstname'] + ' ' + \
                                                  appointment_doctor_details['lastname']

                        if doctor_details is not None:
                            # No mail/msg reminder should be sent
                            # message_to_mail = f'Your appointment will start in {minutes_to_remind} minutes. Please join with the meeting code: {meeting_code}'
                            pass

                        # appointment_date =appointment_data["appointment_slot"].date()
                        # appointment_time = appointment_data["appointment_slot"].time()
                        if user_details is not None:
                            message_to_mail = f'Your AYOO Care Consultation with {appointment_doctor_name} ({appointment_doctor_specialization}) is at {appointment_data["appointment_slot"]}. Please reach at the clinic 15 minutes before the appointment time.'
                            loggers['logger6'].info(
                                "Notification - Appointment Reminder : { msg :" + str(message_to_mail) + "}")

                            mail_and_msg_resp = aws_ctrl.send_appointment_reminder_for_clinic_consultation(
                                meeting_message=message_to_mail,
                                mobile=mobile_num,
                                email=email_id)

                            sms_resp= mail_and_msg_resp
                            # sms_resp = text_local_controller.send_sms(
                            #     template_name='AppointmentReminderPatientInClinic', var_list=[appointment_doctor_name],
                            #     numbers=mobile_num)

                            loggers['logger6'].info("Notification - Appointment Reminder : status :" + str(
                                dict(notification_status=mail_and_msg_resp)))
                            loggers['logger6'].info("Notification - Appointment Reminder : status :" + str(
                                dict(notification_status=sms_resp)))
                            channel_type.append(Channels.Email)
                            channel_type.append(Channels.Sms)
                            channel_id.append(str(mobile_num))
                            channel_id.append(str(email_id))

                            if mail_and_msg_resp:
                                notification_status = True
                                loggers['logger10'].info(
                                    f'{user_name} received the notif. status code: {mail_and_msg_resp}, {sms_resp}')
                    else:
                        # Send reminder for virtual meeting
                        get_meeting_code = self.mongo_db['JitsiMeetInfo'].find_one({'meeting_link': index['remarks']})
                        if get_meeting_code is not None:
                            meeting_code = get_meeting_code['meeting_code']

                            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                            appointment_doctor_details, msg = doctor_ctrl.get_by_id(
                                doctorid=get_meeting_code['doctorid'])

                            appointment_doctor_specialization = appointment_doctor_details['specialization']
                            appointment_doctor_name = appointment_doctor_details['firstname'] + ' ' + \
                                                      appointment_doctor_details['lastname']

                            if doctor_details is not None:
                                # No mail/msg reminder should be sent
                                # message_to_mail = f'Your appointment will start in {minutes_to_remind} minutes. Please join with the meeting code: {meeting_code}'
                                pass

                            if user_details is not None:
                                # s = pyshorteners.Shortener()

                                # joining_url = f'{URL_SHORTENER_PATH}join_appointment?appointment_id={index["object_id"]}'
                                # short_url = s.tinyurl.short(joining_url)

                                # message_to_mail = f'Your AYOO Care Video Consultation with {appointment_doctor_name} ({appointment_doctor_specialization}) will start in {minutes_to_remind} minutes. Please join on App or {WEB_URL_PATH} with meeting code- {meeting_code}.\n\nYou will not need meeting code if you are signed in on AYOO Care App or {WEB_URL_PATH} website.'

                                message_to_mail = f'Your AYOO Care Video Consultation with {appointment_doctor_name} ({appointment_doctor_specialization}) will start in {minutes_to_remind} minutes. Please join on App or {WEB_URL_PATH} with meeting code- {meeting_code}.'

                                loggers['logger6'].info(
                                    "Notification - Appointment Reminder : { msg :" + message_to_mail + "}")

                                mail_and_msg_resp = aws_ctrl.send_appointment_meeting_code(
                                    meeting_message=message_to_mail,
                                    mobile=mobile_num,
                                    email=email_id)
                                sms_resp = mail_and_msg_resp

                                # sms_resp = text_local_controller.send_sms(template_name='AppointmentReminderPatientVirtual', var_list=[appointment_doctor_name, short_url, meeting_code], numbers=mobile_num)
                                # sms_resp = text_local_controller.send_sms(
                                #     template_name='AppointmentReminderPatientVirtual',
                                #     var_list=[appointment_doctor_name, meeting_code], numbers=mobile_num)

                                channel_type.append(Channels.Email)
                                channel_type.append(Channels.Sms)
                                channel_id.append(str(mobile_num))
                                channel_id.append(str(email_id))
                                loggers['logger6'].info("Notification - Appointment Reminder : status :" + str(
                                    dict(notification_status=mail_and_msg_resp)))
                                if mail_and_msg_resp or sms_resp:
                                    notification_status = True

                    reminder_date_time = datetime.datetime.now()

                    self.mongo_db['Notifications'].find_one_and_update(
                        {"_id": index['_id'], "sub_category": NotificationSubCategory.AppointmentReminder,
                         "notification_status": False},
                        {"$set": {
                            "sent_on": reminder_date_time,
                            "channel_type": channel_type,
                            "channel_id": channel_id,
                            "notification_status": notification_status
                        }})
                else:
                    # logger.info('else part')
                    pass

        except Exception as e:
            loggers['logger7'].error("Notification - Appointment Reminder : error occured as : " + str(e))
            logger.info("Exception: " + str(e))

    def mental_health_appointment_booking(self, appointment_event: MentalHealthGroupAppointmentEvent):
        try:
            id_to_notify = [appointment_event.booked_by_id, appointment_event.doctor_id]

            msgs = [
                f'{appointment_event.booked_by_name}, you have booked an appointment with {appointment_event.doctor_name} on {appointment_event.event_date} for Group Counselling',
                f'Dr.{appointment_event.doctor_name}, your slot has been booked on {appointment_event.event_date} for Group Counselling'
            ]

            for patient in appointment_event.patients:
                if patient['patient_id'] not in id_to_notify:
                    id_to_notify.append(patient['patient_id'])
                    msgs.append(
                        f'{patient["patient_name"]}, your appointment is scheduled with {appointment_event.doctor_name} on {appointment_event.event_date} for Group Counselling')

            # logger.info(id_to_notify)
            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentBook,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Booked', msg=firebase_message)

                    event_message = EventMessage(title='Appointment Booked', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentReminder,
                                                           object_id=appointment_event.appointment_id,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=appointment_event.jitsi_link)

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        # logger.info(notif_status)
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def mental_health_appointment_booking_relatives(self, appointment_event: MentalHealthOthersAppointmentEvent):
        try:
            id_to_notify = [appointment_event.booked_by_id, appointment_event.doctor_id]

            msgs = [
                f'{appointment_event.booked_by_name}, you have booked an appointment with {appointment_event.doctor_name} on {appointment_event.event_date} for your {(appointment_event.appointment_for).lower()}',
                f'Dr.{appointment_event.doctor_name}, your slot has been booked on {appointment_event.event_date} for Mental Health Counselling'
            ]

            for patient in appointment_event.patients:
                if patient['patient_id'] not in id_to_notify:
                    id_to_notify.append(patient['patient_id'])
                    msgs.append(
                        f'{patient["patient_name"]}, your appointment is scheduled with {appointment_event.doctor_name} on {appointment_event.event_date}')

            # logger.info(id_to_notify)
            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Appointment,
                                                       sub_category=NotificationSubCategory.AppointmentBook,
                                                       object_id=appointment_event.appointment_id,
                                                       event_datetime=appointment_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=appointment_event.jitsi_link
                                                       )

                    firebase_data_object = FirebaseData(title='Appointment Booked', msg=firebase_message)

                    event_message = EventMessage(title='Appointment Booked', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Appointment,
                                                           sub_category=NotificationSubCategory.AppointmentReminder,
                                                           object_id=appointment_event.appointment_id,
                                                           event_datetime=appointment_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=appointment_event.jitsi_link)

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        # logger.info(notif_status)
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def family_member_add(self, family_member_event: FamilyMemberAddEvent):
        try:
            logger.info('family member add notif def')
            id_to_notify = [family_member_event.relative_one_id, family_member_event.relative_two_id]

            msgs = [
                f'{family_member_event.relative_one_name}, you have added your {family_member_event.relative_one_relation_with_relative_two}, {family_member_event.relative_two_name} in your AYOO account.',
                f'{family_member_event.relative_two_name}, you have been added as {family_member_event.relative_two_relation_with_relative_one} in {family_member_event.relative_one_name}\'s AYOO account.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyMember,
                                                       sub_category=NotificationSubCategory.FamilyMemberAdded,
                                                       object_id=family_member_event.family_id,
                                                       event_datetime=family_member_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=family_member_event.remarks
                                                       )

                    firebase_data_object = FirebaseData(title='Family Member Added', msg=firebase_message)

                    event_message = EventMessage(title='Family Member Added', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.FamilyMember,
                                                           sub_category=NotificationSubCategory.FamilyMemberAdded,
                                                           object_id=family_member_event.family_id,
                                                           event_datetime=family_member_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=family_member_event.remarks)

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast={})
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    # data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))
        except Exception as e:
            logger.info("Exception: " + str(e))

    def family_member_add_request(self, family_member_event: FamilyMemberRequestEvent):
        try:
            logger.info('family member add request notif def')
            id_to_notify = family_member_event.relative_one_id

            msg = f'{family_member_event.relative_two_name}, has requested to add you as a member in AYOO account.'

            if id_to_notify != None:
                firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyMember,
                                                   sub_category=NotificationSubCategory.FamilyMemberAddRequest,
                                                   object_id=family_member_event.relation_id,
                                                   event_datetime=family_member_event.event_date,
                                                   sent_on=datetime.datetime.now(),
                                                   sent_to=id_to_notify,
                                                   remarks=family_member_event.remarks
                                                   )

                firebase_data_object = FirebaseData(title='Family Member Add Request', msg=firebase_message)

                event_message = EventMessage(title='Family Member Add Request', msg=msg,
                                             data=firebase_data_object)
                active_devices = self.__get_all_active_devices(user_id=id_to_notify)

                active_devices = list(active_devices.clone())

                data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.FamilyMember,
                                                       sub_category=NotificationSubCategory.FamilyMemberAddRequest,
                                                       object_id=family_member_event.relation_id,
                                                       event_datetime=family_member_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id_to_notify,
                                                       channel_type=[],
                                                       channel_id=[],
                                                       notification_status=False,
                                                       retry_count=0,
                                                       remarks=family_member_event.remarks)

                channel_type = []
                channel_id = []
                for fcm_token in active_devices:
                    channel_type.append(Channels.Mobile)
                    channel_id.append(fcm_token['fcm_token'])
                    notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                       device_token=fcm_token['fcm_token'],
                                                                       msg_to_broadcast={})
                    if notif_status:
                        data_to_save.notification_status = notif_status

                data_to_save.channel_type = channel_type
                data_to_save.channel_id = channel_id
                self.mongo_db['Notifications'].insert_one(dict(data_to_save))
        except Exception as e:
            logger.info(f"Exception: {str(e)}")

    def family_member_remove(self, family_member_event: FamilyMemberAddEvent):
        try:
            logger.info('family member add notif def')
            id_to_notify = [family_member_event.relative_one_id, family_member_event.relative_two_id]

            msgs = [
                f'{family_member_event.relative_one_name}, you have removed {family_member_event.relative_two_name} as a family member from your AYOO account.',
                f'{family_member_event.relative_two_name}, you have been removed as a family member from {family_member_event.relative_one_name}\'s AYOO account.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyMember,
                                                       sub_category=NotificationSubCategory.FamilyMemberRemoved,
                                                       object_id=family_member_event.family_id,
                                                       event_datetime=family_member_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=family_member_event.remarks
                                                       )

                    firebase_data_object = FirebaseData(title='Family Member Removed', msg=firebase_message)

                    event_message = EventMessage(title='Family Member Removed', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.FamilyMember,
                                                           sub_category=NotificationSubCategory.FamilyMemberRemoved,
                                                           object_id=family_member_event.family_id,
                                                           object_active_status=family_member_event.is_member,
                                                           event_datetime=family_member_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           device_token="",
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=family_member_event.remarks)

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast={})
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    # data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def consent_granted(self, consent_notif_data: ConsentEvent):
        try:
            logger.info('consent grant def')
            id_to_notify = [consent_notif_data.relative_one_id, consent_notif_data.relative_two_id]

            msgs = [
                f'{consent_notif_data.relative_one_name}, you have received the care taker consent for {consent_notif_data.relative_two_name} for {consent_notif_data.consent_duration} days.',
                f'{consent_notif_data.relative_two_name}, you have allowed care taker consent to {consent_notif_data.relative_one_name} for {consent_notif_data.consent_duration} days.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyMember,
                                                       sub_category=NotificationSubCategory.ConsentGranted,
                                                       object_id=consent_notif_data.relation_id,
                                                       event_datetime=consent_notif_data.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=consent_notif_data.remarks
                                                       )

                    firebase_data_object = FirebaseData(title='Consent Granted', msg=firebase_message)
                    #print(msgs[id_to_notify.index(id)])

                    event_message = EventMessage(title='Consent Granted', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.FamilyMember,
                                                           sub_category=NotificationSubCategory.ConsentGranted,
                                                           object_id=consent_notif_data.relation_id,
                                                           event_datetime=consent_notif_data.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=consent_notif_data.remarks)

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast={})
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    # data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))
        except Exception as e:
            logger.info("Exception: " + str(e))

    def consent_removed(self, consent_notif_data: ConsentEvent):
        try:
            logger.info('consent remove def')
            id_to_notify = [consent_notif_data.relative_one_id, consent_notif_data.relative_two_id]

            msgs = [
                f'{consent_notif_data.relative_one_name}, the care taker consent for {consent_notif_data.relative_two_name} has been revoked.',
                f'{consent_notif_data.relative_two_name}, you have revoked the care taker consent from {consent_notif_data.relative_one_name}.']

            for id in id_to_notify:
                if id != None:

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyMember,
                                                       sub_category=NotificationSubCategory.ConsentRemoved,
                                                       object_id=consent_notif_data.relation_id,
                                                       event_datetime=consent_notif_data.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=consent_notif_data.remarks
                                                       )

                    firebase_data_object = FirebaseData(title='Consent Removed', msg=firebase_message)

                    event_message = EventMessage(title='Consent Removed', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.FamilyMember,
                                                           sub_category=NotificationSubCategory.ConsentRemoved,
                                                           object_id=consent_notif_data.relation_id,
                                                           event_datetime=consent_notif_data.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=consent_notif_data.remarks)

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_in_app_notification(firebase_event=event_message,
                                                                           device_token=fcm_token['fcm_token'],
                                                                           msg_to_broadcast={})
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    # data_to_save.sub_category = NotificationSubCategory.AppointmentBook
                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))
        except Exception as e:
            logger.info("Exception: " + str(e))

    def prescription_upload(self, prescription_event: PrescriptionEvent):

        loggers['logger6'].info("Notification - Prescription Uploaded : request :" + str(dict(prescription_event)))
        try:
            # logger.info('pres upload')
            id_to_notify = [prescription_event.patient_id]

            msgs = [
                f'{prescription_event.patient_name}, your prescription is uploaded.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Prescription,
                                                       sub_category=NotificationSubCategory.
                                                       PrescriptionUpload,
                                                       object_id=prescription_event.case_id,
                                                       event_datetime=prescription_event.upload_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=prescription_event.remarks
                                                       )

                    firebase_data_object = FirebaseData(title='Prescription Uploaded', msg=firebase_message)
                    loggers['logger6'].info(
                        "Notification - Prescription Uploaded : { msg :" + str(msgs[id_to_notify.index(id)]) + "}")
                    event_message = EventMessage(title='Prescription Uploaded', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Prescription,
                                                           sub_category=NotificationSubCategory.PrescriptionUpload,
                                                           object_id=prescription_event.case_id,
                                                           event_datetime=prescription_event.upload_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=prescription_event.remarks)

                    # self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        loggers['logger6'].info("Notification - Prescription Uploaded: fcm_token :" + str(
                            dict(device_token=fcm_token['fcm_token'])))
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])

                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        loggers['logger6'].info("Notification - Prescription Uploaded : status :" + str(
                            dict(notification_status=notif_status)))
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            loggers['logger7'].error("Notification - Prescription Uploaded : error occured as : " + str(e))

            logger.info("Exception: " + str(e))

    def family_doctor_add(self, family_doctor_event: FamilyDoctorEvent):
        try:
            id_to_notify = [family_doctor_event.user_id, family_doctor_event.doctor_id]

            msgs = [
                f'{family_doctor_event.user_name}, you have added {family_doctor_event.doctor_name} as your Family Doctor',
                f'{family_doctor_event.doctor_name}, you have been added as Family Doctor for {family_doctor_event.user_name}']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyDoctor,
                                                       sub_category=NotificationSubCategory.
                                                       FamilyDoctorAdded,
                                                       object_id='None',
                                                       event_datetime=family_doctor_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=family_doctor_event.remarks
                                                       )

                    firebase_data_object = FirebaseData(title=NotificationSubCategory.FamilyDoctorAdded,
                                                        msg=firebase_message)

                    event_message = EventMessage(title=NotificationSubCategory.FamilyDoctorAdded,
                                                 msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Prescription,
                                                           sub_category=NotificationSubCategory.PrescriptionUpload,
                                                           object_id='None',
                                                           event_datetime=family_doctor_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=family_doctor_event.remarks)

                    # self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def family_doctor_remove(self, family_doctor_event: FamilyDoctorEvent):
        try:
            id_to_notify = [family_doctor_event.user_id, family_doctor_event.doctor_id]

            msgs = [
                f'{family_doctor_event.user_name}, you have removed {family_doctor_event.doctor_name} as your Family Doctor',
                f'{family_doctor_event.doctor_name}, you have been removed as Family Doctor for {family_doctor_event.user_name}']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.FamilyDoctor,
                                                       sub_category=NotificationSubCategory.
                                                       FamilyDoctorRemoved,
                                                       object_id='None',
                                                       event_datetime=family_doctor_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks=family_doctor_event.remarks
                                                       )

                    firebase_data_object = FirebaseData(title=NotificationSubCategory.FamilyDoctorRemoved,
                                                        msg=firebase_message)

                    event_message = EventMessage(title=NotificationSubCategory.FamilyDoctorRemoved,
                                                 msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Prescription,
                                                           sub_category=NotificationSubCategory.PrescriptionUpload,
                                                           object_id='None',
                                                           event_datetime=family_doctor_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks=family_doctor_event.remarks)

                    # self.mongo_db['Notifications'].insert_one(dict(data_to_save))

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        # logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id
                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def subscription_buy(self, subscription_event: SubscriptionEvent):
        logger.info('subs buy')
        logger.info(subscription_event)
        try:
            id_to_notify = [subscription_event.user_id]

            msgs = [
                f'{subscription_event.user_name}, your Ayoo {subscription_event.plan_type} plan is now active.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Subscription,
                                                       sub_category=NotificationSubCategory.SubscriptionActivate,
                                                       object_id=subscription_event.subscription_id,
                                                       event_datetime=subscription_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks='None'
                                                       )

                    firebase_data_object = FirebaseData(title='Subscription Active', msg=firebase_message)

                    event_message = EventMessage(title='Subscription Active', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Subscription,
                                                           sub_category=NotificationSubCategory.SubscriptionActivate,
                                                           object_id=subscription_event.subscription_id,
                                                           event_datetime=subscription_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks='None'
                                                           )

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def subscription_expired(self, subscription_event: SubscriptionEvent):
        try:
            id_to_notify = [subscription_event.user_id]

            msgs = [
                f'{subscription_event.user_name}, your Ayoo {subscription_event.plan_type} plan is expired.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Subscription,
                                                       sub_category=NotificationSubCategory.SubscriptionExpiry,
                                                       object_id=subscription_event.subscription_id,
                                                       event_datetime=subscription_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks='None'
                                                       )

                    firebase_data_object = FirebaseData(title='Subscription Expired', msg=firebase_message)

                    event_message = EventMessage(title='Subscription Expired', msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Subscription,
                                                           sub_category=NotificationSubCategory.SubscriptionExpiry,
                                                           object_id=subscription_event.subscription_id,
                                                           object_active_status=subscription_event.is_active,
                                                           event_datetime=subscription_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks='None'
                                                           )

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def subscription_expiry_reminder(self, subscription_event: SubscriptionEvent):
        try:
            id_to_notify = [subscription_event.user_id]

            msgs = [
                f'{subscription_event.user_name}, your Ayoo {subscription_event.plan_type} plan will expire in 10 Days.']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Subscription,
                                                       sub_category=NotificationSubCategory.SubscriptionExpiryReminder,
                                                       object_id=subscription_event.subscription_id,
                                                       event_datetime=subscription_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks='None'
                                                       )

                    firebase_data_object = FirebaseData(title=NotificationSubCategory.SubscriptionExpiryReminder,
                                                        msg=firebase_message)

                    event_message = EventMessage(title=NotificationSubCategory.SubscriptionExpiryReminder,
                                                 msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Subscription,
                                                           sub_category=NotificationSubCategory.SubscriptionExpiryReminder,
                                                           object_id=subscription_event.subscription_id,
                                                           event_datetime=subscription_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks='None'
                                                           )

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def medicine_reminder(self, medication_event: MedicationEvent):
        try:
            id_to_notify = [medication_event.user_id]

            msgs = [
                f'{medication_event.user_name}, have {medication_event.dose} {medication_event.dose_type} of {medication_event.medicine_name}']

            for id in id_to_notify:
                if id != None:
                    # logger.info(id_to_notify)

                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Medicines,
                                                       sub_category=NotificationSubCategory.MedicinesReminder,
                                                       object_id=medication_event.medicine_id,
                                                       event_datetime=medication_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id,
                                                       remarks='None'
                                                       )

                    firebase_data_object = FirebaseData(title="Time for your Medicines!",
                                                        msg=firebase_message)

                    event_message = EventMessage(title="Time for your Medicines!",
                                                 msg=msgs[id_to_notify.index(id)],
                                                 data=firebase_data_object)
                    active_devices = self.__get_all_active_devices(user_id=id)

                    active_devices = list(active_devices.clone())

                    # logger.info(len(active_devices))

                    data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Medicines,
                                                           sub_category=NotificationSubCategory.MedicinesReminder,
                                                           object_id=medication_event.medicine_id,
                                                           event_datetime=medication_event.event_date,
                                                           sent_on=datetime.datetime.now(),
                                                           sent_to=id,
                                                           channel_type=[],
                                                           channel_id=[],
                                                           notification_status=False,
                                                           retry_count=0,
                                                           remarks='None'
                                                           )

                    channel_type = []
                    channel_id = []
                    for fcm_token in active_devices:
                        logger.info(fcm_token['fcm_token'])

                        channel_type.append(Channels.Mobile)
                        channel_id.append(fcm_token['fcm_token'])
                        notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                             device_token=fcm_token['fcm_token'])
                        # data_to_save.device_token = fcm_token['fcm_token']
                        if notif_status:
                            data_to_save.notification_status = notif_status

                    data_to_save.channel_type = channel_type
                    data_to_save.channel_id = channel_id

                    self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))

    def chat_message_received(self, chat_event: ChatEvent):
        try:
            id_to_notify = chat_event.to_user_id

            msg = f'Hey {chat_event.to_user_name}, you have received a message from {chat_event.from_user_name}'

            if id_to_notify != None:
                # logger.info(id_to_notify)

                firebase_message = FirebaseMessage(notif_category=NotificationCategory.ChatMessaging,
                                                   sub_category=NotificationSubCategory.NewMessage,
                                                   object_id=chat_event.chat_id,
                                                   event_datetime=chat_event.event_date,
                                                   sent_on=datetime.datetime.now(),
                                                   sent_to=id_to_notify,
                                                   remarks=chat_event.remarks
                                                   )

                firebase_data_object = FirebaseData(title='New Message Received', msg=firebase_message)

                event_message = EventMessage(title='New Message Received', msg=msg,
                                             data=firebase_data_object)
                active_devices = self.__get_all_active_devices(user_id=id_to_notify)

                active_devices = list(active_devices.clone())

                # logger.info(len(active_devices))

                data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.ChatMessaging,
                                                       sub_category=NotificationSubCategory.NewMessage,
                                                       object_id=chat_event.chat_id,
                                                       event_datetime=chat_event.event_date,
                                                       sent_on=datetime.datetime.now(),
                                                       sent_to=id_to_notify,
                                                       channel_type=[],
                                                       channel_id=[],
                                                       notification_status=False,
                                                       retry_count=0,
                                                       remarks=chat_event.remarks)

                # self.mongo_db['Notifications'].insert_one(dict(data_to_save))
                channel_type = []
                channel_id = []
                for fcm_token in active_devices:
                    # logger.info(fcm_token['fcm_token'])

                    channel_type.append(Channels.Mobile)
                    channel_id.append(fcm_token['fcm_token'])
                    notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                         device_token=fcm_token['fcm_token'])
                    # data_to_save.device_token = fcm_token['fcm_token']
                    # logger.info(notif_status)
                    if notif_status:
                        data_to_save.notification_status = notif_status

                data_to_save.channel_type = channel_type
                data_to_save.channel_id = channel_id
                self.mongo_db['Notifications'].insert_one(dict(data_to_save))

        except Exception as e:
            logger.info("Exception: " + str(e))
            return None

    def payment_confirm_and_operation_failure_reminder(self, firebase_message: FirebaseMessage):
        loggers['logger6'].info(
            "Notification - PaymentSuccessOperationFailure : request :" + str(dict(firebase_message)))
        try:

            msgs = f'We regret the failed appointment Booking. We would like to inform you that we have received the payment and we will refund it within a few days.'
            loggers['logger6'].info("Notification - PaymentSuccessOperationFailure : { msg :" + str(msgs) + "}")

            # firebase_message = FirebaseMessage(notif_category=NotificationCategory.Payment,
            #                                    sub_category=NotificationSubCategory.PaymentSuccessOperationFailure,
            #                                    object_id=index['object_id'],
            #                                    event_datetime=index['event_datetime'],
            #                                    sent_on=datetime.datetime.now(),
            #                                    sent_to=index['sent_to'],
            #                                    remarks=index['remarks']
            #                                    )

            firebase_data_object = FirebaseData(title=NotificationSubCategory.PaymentSuccessOperationFailure,
                                                msg=firebase_message)

            event_message = EventMessage(title=NotificationSubCategory.PaymentSuccessOperationFailure,
                                         msg=msgs,
                                         data=firebase_data_object)
            active_devices = self.__get_all_active_devices(user_id=firebase_message.sent_to)

            active_devices = list(active_devices.clone())
            data_to_save = FirebaseMessageDataSave(notif_category=NotificationCategory.Payment,
                                                   sub_category=NotificationSubCategory.PaymentSuccessOperationFailure,
                                                   object_id=firebase_message.object_id,
                                                   event_datetime=firebase_message.event_datetime,
                                                   sent_on=datetime.datetime.now(),
                                                   sent_to=firebase_message.sent_to,
                                                   channel_type=[],
                                                   channel_id=[],
                                                   notification_status=False,
                                                   retry_count=0,
                                                   remarks=firebase_message.remarks)

            channel_type = []
            channel_id = []
            notification_status = False
            for fcm_token in active_devices:
                loggers['logger6'].info("Notification - PaymentSuccessOperationFailure : fcm_token :" + str(
                    dict(device_token=fcm_token['fcm_token'])))
                channel_type.append(Channels.Mobile)
                channel_id.append(fcm_token['fcm_token'])
                notif_status = notif_ctrl.send_notif(firebase_event=event_message,
                                                     device_token=fcm_token['fcm_token'])
                loggers['logger6'].info("Notification - PaymentSuccessOperationFailure : status :" + str(
                    dict(notification_status=notif_status)))

                if notif_status:
                    notification_status = notif_status

                data_to_save.notification_status = notif_status
                data_to_save.channel_type = channel_type
                data_to_save.channel_id = channel_id
                self.mongo_db['Notifications'].insert_one(dict(data_to_save))

            user_details = self.__get_patient_details(patientid=firebase_message.sent_to)
            mobile_num = user_details['mobile']
            email_id = user_details['email']

            if user_details is not None:
                message_to_mail = f'Your AYOO Care Consultation for order id {firebase_message.object_id} was not booked. Since we have received the payment it will be refunded in your account within few days.'

                loggers['logger6'].info(
                    "Notification - PaymentSuccessOperationFailure : { msg :" + str(message_to_mail) + "}")
                aws_ctrl = AWSEmailAndMsgSender()
                msg_status_code, mail_status_code = aws_ctrl.send_payment_or_membership_failure_message(
                    message=message_to_mail,
                    operation_type=firebase_message.remarks,
                    mobile=mobile_num, email=email_id
                )
                loggers['logger6'].info("Notification - PaymentSuccessOperationFailure : msg_status" + str(
                    msg_status_code) + "mail_status" + str(mail_status_code))

                return notification_status, msg_status_code, mail_status_code


        except Exception as e:
            loggers['logger7'].error("Notification - PaymentSuccessOperationFailure : error occured as : " + str(e))
            logger.info("Exception: " + str(e))
