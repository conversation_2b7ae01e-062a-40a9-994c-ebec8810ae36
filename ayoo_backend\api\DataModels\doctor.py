from .basemodel import BaseMongoModel, BaseModel
from typing import List, Optional


class ClinicConsultationAndFees(BaseModel):
    slot_duration: int
    fees: int = None

class Degree(BaseModel):
    year: str
    college: str
    degree: str

class VirtualConsultationAndFees(BaseModel):
    slot_duration: int
    fees: int = None


class Doctor(BaseMongoModel):
    additional_qualification: str
    awards: str
    bio: List[str]
    clinic_consultation_and_fees: List[ClinicConsultationAndFees]
    degree: List[Degree]
    doctorid: str
    doctortype: str
    experience: str
    family_doctor_active: bool | None
    fellowship: str
    graduation: str
    homeaddress: str
    image_id: str | None
    is_active: bool
    is_offering_couple_therapy: bool = False
    is_offering_family_therapy: bool = False
    languages: List[str]
    license: str
    masters: str
    practice_area: List[str]
    interest_area: List[str]
    consultation_symptoms: List[str]
    profile_image_url: str | None
    profilename: str
    residency: str
    signature: str
    specialization: str
    virtual_consultation_and_fees: List[VirtualConsultationAndFees]
    working_hour_ends_at: str
    working_hour_starts_at: str
