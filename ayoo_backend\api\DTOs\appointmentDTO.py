from .baseDTO import <PERSON><PERSON>odelD<PERSON>, BaseModel
from typing import List, Optional
from datetime import datetime, date
from ..DAOs.clinicDAO import <PERSON><PERSON><PERSON>
from ..DAOs.doctorDAO import <PERSON><PERSON><PERSON>, DoctorProfileDAO
from ..DAOs.userDAO import User<PERSON>O
from ..DAOs.relativeDAO import <PERSON><PERSON><PERSON><PERSON>, RelativeListQueryFields
from ..DAOs.jitsiDAO import JitsiDAO
from ..DAOs.paymentDAO import PaymentDAO
from ..DataModels.appointment import AppointmentType, Status
from pydantic import validator, root_validator, Field
from fastapi import HTTPException


class PatientView(BaseModel):
    
    relativeid: str = Field(alias = 'userid')
    firstname: str
    lastname: str
    birthdate: date | datetime = Field(alias = 'dob')
    email: str
    country_code: str = None
    mobile: str
    gender: str
    is_registered: bool = False
    is_active: bool = False
    is_deleted: bool = False

    class Config:
        allow_population_by_field_name = True

    @classmethod
    def get_patient_from_relative_table(cls, relative_id, *args, **kwargs):
        # print("Called this")
        try:
            relative = RelativeDAO().list(filters = RelativeListQueryFields(relativeid = relative_id))[0]
            # print(relative)
        except Exception as e:
            raise Exception(f"Patient {relative_id} not found!!!")
        return cls(**relative.__dict__)

class DoctorView(BaseModel):
    doctorid: str
    name: Optional[str] = ""
    firstname: Optional[str]
    lastname: Optional[str]
    gender: str
    ayooid: str
    languages: List[str]
    graduation: str
    masters: str
    doctortype: str
    specialization: str
    additional_qualification: str
    profile_image_url: str
    consulting_duration_virtual: Optional[int]
    consulting_duration_clinic: Optional[int]
    consulting_fees_virtual: Optional[int]
    consulting_fees_clinic: Optional[int]

    @root_validator(pre = False)
    @classmethod
    def validate_name(cls, values):
        # if "firstname" in values and "lastname" in values:
        values["name"] = " ".join([values.get("firstname"), values.get("lastname")])
            # values.pop("firstname")
            # values.pop("lastname")
        return values


class UpcomingAppointmentDTO(GetModelDTO):
    appointment_id: str
    caseid: str
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: datetime
    end_date: datetime
    slot_duration: Optional[int]
    end_date: datetime
    appointment_type: AppointmentType | None
    appointment_for: str
    patient: List[PatientView]
    patient_name: Optional[str] = ""
    clinic: dict
    doctor: DoctorView
    additional_notes: Optional[str]
    meeting_link: Optional[str] = ""
    appointment_status: Optional[str] = ""
    payment: str
    user_reschedule_allowed: bool
    user_cancellation_allowed: bool


    @root_validator(pre = False)
    @classmethod
    def set_slot_duration(cls, values):
        values["slot_duration"] = int((values.get("end_date") - values.get("appointment_slot")).total_seconds()/60)
        values["booking_cost"] = values.get("payment", 0)
        return values


    @classmethod
    def create_from_appointment_list(cls, appointment_list, clinic_dao = ClinicDAO(), user_dao = UserDAO(), doctor_profile_dao = DoctorProfileDAO(), doctor_dao = DoctorDAO(), jitsi_dao = JitsiDAO(), *args, **kwargs):
        patient_ids = list(set([x.patient_id for x in appointment_list]))
        doctor_ids = list(set([x.doctorid for x in appointment_list]))
        clinic_ids = list(set([x.clinicid for x in appointment_list]))
        appointment_ids = list(set([x.appointment_id for x in appointment_list]))
        meetings = jitsi_dao.retrieve_from_list(value_list = appointment_ids, field_name = "appointment_id")
        patients = user_dao.retrieve_from_list(id_list = patient_ids, field_name = "userid")
        left_patients = [x for x in patient_ids if x not in [y.userid for y in patients]]
        extra_patients = RelativeDAO().retrieve_from_list(id_list = left_patients, field_name = "relativeid")
        doctor_profiles = doctor_profile_dao.retrieve_from_list(id_list = doctor_ids, field_name = "doctorid")
        doctors = doctor_dao.retrieve_from_list(value_list = doctor_ids, field_name = "doctorid")
        clinics = clinic_dao.retrieve_from_list(id_list = clinic_ids, field_name = "clinicid")
        patient_dict = {x.userid: x.__dict__ for x in patients}
        patient_dict.update({x.relativeid: x.__dict__ for x in extra_patients})
        doctor_profile_dict = {x.doctorid: x.__dict__ for x in doctor_profiles}
        doctor_dict = {x.doctorid: x.dict() for x in doctors}
        clinic_dict = {x.clinicid: x.__dict__ for x in clinics}
        meeting_dict = {x.appointment_id: x.patient_joining_link for x in meetings}
        # print(patient_dict.keys())
        all_appointments = []
        for appointment in appointment_list:
            # Handle multiple patients if patients array exists
            patient = None
            patient_name = ""
            if hasattr(appointment, 'patients') and appointment.patients and len(appointment.patients) > 1:
                # Multiple patients case
                patients_array = []
                patient_names = []
                for patient_id in appointment.patients:
                    if patient_id in patient_dict:
                        patient_details = PatientView(**patient_dict.get(patient_id))
                        patients_array.append(patient_details)
                        patient_names.append(f"{patient_details.firstname} {patient_details.lastname}")
                    else:
                        try:
                            patient_details = PatientView.get_patient_from_relative_table(relative_id=patient_id)
                            patients_array.append(patient_details)
                            patient_names.append(f"{patient_details.firstname} {patient_details.lastname}")
                        except Exception:
                            pass
                patient = patients_array
                patient_name = ", ".join(patient_names)
            else:
                # Single patient case (original logic)
                if appointment.patient_id in patient_dict:
                    patient = [PatientView(**patient_dict.get(appointment.patient_id))]
                else:
                    patient = [PatientView.get_patient_from_relative_table(relative_id = appointment.patient_id)]
                patient_name = f"{patient[0].firstname} {patient[0].lastname}"
            
            try:
                all_appointments.append(cls(
                **appointment.dict(), 
                patient = patient,
                patient_name = patient_name,
                doctor = DoctorView(**{**doctor_dict.get(appointment.doctorid, {}),**doctor_profile_dict.get(appointment.doctorid, {})}),
                clinic = clinic_dict.get(appointment.clinicid, {}),
                meeting_link = meeting_dict.get(appointment.appointment_id)
            ))
            except Exception as e:
                raise HTTPException(409, f"Conflict: {str(e)}" )
        try:
            return all_appointments
        except Exception as e:
            return HTTPException(400, str(e))


class BlockConflictDTO(GetModelDTO):
    appointment_id: str
    caseid: str
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: datetime
    end_date: datetime
    slot_duration: Optional[int]
    end_date: datetime
    appointment_type: AppointmentType | None
    appointment_for: str
    patient: PatientView
    additional_notes: Optional[str]
    appointment_status: Optional[str] = ""
    payment: str
    booked_by: str
    booked_by_name: str
    doctorid: str
    status: Status


    @root_validator(pre = False)
    @classmethod
    def set_slot_duration(cls, values):
        values["slot_duration"] = int((values.get("end_date") - values.get("appointment_slot")).total_seconds()/60)
        values["booking_cost"] = values.get("payment", 0)
        return values


    @classmethod
    def create_from_appointment_list(cls, appointment_list, user_dao = UserDAO(), *args, **kwargs):
        patient_ids = list(set([x.patient_id for x in appointment_list]))
        user_patients = user_dao.retrieve_from_list(id_list = patient_ids, field_name = "userid")
        relative_patients = RelativeDAO().retrieve_from_list(id_list = patient_ids, field_name = "relativeid")
        user_patient_dict = {x.userid: x.__dict__ for x in user_patients}
        relative_patient_dict = {x.relativeid: x.__dict__ for x in relative_patients}
        patient_dict = {**user_patient_dict, **relative_patient_dict}
        # print(patient_dict.keys())
        all_appointments = []
        for appointment in appointment_list:
            if appointment.patient_id in patient_dict:
                patient = PatientView(**patient_dict.get(appointment.patient_id))
            else:
                patient = PatientView.get_patient_from_relative_table(relative_id = appointment.patient_id)
            try:
                all_appointments.append(cls(
                **appointment.dict(), 
                patient = patient
            ))
            except Exception as e:
                raise HTTPException(409, f"Conflict: {str(e)}" )
        try:
            return all_appointments
        except Exception as e:
            return HTTPException(400, str(e))

class AdminAppointmentListingDTO(GetModelDTO):
    created_at: Optional[datetime]
    appointment_slot: datetime
    caseid: str|None = None
    patient_name: str
    doctor_name: str
    booked_by: str
    booked_by_name: str
    appointment_for: str
    appointment_type: str
    slot_duration: Optional[int]
    appointment_status: str
    booked_via: Optional[str]
    payment: int
    promo_code: Optional[str]
    payment_mode: Optional[str]
    payment_status: str


    @classmethod
    def create_from_appointment_list(cls, appointment_list, user_dao = UserDAO(), doctor_profile_dao = DoctorProfileDAO(), payment_dao = PaymentDAO(), *args, **kwargs):
        #patient_name
        #doctor_name
        #promo_code
        #payment_mode
        #payment_status
        patient_ids = list(set([x.patient_id for x in appointment_list]))
        doctor_ids = list(set([x.doctorid for x in appointment_list]))
        appointment_ids = list(set([x.appointment_id for x in appointment_list]))
        user_patients = user_dao.retrieve_from_list(id_list = patient_ids, field_name = "userid")
        relative_patients = RelativeDAO().retrieve_from_list(id_list = patient_ids, field_name = "relativeid")
        user_patient_dict = {x.userid: x.__dict__ for x in user_patients}
        relative_patient_dict = {x.relativeid: x.__dict__ for x in relative_patients}
        patient_dict = {**user_patient_dict, **relative_patient_dict}
        doctor_profiles = doctor_profile_dao.retrieve_from_list(id_list = doctor_ids, field_name = "doctorid")
        doctor_dict = {x.doctorid: x.__dict__ for x in doctor_profiles}
        payments= PaymentDAO().retrieve_from_list(value_list = appointment_ids, field_name = "appointment_id")
        payment_dict = {x.appointment_id: x.dict(exclude_none = True) for x in payments}
        all_appointments = []
        for appointment in appointment_list:
            patient = patient_dict.get(appointment.patient_id, {})
            doctor = doctor_dict.get(appointment.doctorid, {})
            payment = payment_dict.get(appointment.appointment_id, {})
            try:
                all_appointments.append(cls(
                **{
                    **appointment.dict(),
                    "patient_name": " ".join([patient.get("firstname", ""), patient.get("lastname", "")]),
                    "doctor_name": " ".join([doctor.get("firstname", ""), doctor.get("lastname", "")]),
                    "appointment_status": appointment.status.status.value,
                    "slot_duration":int((appointment.end_date - appointment.appointment_slot).total_seconds()/60),
                    **payment
                   }
            ))
            except Exception as e:
                print(appointment.appointment_id, str(e))
        try:
            return all_appointments
        except Exception as e:
            return HTTPException(400, str(e))


class CancelAppointmentPayload(BaseModel):
    patient_id: str
    reason: str = "No longer available"
    comment: str = "No longer available on the previously agreed slot"

class RescheduleAppointmentPayload(BaseModel):
    appointment_slot: str
    reason: str = "No longer available"
    comment: str = "No longer available on the previously agreed slot"
    appointment_type: AppointmentType

    @root_validator(pre = False)
    @classmethod
    def change_str_to_date(cls, values):
        str_format = '%Y-%m-%d %I:%M %p'
        values["appointment_slot"] = datetime.strptime(values["appointment_slot"], str_format)
        return values
