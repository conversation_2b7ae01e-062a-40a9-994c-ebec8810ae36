from ..DAOs.doctorDAO import <PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, Query
from ..services.auth import get_admin_from_token
from .payloads.admin_slot import AdminSlotCreate
from ..DAOs.virtualSlotDAO import <PERSON>SlotDA<PERSON>, GetByDoctor<PERSON><PERSON>y<PERSON>ields, DashBoardQueryFields as DashBoardAvailability
from ..DTOs.adminslots import AdminSlotDTO
from ..DAOs.appointmentDAO import AppointmentDA<PERSON>, DashBoardQueryFields as DashBoardAppointments
from ..DAOs.slotDAO import DashBoardQueryFields as DashBoardSlotsQuery, SlotDAO
from datetime import datetime

weekdays = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]

def get_next_day(day):
    if day.lower() == "saturday":
        return "sunday"
    return weekdays[weekdays.index(day)]

slots_router = APIRouter(prefix= "/slots", dependencies=[Depends(get_admin_from_token)], tags = ["slots"])

@slots_router.post("/availability")
async def get_doctors_occupancy(payload: DashBoardAvailability):
    number_of_days = int((payload.end_time - payload.start_time).days)
    start_day = payload.start_time.strftime("%A").lower()
    end_day = payload.end_time.strftime("%A").lower()
    number_of_weeks = number_of_days//7
    week_day_wise_days = {x: number_of_weeks for x in weekdays}
    extra_days = number_of_days%7
    extra_day = start_day
    while extra_days:
        week_day_wise_days[extra_day] += 1
        extra_day = get_next_day(extra_day)
        extra_days -= 1
    slots = VirtualSlotDAO().list(skip = 0, limit = 200, filter = payload)
    app_query = DashBoardAppointments(start_time = payload.start_time, end_time = payload.end_time, doctors = payload.doctors)
    appointments = AppointmentDAO().list(skip = 0, limit = 1000, filter = app_query)
    slots_query = DashBoardSlotsQuery(start_time = payload.start_time, end_time = payload.end_time, doctors = payload.doctors)
    blocks = SlotDAO().list(skip = 0, limit = 1000, filter = slots_query)
    doctor_map = {x.doctorid: x for x in slots}
    result_map = {key: {"virtual_appointments": 0, "in_clinic_appointments": 0, "virtual_availability": 0, "in_clinic_availability": 0} for key in doctor_map}
    for key, result in result_map.items():
        for day in weekdays:
            day_list = getattr(doctor_map[key], day)
            for item in day_list:
                if item.availability_type.value == "InClinic":
                    diff = datetime.combine(datetime.today(), datetime.strptime(item.ends_at, '%I:%M %p').time()) - datetime.combine(datetime.today(), datetime.strptime(item.starts_at, '%I:%M %p').time())
                    result_map[key]["in_clinic_availability"] += ((diff.total_seconds()/60) * week_day_wise_days[day])
                elif item.availability_type.value == "Virtual":
                    diff = datetime.combine(datetime.today(), datetime.strptime(item.ends_at, '%I:%M %p').time()) - datetime.combine(datetime.today(), datetime.strptime(item.starts_at, '%I:%M %p').time())
                    result_map[key]["virtual_availability"] += ((diff.total_seconds()/60) * week_day_wise_days[day])
    for block in blocks:
        diff = block.end_time - block.start_time
        if block.type.value == "SLOT":
            if block.sub_type.value == "Virtual":
                result_map[block.doctor_id]["virtual_availability"] += diff.total_seconds()/60
            elif block.sub_type.value == "InClinic":
                result_map[block.doctor_id]["in_clinic_availability"] += diff.total_seconds()/60
        elif block.type.value == "BLOCK":
            if block.sub_type.value == "Virtual":
                result_map[block.doctor_id]["virtual_availability"] -= diff.total_seconds()/60
            elif block.sub_type.value == "InClinic":
                result_map[block.doctor_id]["in_clinic_availability"] -= diff.total_seconds()/60

    for app in appointments:
        slot_duration = app.end_date - app.appointment_slot
        if app.appointment_type == "Virtual":
            result_map[app.doctorid]["virtual_appointments"] += slot_duration.total_seconds()/60
        elif app.appointment_type == "InClinic":
            result_map[app.doctorid]["in_clinic_appointments"] += slot_duration.total_seconds()/60
    doctor_data = DoctorDAO().retrieve_from_list([x for x in doctor_map], field_name = "doctorid")
    for doctor_obj in doctor_data:
        result_map[doctor_obj.doctorid]["doctor_id"] = doctor_obj.doctorid
        result_map[doctor_obj.doctorid]["profile_name"] = doctor_obj.profilename

    return [v for k,v in result_map.items()]


@slots_router.get("/{slot_id}")
async def get_slot(slot_id: str):
    slot = VirtualSlotDAO().get(slot_id)
    return AdminSlotDTO.create_from_model(slot)

@slots_router.post("/")
async def create_slot(payload: AdminSlotCreate):
    # Validate time format is HH:MM AM/PM
    try:
        for day in weekdays:
            day_data = getattr(payload, day, None)
            if day_data:
                # Check InClinic slot times
                if day_data.InClinic:
                    if day_data.InClinic.start_time:
                        datetime.strptime(day_data.InClinic.start_time, "%I:%M %p")
                    if day_data.InClinic.end_time:
                        datetime.strptime(day_data.InClinic.end_time, "%I:%M %p")
                    if day_data.InClinic.break_start:
                        datetime.strptime(day_data.InClinic.break_start, "%I:%M %p")
                    if day_data.InClinic.break_end:
                        datetime.strptime(day_data.InClinic.break_end, "%I:%M %p")
                
                # Check Virtual slot times
                if day_data.Virtual:
                    if day_data.Virtual.start_time:
                        datetime.strptime(day_data.Virtual.start_time, "%I:%M %p")
                    if day_data.Virtual.end_time:
                        datetime.strptime(day_data.Virtual.end_time, "%I:%M %p")
                    if day_data.Virtual.break_start:
                        datetime.strptime(day_data.Virtual.break_start, "%I:%M %p")
                    if day_data.Virtual.break_end:
                        datetime.strptime(day_data.Virtual.break_end, "%I:%M %p")
    except ValueError:
        raise HTTPException(400, "Time format must be HH:MM AM/PM (e.g., 10:00 AM)")
    
    slots = VirtualSlotDAO().list(skip = 0, limit = 1, filter = GetByDoctorQueryFields(doctorid = payload.doctorid))
    if len(slots) == 0:
        return VirtualSlotDAO().create(payload.to_virtual_slot_model())
    else:
        slot = slots[0]
        _id = slot.id
        return VirtualSlotDAO().update(_id, payload.to_virtual_slot_model())

@slots_router.put("/{slot_id}")
async def update_slot(slot_id: str, payload: AdminSlotCreate):
    return VirtualSlotDAO().update(slot_id, payload.to_virtual_slot_model())

@slots_router.get("/doctor/{doctor_id}")
async def get_slot(doctor_id: str):
    slots = VirtualSlotDAO().list(skip = 0, limit = 1, filter = GetByDoctorQueryFields(doctorid = doctor_id))
    if not len(slots):
        raise HTTPException(204, {"details": "slots for doctor {doctor_id} not found!!!!"})
    return AdminSlotDTO.create_from_model(slots[0])


