from ..database import Base, get_db
from ..mongodb import mongodb_conn
from fastapi import HTTP<PERSON>xception
from pydantic import BaseModel
from enum import Enum
from ..validators import BaseValidator, ValidationError
import uuid
from datetime import datetime
from typing import List
from ..DataModels.basemodel import BaseMongoModel
from bson.objectid import ObjectId


class NotDefinedException:
    def __init__(self, message):
        super().__init__(message)


class BaseSQLQueryFields(BaseModel):

    def apply_filters(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none=True)
        for key, value in d.items():
            column = getattr(dao._model_class, key)
            query_object = query_object.filter(column == value)
        return query_object


class BaseMongoQueryFields(BaseModel):

    def apply_filters(self, query_object, dao, *args, **kwargs):
        return self.dict(exclude_none=True)


class SortOrder(Enum):

    asc = "asc"
    desc = "desc"


class BaseSQLSort(BaseModel):

    # assuming sort_by_ prefix
    def remove_prefix(self, t): return t[8:]

    def apply_sort(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none=True)
        for key, value in d.items():
            column = getattr(dao._model_class, self.remove_prefix(key))
            if value == SortOrder.asc:
                query_object = query_object.order_by(column)
            elif value == SortOrder.desc:
                query_object = query_object.order_by(column.desc())
        return query_object


class BaseMongoSort(BaseModel):

    # assuming sort_by_prefix
    def remove_prefix(self, t): return t[8:]

    def apply_sort(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none=True)
        sort_list = []
        for key, value in d.items():
            if value == SortOrder.asc:
                sort_list.append((self.remove_prefix(key), 1))
            elif value == SortOrder.desc:
                sort_list.append((self.remove_prefix(key), -1))
        if sort_list == []:
            return [("_id", 1)]
        return sort_list


class BaseDAO:

    def create(self, _obj, *args, **kwargs):
        raise NotDefinedException("Method not defined!!!")

    def update(self, _id, _obj, *args, **kwargs):
        raise NotDefinedException("Method not defined!!!")

    def get(self, _id, *args, **kwargs):
        raise NotDefinedException("Method not defined!!!")

    def list(self, filter, sort, *args, **kwargs):
        raise NotDefinedException("Method not defined!!!")

    def delete(self, _id, *args, **kwargs):
        raise NotDefinedException("Method not defined!!!")


class BaseSQLDAO(BaseDAO):
    db = get_db()

    def create(self, _obj, validators: List[BaseValidator] = [], *args, **kwargs):
        _object = self._model_class(**_obj, **kwargs)
        if (self.pk not in dict(_obj).keys()):
            _object = self._model_class(
                **{self.pk: str(uuid.uuid4())}, **_obj, **kwargs)
        for validator in validators:

            try:
                validator(_object=_object, model_class=self._model_class,
                          dao=self, *args, **kwargs)
            except ValidationError as e:
                raise HTTPException(400, str(e))
        try:
            self.db.add(_object)
            self.db.commit()
            return _object
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, e)

    def get(self, _id, *args, **kwargs):
        try:
            _object = self.db.query(self._model_class).filter(
                getattr(self._model_class, self.pk) == _id).first()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))
        if _object:
            return _object
        raise HTTPException(404, f"Object {_id} not found !!!!!")

    def list(self, skip=0, limit=10, filters=BaseSQLQueryFields(), sort=BaseSQLSort(), *args, **kwargs):
        query = self.db.query(self._model_class)
        query = filters.apply_filters(query, self)
        query = sort.apply_sort(query, self)
        try:
            if limit != 0:
                return query.offset(skip).limit(limit).all()
            else:
                return query.offset(skip).all()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))

    def delete(self, _id, *args, **kwargs):
        try:
            _object = self.db.query(self._model_class).filter(
                getattr(self._model_class, self.pk) == _id).first()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))
        if not _object:
            raise HTTPException(404, f"Object {_id} not Found!!!")
        self.db.delete(_object)
        try:
            self.db.commit()
            return {"detail": f"Object {_id} Deleted!!!"}
        except Exception as e:
            self.db.rollback()
            return HTTPException(400, str(e))

    def update(self, _id, _obj, validators: List[BaseValidator] = [], *args, **kwargs):
        new_object = self._model_class(**_obj)
        try:
            _object = self.db.query(self._model_class).filter(
                getattr(self._model_class, self.pk) == _id).first()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))
        if not _object:
            raise HTTPException(404, "Object {_id} not found !!!!!")
        for validator in validators:

            try:
                validator(new_object=new_object, old_object=_object, pk=_id,
                          model_class=self._model_class, dao=self, *args, **kwargs)
            except ValidationError as e:
                raise HTTPException(400, str(e))
        _object.update(new_object)
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))
        return new_object

    def retrieve_from_list(self, id_list, field_name, *args, **kwargs):
        try:
            query = self.db.query(self._model_class)
            return query.filter(getattr(self._model_class, field_name).in_(id_list)).all()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))

    def get_count(self, filters=BaseSQLQueryFields(), *args, **kwargs):
        query = self.db.query(self._model_class)
        query = filters.apply_filters(query, self)
        try:
            return query.count()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))

    def bulk_create(self, objs, validators: List[BaseValidator] = [], *args, **kwargs):
        def get_object(_obj):
            _object = self._model_class(**_obj, **kwargs)
            if (self.pk not in dict(_obj).keys()):
                _object = self._model_class(
                    **{self.pk: str(uuid.uuid4())}, **_obj, **kwargs)
            return _object

        entries = [get_object(entry.dict()) for entry in objs]
        self.db.add_all(entries)
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))

class BaseMongoDAO(BaseDAO):
    db = mongodb_conn["ayoo"]
    _model_class: BaseMongoModel

    def create(self, _obj, validators: List[BaseValidator] = [], *args, **kwargs):
        _obj = self._model_class(
            **_obj.dict(exclude_none=False, by_alias=True))
        _obj.set_created_at(datetime.now())
        for validator in validators:
            try:
                validator(_object=_obj, model_class=self._model_class,
                          dao=self, *args, **kwargs)
            except ValidationError as e:
                raise HTTPException(400, str(e))
        try:
            _obj.set_created_at(datetime.now())
            _id = self.db[self.collection].insert_one(
                _obj.dict(exclude_none=True, by_alias=True)).inserted_id
            return {"status": "Success", "_id": str(_id)}
        except Exception as e:
            raise HTTPException(500, {"details": str(e)})

    def update(self, _id, _obj, validators: List[BaseValidator] = [], *args, **kwargs):
        _obj = self._model_class(
            **_obj.dict(exclude_none=True, by_alias=True), _id=ObjectId(_id))
        for validator in validators:
            try:
                validator(_object=_obj, model_class=self._model_class,
                          dao=self, *args, **kwargs)
            except ValidationError as e:
                raise HTTPException(400, str(e))
        try:
            _obj.set_updated_at(datetime.now())
            _obj = self.db[self.collection].update_one({"_id": ObjectId(_id)}, {
                                                       "$set": {**_obj.dict(exclude_none=True, by_alias=True, exclude={"id"})}})
            if _obj.matched_count:
                return {"status": "Success", "details": f"matched: {_obj.matched_count} \n modified: {_obj.modified_count}"}
            else:
                raise HTTPException(
                    400, {"status": "Failed", "details": "Something went wrong!!!!"})
        except Exception as e:
            raise HTTPException(500, {"details": str(e)})

    def patch(self, _id, field, value, validators: List[BaseValidator] = [], *args, **kwargs):
        for validator in validators:
            try:
                validator(field_name=field, value=value,
                          model_class=self._model_class, dao=self, *args, **kwargs)
            except ValidationError as e:
                raise HTTPException(400, str(e))
        try:
            _output = self.db[self.collection].update_one(
                {"_id": ObjectId(_id)}, {"$set": {field: value, "updated_at": datetime.now()}})
            if _output.matched_count:
                return {"status": "Success", "details": f"matched: {_output.matched_count} \n modified: {_output.modified_count}"}
            else:
                raise HTTPException(
                    400, {"status": "Failed", "details": "Something went wrong!!!"})

        except Exception as e:
            raise HTTPException(500, {"details": str(e)})

    def get(self, _id, *args, **kwargs):
        try:
            if 'key_name' in kwargs.keys():
                _object = self.db[self.collection].find_one(
                    {kwargs.get('key_name'): _id})
            else:
                _object = self.db[self.collection].find_one(
                    {"_id": ObjectId(_id)})

        except Exception as e:
            raise HTTPException(500, detail={"detail": str(e)})
        if _object:
            return self._model_class(**_object)
        raise HTTPException(404, f"Object {_id} not found !!!!!")

    def list(self, skip=0, limit=20, filter=BaseMongoQueryFields(), sort=BaseMongoSort(), *args, **kwargs):
        query = filter.apply_filters(
            query_object=None, dao=self, *args, **kwargs)
        cursor = self.db[self.collection].find(query, skip=skip, limit=limit).sort(
            sort.apply_sort(query_object=None, dao=self, *args, **kwargs))
        try:
            return [self._model_class(**_obj) for _obj in cursor]
        except Exception as e:
            raise HTTPException(500, {"detail": str(e)})

    def delete(self, _id, *args, **kwargs):
        try:
            _output = self.db[self.collection].delete_one(
                {"_id": ObjectId(_id)})
            if _output.deleted_count:
                return {"status": "Success", "details": f"deleted: {_output.deleted_count}"}
            else:
                raise HTTPException(
                    400, {"status": "Failed", "details": "Something went wrong!!!"})
        except Exception as e:
            raise HTTPException(500, {"detail": str(e)})

    def retrieve_from_list(self, value_list, field_name, *args, **kwargs):
        query = {
            field_name: {"$in": value_list}
        }
        cursor = self.db[self.collection].find(query)
        try:
            return [self._model_class(**_obj) for _obj in cursor]
        except Exception as e:
            raise HTTPException(500, {"detail": str(e)})

    def get_count(self, filter=BaseMongoQueryFields(), *args, **kwargs):
        try:
            query = filter.apply_filters(
                query_object=None, dao=self, *args, **kwargs)
            #print(query)
            return self.db[self.collection].count_documents(query)
        except Exception as e:
            raise HTTPException(500, {"detail": str(e)})

    def execute_pipeline(self, pipeline=[], *args, **kwargs):
        try:
            return [x for x in self.db[self.collection].aggregate(pipeline)]
        except Exception as e:
            raise HTTPException(500, {"detail": str(e)})
