from pydantic import root_validator, validator
from .baseDAO import BaseDAO, BaseSQLDAO, BaseSQLQueryFields, BaseSQLSort, SortOrder
from .userDAO import User<PERSON><PERSON>
from .relativeDAO import <PERSON><PERSON><PERSON><PERSON>
from fastapi import HTTPException, Query
from datetime import datetime, date, timedelta
from typing import List, final, Optional
from sqlalchemy import or_, union, literal_column, desc, text, func, extract, and_, DateTime
from sqlalchemy.sql.functions import coalesce
from sqlalchemy.sql.expression import cast
import traceback

class AdminPatientListingQueryFields(BaseSQLQueryFields):
    name: str = Query(None)
    mobile: str = Query(None)
    email: str = Query(None)
    is_registered: bool = Query(None)
    deleted: str = Query(None)

    def apply_filters(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none = True)
        for key, value in d.items():
            if key == "name":
                firstname =  literal_column("firstname")
                lastname = literal_column("lastname")
                query_object = query_object.filter(or_(firstname.ilike(f"%{value}%"), lastname.ilike(f"%{value}%")))
            elif key == "is_registered":
                column = literal_column(key)
                query_object = query_object.filter(column == value)
            elif key == "deleted":
                # Skip this as it can be globally handled
                pass
            else:
                column = literal_column(key)
                query_object = query_object.filter(column.ilike(f"%{value}%"))

        # Deleted can be:
        # - Not provided or "false": Show only non-deleted patients
        # - "true": Show all patients
        # - "only": Show only deleted patients
        if "deleted" not in d or d["deleted"].lower() == "false":
           
            query_object = query_object.filter(literal_column("is_deleted") == False)

        elif d["deleted"].lower() == "only":
           
            query_object = query_object.filter(literal_column("is_deleted") == True)

        return query_object

class LastSevenDaysRegistered(BaseSQLQueryFields):

    def apply_filters(self, query_object, dao, *args, **kwargs):
        today = datetime.combine(datetime.today(), datetime.min.time())
        seven_days_back = today - timedelta(days = 7)
        query_object = query_object.filter(literal_column("date_registered") >= seven_days_back)
        return query_object


class UsersWithAppointments(BaseSQLQueryFields):
    userid: List[str] = []

    def apply_filters(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none = True)
        for key, value in d.items():
            column = literal_column(key)
            if key == "userid":
                query_object = query_object.filter(column.in_(value))
            else:
                query_object = query_object.filter(column == value)
        return query_object

class PIIQuery(BaseSQLQueryFields):
    firstName: str
    lastName: str
    gender: str
    dob: date | datetime
    email: Optional[str] = None
    mobile: Optional[str] = None

    @validator('dob')
    def validate_dob(cls, val):
        if isinstance(val, datetime):
            val = val.date()
        return val 


    def apply_filters(self, query_object, dao, *args, **kwargs):
        query_object = query_object.filter(or_(and_(func.lower(literal_column("firstname")) == func.lower(self.firstName), literal_column("lastname").ilike(f"{self.lastName[0]}%")), and_(func.lower(literal_column("lastname")) == func.lower(self.lastName), literal_column("firstname").ilike(f"{self.firstName[0]}%")))) 
        query_object = query_object.filter(literal_column("gender") == self.gender)
        query_object = query_object.filter(literal_column("birthdate") == self.dob)
        return query_object

class ConfirmDetailsQuery(BaseSQLQueryFields):
    userid: str
    email: str
    mobile: str

    def apply_filters(self, query_object, dao, *args, **kwargs):
        query_object = query_object.filter(literal_column("userid") == self.userid)
        query_object = query_object.filter(literal_column("email") == self.email)
        query_object = query_object.filter(literal_column("mobile").ilike(f"%{self.mobile}"))
        return query_object

class VerifyDetailsQuery(BaseSQLQueryFields):
    userid: str = Query(None)
    email: str = Query(None)
    mobile: str = Query(None)

    def apply_filters(self, query_object, dao, *args, **kwargs):
        if self.userid is not None:
            query_object = query_object.filter(literal_column("userid") == self.userid)
        if self.email is not None:
            query_object = query_object.filter(literal_column("email") == self.email)
        if self.mobile is not None:
            query_object = query_object.filter(literal_column("mobile").ilike(f"%{self.mobile}"))
        return query_object

class PatientExists(BaseSQLQueryFields):
    email: str
    mobile: str

    def apply_filters(self, query_object, dao, *args, **kwargs):
        query_object = query_object.filter(or_(literal_column("email") == self.email, literal_column("mobile") == self.mobile))
        return query_object

class GetPatientByUserID(BaseSQLQueryFields):
    userid: str = Query()

    def apply_filters(self, query_object, dao, *args, **kwargs):
        return query_object.filter(literal_column("userid") == self.userid)

class AdminPatientListingSort(BaseSQLSort):
    sort_by_date_registered: SortOrder = Query(None)
    sort_by_firstname: SortOrder = Query("asc")


    def apply_sort(self, query_object, dao, *args, **kwargs):
        d = self.dict(exclude_none = True)
        for key, value in d.items():
            if key == "mapping":
                continue
            if value == SortOrder.asc:
                query_object = query_object.order_by(self.remove_prefix(key))
            elif value == SortOrder.desc:
                query_object = query_object.order_by(desc(self.remove_prefix(key)))
        return query_object




class AllPatientsDAO(BaseSQLDAO):

    @staticmethod
    def convert_to_dict(patient):
        return  {
        "userid": patient[0],
        "firstname": patient[1],
        "lastname": patient[2],
        "mobile": patient[3],
        "email": patient[4],
        "birthdate": patient[5],
        "gender": patient[6],
        "is_registered": patient[7],
        "date_registered": patient[8],
        "joining_date": patient[9],
        "ayoo_id": patient[10],
        "source": patient[11],
        "is_deleted": patient[12] if len(patient) > 12 else False
        }


    def list(self, skip=0, limit=10, filters: BaseSQLQueryFields = AdminPatientListingQueryFields(), sort: BaseSQLSort = AdminPatientListingSort(), *args, **kwargs):
        user_dao = UserDAO()
        relative_dao = RelativeDAO()
        user_query = self.db.query(user_dao._model_class).with_entities(user_dao._model_class.userid.label("userid"), user_dao._model_class.firstname.label("firstname"), user_dao._model_class.lastname.label("lastname"), user_dao._model_class.mobile.label("mobile"), user_dao._model_class.email.label("email"), user_dao._model_class.birthdate.label("birthdate"), user_dao._model_class.gender.label("gender"), user_dao._model_class.is_registered.label("is_registered"), literal_column("users.joining_date").label("joining_date"), user_dao._model_class.ayoo_id.label("ayoo_id"), user_dao._model_class.date_registered.label("date_registered"),  literal_column("'user'").label('source'), user_dao._model_class.is_deleted.label("is_deleted"))
        relative_query = self.db.query(relative_dao._model_class).with_entities(relative_dao._model_class.relativeid.label("userid"), relative_dao._model_class.firstname.label("firstname"), relative_dao._model_class.lastname.label("lastname"), relative_dao._model_class.mobile.label("mobile"), relative_dao._model_class.email.label("email"), relative_dao._model_class.birthdate.label("birthdate"), relative_dao._model_class.gender.label("gender"),  literal_column('False').label('is_registered'), literal_column("relatives.date_created").label("joining_date"), relative_dao._model_class.ayoo_id.label('ayoo_id'), literal_column('NULL').label('date_registered'), literal_column("'relative'").label('source'), literal_column('False').label('is_deleted'))
        #user_query = filters.apply_filters(user_query, user_dao)
        #relative_query = filters.apply_filters(relative_query, relative_dao)
        #user_query = sort.apply_sort(user_query, user_dao)
        final_query = user_query.union(relative_query).with_entities(text("userid"), func.max(text("firstname")).label("firstname"), func.max(text("lastname")).label("lastname"), func.max(text("mobile")).label("mobile"), func.max(text("email")).label("email"), func.max(text("birthdate")).label("birthdate"), func.max(text("gender")).label("gender"), func.bool_or(text("is_registered")).label("is_registered"), func.max(text("date_registered")).label("date_registered"), func.max(text("joining_date")), func.min(text("ayoo_id")).label("ayoo_id"), func.max(text("source")).label("source"), func.bool_or(text("is_deleted")).label("is_deleted")).group_by(text("userid"))

        #print(str(final_query))

        final_query = filters.apply_filters(final_query, self)
        final_query = sort.apply_sort(final_query, self)
        #return final_query.all()
        try:
            if limit != 0:
                return [self.convert_to_dict(x) for x in final_query.offset(skip).limit(limit).all()]
            else:
                return [self.convert_to_dict(x) for x in final_query.offset(skip).all()]
        except Exception as e:
            self.db.rollback()
            traceback.print_exc()
            raise HTTPException(400, str(e))

    def get_count(self, filters: BaseSQLQueryFields = AdminPatientListingQueryFields(), *args, **kwargs):
        user_dao = UserDAO()
        relative_dao = RelativeDAO()

        if 'apply_year_filter' in kwargs and kwargs.get('apply_year_filter') is True:
            relative_date_created = relative_dao._model_class.date_created.label('date_registered')
        else:
            relative_date_created = literal_column('NULL').label('date_registered')

        user_query = self.db.query(user_dao._model_class).with_entities(user_dao._model_class.userid.label("userid"), user_dao._model_class.firstname.label("firstname"), user_dao._model_class.lastname.label("lastname"), user_dao._model_class.mobile.label("mobile"),
                user_dao._model_class.email.label("email"), user_dao._model_class.birthdate.label("birthdate"), user_dao._model_class.gender.label("gender"), user_dao._model_class.is_registered.label("is_registered"), user_dao._model_class.date_registered.label("date_registered"), literal_column("'user'").label('source'), user_dao._model_class.is_deleted.label("is_deleted"))
        relative_query = self.db.query(relative_dao._model_class).with_entities(relative_dao._model_class.relativeid.label("userid"), relative_dao._model_class.firstname.label("firstname"), relative_dao._model_class.lastname.label("lastname"),
                relative_dao._model_class.mobile.label("mobile"), relative_dao._model_class.email.label("email"), relative_dao._model_class.birthdate.label("birthdate"), relative_dao._model_class.gender.label("gender"),
                literal_column('False').label('is_registered'), relative_date_created, literal_column("'relative'").label('source'), literal_column('False').label('is_deleted'))
        #user_query = filters.apply_filters(user_query, user_dao)
        #relative_query = filters.apply_filters(relative_query, relative_dao)
        final_query = user_query.union(relative_query).with_entities(text("userid"), func.max(text("firstname")), func.max(text("lastname")), func.max(text("mobile")), func.max(text("email")), func.max(text("birthdate")), func.max(text("gender")), func.bool_or(text("is_registered")), func.max(text("date_registered")), func.count(text("source")), func.bool_or(text("is_deleted"))).group_by(text("userid"))

        if 'apply_year_filter' in kwargs and kwargs.get('apply_year_filter') is True:
            final_query = final_query.filter(extract("year", text("date_registered")) == datetime.now().year)

        final_query = filters.apply_filters(final_query, self)
        try:
            return final_query.count()
        except Exception as e:
            self.db.rollback()
            raise HTTPException(400, str(e))

