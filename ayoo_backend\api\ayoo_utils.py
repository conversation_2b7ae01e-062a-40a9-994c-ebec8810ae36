import datetime
import json
import string
import random
import re
import phonenumbers
from passlib.context import <PERSON>pt<PERSON><PERSON>xt
import base64

from .dbmodels import DBUser
from .dbmodels import DBDoctor


def phone_number_parser(num):
    try:
        phone = phonenumbers.parse(num)
        # phone = phonenumbers.parse(str(num), region='IN')
        phone = phonenumbers.format_number(
            phone, phonenumbers.PhoneNumberFormat.E164)
        # logger.info('Phone = ')
        # logger.info(phone)
        return phone
    except Exception:
        return num


pwd_context = CryptContext(
    schemes=["pbkdf2_sha256"],
    default="pbkdf2_sha256",
    pbkdf2_sha256__default_rounds=3000
)


def encrypt_password(password):
    return pwd_context.encrypt(password)


def check_encrypted_password(password, hashed):
    return pwd_context.verify(password, hashed)


def db_user_from_json(db_json):
    decoded_string = base64.decodebytes(
        db_json.encode('utf-8')).decode('utf-8')
    # logger.info('db_json', db_json)
    # logger.info('decoded_string', type(decoded_string), decoded_string)
    clones = dict(json.loads(decoded_string))
    db_user = DBUser()
    db_user.__dict__.update(clones)
    db_user.birthdate = datetime.datetime.strptime(
        str(db_user.birthdate), '%Y-%m-%d')
    # logger.info(db_user.userid, db_user.firstname)
    return db_user


def db_doctor_from_json(db_json):
    decoded_string = base64.decodebytes(
        db_json.encode('utf-8')).decode('utf-8')
    # logger.info('db_json', db_json)
    # logger.info('decoded_string', type(decoded_string), decoded_string)
    clones = dict(json.loads(decoded_string))
    db_doctor = DBDoctor()
    db_doctor.__dict__.update(clones)
    db_doctor.dob = datetime.datetime.strptime(db_doctor.dob, '%Y-%m-%d')
    # logger.info(db_doctor.doctorid, db_doctor.firstname)
    return db_doctor


def encode_db_user(db_user: DBUser):
    # logger.info('dumping dict')
    db_user.birthdate = db_user.birthdate.strftime('%Y-%m-%d')
    obj = dict(filter(lambda kv: not kv[0].startswith(
        '_'), db_user.__dict__.items()))
    # logger.info(obj)
    serialized = json.dumps(obj)
    # logger.info('serialized', serialized)
    bstring = base64.encodebytes(serialized.encode('utf-8')).decode('UTF-8')
    # logger.info('bstring', type(bstring), bstring)
    return bstring


def encode_db_doctor(db_doc: DBDoctor):
    # logger.info('dumping dict')
    db_doc.dob = db_doc.dob.strftime('%Y-%m-%d')
    obj = dict(filter(lambda kv: not kv[0].startswith(
        '_'), db_doc.__dict__.items()))
    # logger.info(obj)
    serialized = json.dumps(obj)
    # logger.info('serialized', serialized)
    bstring = base64.encodebytes(serialized.encode('utf-8')).decode('UTF-8')
    # logger.info('bstring', type(bstring), bstring)
    return bstring


def get_time(with_delta_seconds=0):
    curr_time = datetime.datetime.utcnow()
    if with_delta_seconds > 0:
        return curr_time + datetime.timedelta(minutes=with_delta_seconds)
    else:
        return curr_time


def generate_password(length):
    characters = string.ascii_letters + string.digits
    password = ''.join(random.choice(characters) for _ in range(length))
    return password


def get_age_in_years(dob: str):
    present_date = datetime.datetime.now()
    patient_age = str(dob)
    birthdate = datetime.datetime.strptime(patient_age, '%Y-%m-%d')
    age = present_date.year - birthdate.year
    if (present_date.month, present_date.day) < (birthdate.month, birthdate.day):
        return age - 1
    return age


def is_blank_string(input_string: str):
    return input_string == ""


def string_data_type_validation(input_string: str):
    pattern = r'^[a-zA-Z0-9.,\-\/+$& \n]+$'
    # return bool(re.match(pattern, input_string))
    return True


def is_valid_integer(input_string):
    return input_string.isdigit()


def is_valid_length(input_string: str, allowed_length: int):
    # print(len(input_string))
    return len(input_string) <= allowed_length


def check_minor(input_dob: str):
    dob = datetime.datetime.strptime(input_dob, "%Y-%m-%d")
    current_date = datetime.datetime.now()
    age = current_date.year - dob.year - \
        ((current_date.month, current_date.day) < (dob.month, dob.day))
    return age >= 18


def check_if_past_next_day_eod(created_at: datetime.datetime):
    return datetime.datetime.now() > datetime.datetime.combine(created_at.date() + datetime.timedelta(days=2), datetime.time(0, 0, 0))


def check_if_past_x_hrs_prior(input_datetime, hrs_prior):
    return datetime.datetime.now() > input_datetime - datetime.timedelta(hours=hrs_prior)


def check_if_past_24_hrs_prior(input_datetime):
    return check_if_past_x_hrs_prior(input_datetime, 24)


def check_if_past_6_hrs_prior(input_datetime):
    return check_if_past_x_hrs_prior(input_datetime, 6)


def check_if_past_1_hr_prior(input_datetime):
    return check_if_past_x_hrs_prior(input_datetime, 1)


def calcualte_appointment_duration(duration: int):
    match duration:
        case 60:
            return 50
        case 50:
            return 60
        case 30:
            return 25
        case 25:
            return 30
        case 20:
            return 20
        case 90:
            return 90
        case 85:
            return 90

    return duration

def mask_email(email: str):
    if "@" not in email:
        raise Exception("Not a valid email !!!!")
    at_index = email.find('@')
    # Extract the name and domain parts
    name = email[:at_index]
    domain = email[at_index:]
    dot_index = domain.find('.')
    host = domain[:dot_index]
    end = domain[dot_index:]
    
    # Mask the middle part of the name
    if len(name) > 6:
        masked_name = '*' * len(name[0:-3]) + name[-3:]
    elif len(name) > 2:
        masked_name = name[0] + '*' * len(name[2:]) + name[-1]
    else:
        masked_name = name[0] + '*' * (len(name) - 1)
    return masked_name + host[:2] + '*' * len(host[2:]) + end

def mask_mobile(mobile: str):
    if len(mobile) < 3:
        raise Exception("Mobile number length too short !!!")
    return "*" * (len(mobile) - 3) + mobile[-3:]



