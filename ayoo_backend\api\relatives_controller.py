import random
import uuid
import datetime
import copy
import enum
from typing import Optional

import pyshorteners
import sqlalchemy.exc
from fastapi import HTT<PERSON>Exception
from sqlalchemy import or_, and_, desc, extract, literal_column, union_all, func, Integer, select, union
from sqlalchemy.orm import relationship, scoped_session, aliased

from .api_configs import OTP_GENERATOR, WEB_URL_PATH
from .ayoo_utils import phone_number_parser, get_age_in_years
# from ayoo_backend.api.chat_controller import ChatController
from .dbmodels import DBRelatives, DBUser, DBRelations, DBCaretakerAccessCode, PatientAyooIDCount
from .firebase_controller import FireBaseNotificationController
from .firebase_models import ConsentEvent
from .text_local_service.text_local_controller import TextLocalController
from .viewmodels import UserSignUpView, CreateUserByAdmin

from .ayoo_utils import phone_number_parser, encrypt_password, generate_password
from .aws_msg_email_generator import AWS<PERSON>mailAndMsgSender

from .api_configs import OTP_GENERATOR, URL_SHORTENER_PATH, current_env


class RelativesController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        self.otp_generator = OTP_GENERATOR

    def get_relative_by_id(self, relativeid):
        from . import dbmodels

        relative_data: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == relativeid).one_or_none()
        if relative_data:
            return relative_data
        else:
            return None

    def get_relation_detail_by_relation_id(self, relation_id):
        from . import dbmodels

        relative_data: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relation_id == relation_id).one_or_none()
        if relative_data:
            return relative_data
        else:
            return None

    def get_user_by_details(self, user_data: CreateUserByAdmin):
        from . import dbmodels

        find_docs: DBUser = self.db.query(dbmodels.DBUser).filter(
            DBUser.firstname == user_data.firstname,
            DBUser.lastname == user_data.lastname,
            DBUser.birthdate == user_data.dob,
            DBUser.email == user_data.email,
            DBUser.mobile == user_data.mobile,
            DBUser.gender == user_data.gender
        ).one_or_none()

        if find_docs is not None:
            return find_docs
        else:
            return None

    def get_relation_data_by_caretaker_and_relative(self, caretaker_id, relative_id) -> Optional[DBRelatives]:
        from ..api import dbmodels
        find_docs: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.caretaker_id == caretaker_id,
            DBRelatives.relativeid == relative_id
        )
        if find_docs.count():
            return find_docs[0]
        else:
            return None

    def get_relative_by_details(self, caretaker_id, relative_data) -> Optional[DBRelatives]:
        from . import dbmodels

        mobile = phone_number_parser(relative_data.mobile)
        relationship = ''
        dob = ''
        # dob = relative_data.dob
        # relationship = relative_data.appointment_for
        if isinstance(relative_data, DBRelatives):
            # logger.info(isinstance(relative_data, DBRelatives))
            # logger.info("it is an instance of DBRelative")
            dob = relative_data.birthdate
            relationship = relative_data.relationship
        else:
            # logger.info("it is not an instance of DBRelative")
            if isinstance(relative_data, DBRelatives) is False:
                dob = relative_data.dob
                relationship = relative_data.relation

        find_docs: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.caretaker_id == caretaker_id,
            DBRelatives.relationship == relationship,
            DBRelatives.firstname == relative_data.firstname,
            DBRelatives.lastname == relative_data.lastname,
            DBRelatives.birthdate == dob,
            DBRelatives.email == relative_data.email,
            DBRelatives.mobile == mobile,
            DBRelatives.gender == relative_data.gender
        )

        if find_docs.count():
            return find_docs
        else:
            return None

    def create_relative(self, caretaker_id, relative_data, caretaker_data, active_flag) -> Optional[DBRelatives]:

        uid = str(uuid.uuid4())
        mobile = phone_number_parser(relative_data.mobile)
        relationship = ''
        dob = ''
        # dob = relative_data.dob
        # relationship = relative_data.appointment_for
        if isinstance(relative_data, DBRelatives):
            # logger.info(isinstance(relative_data, DBRelatives))
            # logger.info("it is an instance of DBRelative")
            dob = relative_data.birthdate
            relationship = relative_data.relationship
        else:
            # logger.info("it is not an instance of DBRelative")
            if isinstance(relative_data, DBRelatives) is False:
                dob = relative_data.dob
                relationship = relative_data.relation

        dob = datetime.datetime.strptime(dob, '%Y-%m-%d')

        patient_age = get_age_in_years(str(relative_data.dob))
        if patient_age >= 18:
            consent = False
        else:
            consent = True

        relative_ayoo_id = self.generate_patient_ayoo_id()

        relative_doc = DBRelatives(
            relation_id=str(uuid.uuid4()),
            relativeid=uid,
            ayoo_id=relative_ayoo_id,
            caretaker_id=caretaker_id,
            relationship=relationship,
            firstname=relative_data.firstname.title(),
            lastname=relative_data.lastname.title(),
            birthdate=dob,
            email=relative_data.email,
            mobile=mobile,
            gender=relative_data.gender,
            is_active=active_flag,
            is_registered=False,
            consent=consent,
            reverse_consent=False,
            date_created=datetime.datetime.now()
        )

        caretaker_doc = DBRelatives(
            relation_id=str(uuid.uuid4()),
            relativeid=caretaker_data['user_id'],
            caretaker_id=uid,
            ayoo_id=caretaker_data.get('ayoo_id', ''),
            relationship=caretaker_data['relation'],
            firstname=caretaker_data['firstname'].title(),
            lastname=caretaker_data['lastname'].title(),
            birthdate=caretaker_data['dob'],
            email=caretaker_data['email'],
            mobile=caretaker_data['mobile'],
            gender=caretaker_data['gender'],
            is_active=active_flag,
            is_registered=True,
            consent=False,
            reverse_consent=False,
            date_created=datetime.datetime.now()
        )
        try:
            self.db.add(relative_doc)
            self.db.add(caretaker_doc)

            last_generated_ayoo_id_record: PatientAyooIDCount = self.db.query(PatientAyooIDCount).filter(
                PatientAyooIDCount.year == int(datetime.datetime.now().year)).one_or_none()

            if last_generated_ayoo_id_record.is_active:
                last_generated_ayoo_id_record.total_user_count = last_generated_ayoo_id_record.total_user_count + 1
                last_generated_ayoo_id_record.current_ayoo_id = relative_ayoo_id
                last_generated_ayoo_id_record.updated_at = datetime.datetime.now()
            else:
                raise Exception('Ayoo ID record edit is not allowed')

            # self.db.commit()

            relative_mongo_data_exist = self.mongo_db['UserCollection'].find_one(
                {'userid': str(relative_doc.relativeid)})
            if relative_mongo_data_exist is None:
                relative_mongo_dict = dict(userid=str(relative_doc.relativeid),
                                           new_user=True,
                                           address="",
                                           personal_doctor_name="",
                                           personal_doctor_phone="",
                                           personal_doctor_email="",
                                           emergency_contacts=[])
                self.mongo_db['UserCollection'].insert_one(relative_mongo_dict)

            self.db.commit()
            resp = self.get_relative_by_id(uid)
            s = pyshorteners.Shortener()

            link_to_register = f'{WEB_URL_PATH}memberSignup?relativeId={resp.relativeid}&firstname={resp.firstname}&lastname={resp.lastname}&email={resp.email}&mobile={resp.mobile}&caretakerId={resp.caretaker_id}&birthdate={resp.birthdate}&gender={resp.gender}'
            try:
                short_url = s.tinyurl.short(link_to_register)
            except Exception as e:
                self.db.rollback()
                print(f"TinyUrl failed {e}")
                short_url = link_to_register

            # print(link_to_register)
            # print(short_url)
            # print(resp)
            if resp:
                html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Registration</title>
</head>
<body style="font-family: Arial, sans-serif;">

    <div style="text-align: center; margin: 20px;">
        <h1>Welcome to AYOO Care</h1>
        <p>Click the button below to complete your registration:</p>

        <a href="{short_url}" style="display: inline-block; padding: 10px 20px; background-color: #d33379; color: #fff; text-decoration: none; border-radius: 5px;">Complete Registration</a>
    </div>

</body>
</html>

"""
                if patient_age >= 18:
                    #print("sending mail!!!!")
                    aws_mail_ctrl = AWSEmailAndMsgSender()
                    text_local_controller = TextLocalController()
                    aws_mail_ctrl.send_welcome_msg_to_users(welcome_message_on_mail=html_content,
                                                            welcome_message_on_mobile=f"Welcome to AYOO Care. Click here to register: {short_url}",
                                                            mobile=mobile, email=relative_data.email)
                    try:
                        text_local_controller.send_sms(template_name='WelcomeMessage', var_list=[short_url],
                                                       numbers=mobile)
                    except Exception as e:
                        print(f"SMS not sent due to {e}")
                created_data = dict(
                    relative_1=relative_doc,
                    relative_2=caretaker_doc
                )
                return created_data, 'Relative Added'
            else:
                self.db.rollback()
                raise Exception('Relative could not be added')

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding relative, with mobile {str(relative_data.mobile)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding relative, with mobile {str(relative_data.mobile)}'

    def get_all_relative_for_caretaker(self, caretaker_id: str):
        try:
            all_relative = []
            relative_resp: DBRelatives = self.db.query(DBRelatives).filter_by(caretaker_id=caretaker_id).all()
            # logger.info(relative_resp)
            if relative_resp:
                for relative in relative_resp:
                    # logger.info(relative)
                    all_relative.append(dict(relativeid=relative.relativeid,
                                             caretaker_id=relative.caretaker_id,
                                             relationship=relative.relationship,
                                             firstname=relative.firstname,
                                             lastname=relative.lastname,
                                             birthdate=relative.birthdate,
                                             email=relative.email,
                                             mobile=relative.mobile,
                                             gender=relative.gender))
                return all_relative, 'relative found'
            else:
                return None, 'relatives not found'
        except Exception as e:
            return None, f'error occurred as {str(e)} while getting relative info'

    def __get_user(self, login_or_email) -> DBUser:
        from . import dbmodels

        resp: DBUser  # = None
        if '@' in login_or_email:
            resp = self.db.query(dbmodels.DBUser).filter_by(email=login_or_email.lower(),
                                                            is_deleted=False).one_or_none()
        else:
            mobile = phone_number_parser(login_or_email)
            resp = self.db.query(dbmodels.DBUser).filter_by(mobile=mobile, is_deleted=False).one_or_none()

        return resp

    def create_db_record_for_patient_ayoo_id(self):
        try:
            current_year = int(datetime.datetime.now().year)
            last_generated_ayoo_id_record: PatientAyooIDCount = self.db.query(PatientAyooIDCount).filter(
                PatientAyooIDCount.year == current_year).one_or_none()

            if last_generated_ayoo_id_record is None:
                year_code = datetime.datetime.now().year % 100
                new_record = PatientAyooIDCount(
                    _id=str(uuid.uuid4()),
                    prefix='AID',
                    current_ayoo_id=f'AID-{year_code}00-0000',
                    year=current_year,
                    year_code=year_code,
                    total_user_count=0,
                    is_active=True,
                    remark='',
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now()
                )

                user_ids = select(DBUser.ayoo_id)
                relative_ids = select(DBRelatives.ayoo_id)

                combined_ids = union(user_ids, relative_ids).alias("combined")
                stmt = (
                    select(combined_ids.c.ayoo_id)
                    .order_by(combined_ids.c.ayoo_id.desc())
                    .limit(1)
                )
                highest_ayoo_id = self.db.execute(stmt).scalar()

                # Handle case when highest_ayoo_id is None (blank database)
                if highest_ayoo_id is not None:
                    new_record.current_ayoo_id = str(highest_ayoo_id)
                    try:
                        new_record.total_user_count = int(highest_ayoo_id.split('-')[-1])
                    except (ValueError, IndexError):
                        # If there's an issue parsing the ID, default to 0
                        new_record.total_user_count = 0
                # If highest_ayoo_id is None, we keep the default values set above

                self.db.add(new_record)
                self.db.commit()

        except Exception as e:
            self.db.rollback()
            raise Exception(str(e))

    def generate_patient_ayoo_id(self):
        try:
            current_year = int(datetime.datetime.now().year)
            last_generated_ayoo_id_record: PatientAyooIDCount = self.db.query(PatientAyooIDCount).filter(
                PatientAyooIDCount.year == current_year).one_or_none()

            current_user_count = int(last_generated_ayoo_id_record.total_user_count)

            if last_generated_ayoo_id_record is None:
                year_code = datetime.datetime.now().year % 100
                new_record = PatientAyooIDCount(
                    _id=str(uuid.uuid4()),
                    prefix='AID',
                    current_ayoo_id=f'AID-{year_code}00-0000',
                    year=current_year,
                    year_code=year_code,
                    total_user_count=0,
                    is_active=True,
                    remark='',
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now()
                )
                self.db.add(new_record)

                prev_record: PatientAyooIDCount = self.db.query(PatientAyooIDCount).filter(
                    PatientAyooIDCount.year == current_year - 1).one_or_none()

                if prev_record is not None:
                    prev_record.is_active = False
                    prev_record.updated_at = datetime.datetime.now()

                self.db.commit()

            next_user_count = current_user_count + 1
            prefix_value = next_user_count // 10000
            base36 = ""
            chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            value: int = prefix_value
            while value:
                value, remainder = divmod(value, 36)
                base36 = chars[remainder] + base36

            prefix_chars = base36.zfill(2)
            last_four_digits = next_user_count % 10000
            ayoo_id = "AID-{:02}{:02}-{:04}".format(
                datetime.datetime.now().year % 100,
                prefix_chars,
                last_four_digits
            )
            return ayoo_id
        except Exception as e:
            self.db.rollback()
            raise Exception(f'Error in getting new ayoo id: {str(e)}')

    def create_user(self, user_data: CreateUserByAdmin, existing_uuid: str = None, exisiting_ayoo_id: str = None,
                    joining_date: datetime = None, is_registered: bool = True):
        try:
            if is_registered is True:  # this is temporary, remove this, once full user migration has been performed
                tmp_user = self.__get_user(user_data.email)
                if tmp_user:
                    return None, f'user already exists with email {user_data.email}'
                tmp_user_mob = self.__get_user(user_data.mobile)
                if tmp_user_mob:
                    return None, f'user already exists with mobile {user_data.mobile}'

            uid = str(uuid.uuid4()) if existing_uuid is None else existing_uuid
            passwd = encrypt_password(generate_password(8))

            mobile = phone_number_parser(user_data.mobile)
            birthdate = datetime.datetime.strptime(user_data.dob, '%Y-%m-%d')

            if exisiting_ayoo_id is None:
                ayoo_id = self.generate_patient_ayoo_id()
                last_generated_ayoo_id_record: PatientAyooIDCount = self.db.query(PatientAyooIDCount).filter(
                    PatientAyooIDCount.year == int(datetime.datetime.now().year)).one_or_none()

                if last_generated_ayoo_id_record.is_active:
                    last_generated_ayoo_id_record.total_user_count = last_generated_ayoo_id_record.total_user_count + 1
                    last_generated_ayoo_id_record.current_ayoo_id = ayoo_id
                    last_generated_ayoo_id_record.updated_at = datetime.datetime.now()
                else:
                    raise Exception('Ayoo ID record edit is not allowed')

            else:
                ayoo_id = exisiting_ayoo_id

            db_user = DBUser(userid=uid, ayoo_id=ayoo_id, firstname=user_data.firstname.title(),
                             lastname=user_data.lastname.title(),
                             email=user_data.email, mobile=mobile, encpassword=passwd,
                             birthdate=birthdate, gender=user_data.gender, is_registered=is_registered, is_active=True,
                             date_registered=datetime.datetime.now() if is_registered is True else None,
                             is_deleted=False,
                             joining_date=datetime.datetime.now() if joining_date is None else joining_date)

            self.db.add(db_user)

            user_data_exist = self.mongo_db['UserCollection'].find_one({'userid': db_user.userid})
            if user_data_exist is None:
                emergency_contact = []
                if user_data.emergency_contact_name not in ['', None] or user_data.emergency_contact_phone not in ['',
                                                                                                                   None] or user_data.emergency_contact_relation not in [
                    '', None]:
                    emergency_contact = [{
                        'emergency_contact_id': str(uuid.uuid4()),
                        'user_id': '',
                        'full_name': user_data.emergency_contact_name,
                        'mobile': user_data.emergency_contact_phone,
                        'email': '',
                        'relation': user_data.emergency_contact_relation}]
                self.mongo_db['UserCollection'].insert_one(
                    dict(userid=db_user.userid,
                         new_user=True,
                         address=user_data.address,
                         personal_doctor_name=user_data.personal_doctor_name,
                         personal_doctor_phone=user_data.personal_doctor_phone,
                         personal_doctor_email=user_data.personal_doctor_email,
                         emergency_contacts=emergency_contact,
                         state=user_data.state,
                         pin=user_data.pin,
                         country=user_data.country,
                         marital_status=user_data.marital_status.value if user_data.marital_status is not None else None,
                         language1=user_data.language1,
                         language2=user_data.language2,
                         )
                )

            self.db.commit()

            return db_user, 'user created successfully'
        except Exception as e:
            self.db.rollback()
            return None, f'error occurred as {str(e)} while adding user by admin'

    def create_relations_and_relationships(self, caretaker: str, patient: str, relation_of_patient_with_caretaker: str,
                                           caretaker_details: dict, patient_details: dict, active_flag,
                                           caretaker_registered: bool = True,
                                           patient_registered: bool = False, other_relation_allowed: bool = False):
        if caretaker == patient:
            raise Exception("Relative and Caretaker cannot be same!!!!!")
        try:

            relation_exist = self.db.query(DBRelatives).filter(
                or_(
                    and_(DBRelatives.caretaker_id == caretaker, DBRelatives.relativeid == patient),
                    and_(DBRelatives.relativeid == patient, DBRelatives.caretaker_id == caretaker)
                )
            ).all()
            if relation_exist is None or len(relation_exist) == 0:
                relation_of_caretaker_with_patient = None
                if relation_of_patient_with_caretaker == 'Parent':
                    relation_of_caretaker_with_patient = 'Child'

                if relation_of_patient_with_caretaker == 'Spouse':
                    relation_of_caretaker_with_patient = 'Spouse'

                if relation_of_patient_with_caretaker == 'Child':
                    relation_of_caretaker_with_patient = 'Parent'

                if relation_of_patient_with_caretaker == 'Family':
                    relation_of_caretaker_with_patient = 'Family'

                if relation_of_patient_with_caretaker == 'Others':
                    relation_of_caretaker_with_patient = 'Others'

                if relation_of_caretaker_with_patient is None:
                    raise Exception('Cannot define relations')

                relative_1 = DBRelatives(relation_id=str(uuid.uuid4()),
                                         relativeid=patient,
                                         ayoo_id=patient_details.get('ayoo_id', ''),
                                         caretaker_id=caretaker,
                                         relationship=relation_of_patient_with_caretaker,
                                         firstname=patient_details['firstname'],
                                         lastname=patient_details['lastname'],
                                         email=patient_details['email'],
                                         mobile=patient_details['mobile'],
                                         birthdate=patient_details['dob'],
                                         gender=patient_details['gender'],
                                         is_active=active_flag,
                                         is_registered=patient_registered,
                                         consent=False,
                                         reverse_consent=False,
                                         date_created=datetime.datetime.now())

                relative_2 = DBRelatives(relation_id=str(uuid.uuid4()),
                                         relativeid=caretaker,
                                         ayoo_id=caretaker_details.get('ayoo_id', ''),
                                         caretaker_id=patient,
                                         relationship=relation_of_caretaker_with_patient,
                                         firstname=caretaker_details['firstname'],
                                         lastname=caretaker_details['lastname'],
                                         email=caretaker_details['email'],
                                         mobile=caretaker_details['mobile'],
                                         birthdate=caretaker_details['dob'],
                                         gender=caretaker_details['gender'],
                                         is_active=active_flag,
                                         is_registered=caretaker_registered,
                                         consent=False,
                                         reverse_consent=False,
                                         date_created=datetime.datetime.now())
                self.db.add(relative_1)
                self.db.add(relative_2)
                self.db.commit()
                return dict(
                    relative_1=relative_2,
                    relative_2=relative_1
                )
            else:
                return None

        except Exception as e:
            self.db.rollback()
            raise Exception(str(e))

    def validate_realtive(self, caretakerid, relativeid):
        from . import dbmodels

        relative_data: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == relativeid,
            DBRelatives.caretaker_id == caretakerid).one_or_none()
        if relative_data:
            return True
        else:
            return None

    def update_consent(self, relation_id: str, consent_value: bool):
        try:
            tmp_relation: DBRelatives = self.db.query(DBRelatives).filter(
                DBRelatives.relation_id == relation_id).one_or_none()
            updated_relation_data = DBRelatives(relation_id=tmp_relation.relation_id,
                                                relativeid=tmp_relation.relativeid,
                                                caretaker_id=tmp_relation.caretaker_id,
                                                relationship=tmp_relation.relationship,
                                                firstname=tmp_relation.firstname,
                                                lastname=tmp_relation.lastname,
                                                birthdate=tmp_relation.birthdate,
                                                email=tmp_relation.email,
                                                mobile=tmp_relation.mobile,
                                                gender=tmp_relation.gender,
                                                is_active=tmp_relation.is_active,
                                                is_registered=tmp_relation.is_registered,
                                                consent=consent_value,
                                                reverse_consent=tmp_relation.reverse_consent)

            tmp_relation_for_care_taker: DBRelatives = self.db.query(DBRelatives).filter(
                and_(DBRelatives.caretaker_id == tmp_relation.relativeid,
                     DBRelatives.relativeid == tmp_relation.caretaker_id)).one_or_none()
            if tmp_relation_for_care_taker is None:
                return None, 'Relation does not exist'

            try:
                tmp_relation.update(updated_relation_data)
                tmp_relation_for_care_taker.reverse_consent = consent_value
                self.db.commit()
            except sqlalchemy.exc.SQLAlchemyError as err:
                self.db.rollback()
                return None, f'{str(err)}'

            access_data: DBCaretakerAccessCode = self.db.query(DBCaretakerAccessCode).filter_by(
                relation_id=updated_relation_data.relation_id).order_by(desc(DBCaretakerAccessCode.created_at)).first()

            if access_data is None:
                return None, f'Something went wrong: Caretaker access data not found'

            data_to_return = dict(
                consent=consent_value,
                relation_id=access_data.relation_id,
                consent_duration=access_data.consent_duration
            )
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            relative_details_2: DBRelatives = updated_relation_data
            relative_details_1: DBRelatives = self.db.query(DBRelatives).filter_by(
                relativeid=relative_details_2.caretaker_id, caretaker_id=relative_details_2.relativeid).one_or_none()

            text_local_controller = TextLocalController()

            if consent_value:
                return_msg = "Caretaker Access Granted"
                consent_grant_data = ConsentEvent(
                    relation_id=relative_details_1.relation_id,
                    relative_one_id=relative_details_1.relativeid,
                    relative_one_name=relative_details_1.firstname + ' ' + relative_details_1.lastname,
                    relative_one_relation_with_relative_two=relative_details_2.relationship,
                    relative_two_id=relative_details_2.relativeid,
                    relative_two_name=relative_details_2.firstname + ' ' + relative_details_2.lastname,
                    relative_two_relation_with_relative_one=relative_details_1.relationship,
                    consent=True,
                    consent_duration=access_data.consent_duration,
                    event_date=datetime.datetime.now(),  # add or remove date
                    remarks='None')

                text_local_controller.send_sms(template_name='CaretakerAccessGrantRelativeOne',
                                               var_list=[consent_grant_data.relative_two_name,
                                                         access_data.consent_duration],
                                               numbers=relative_details_1.mobile)
                text_local_controller.send_sms(template_name='CaretakerAccessGrantRelativeTwo',
                                               var_list=[consent_grant_data.relative_one_name,
                                                         access_data.consent_duration],
                                               numbers=relative_details_2.mobile)

                frbs_ctrl.consent_granted(consent_notif_data=consent_grant_data)

            else:
                return_msg = "Caretaker Access Removed"
                consent_grant_data = ConsentEvent(
                    relation_id=relative_details_1.relation_id,
                    relative_one_id=relative_details_1.relativeid,
                    relative_one_name=relative_details_1.firstname + ' ' + relative_details_1.lastname,
                    relative_one_relation_with_relative_two=relative_details_2.relationship,
                    relative_two_id=relative_details_2.relativeid,
                    relative_two_name=relative_details_2.firstname + ' ' + relative_details_2.lastname,
                    relative_two_relation_with_relative_one=relative_details_1.relationship,
                    consent=False,
                    consent_duration=0,
                    event_date=datetime.datetime.now(),  # add or remove date
                    remarks='None'
                )

                text_local_controller.send_sms(template_name='CaretakerAccessRevokeRelativeOne',
                                               var_list=[consent_grant_data.relative_two_name],
                                               numbers=relative_details_1.mobile)
                text_local_controller.send_sms(template_name='CaretakerAccessRevokeRelativeTwo',
                                               var_list=[consent_grant_data.relative_one_name],
                                               numbers=relative_details_2.mobile)

                frbs_ctrl.consent_removed(consent_notif_data=consent_grant_data)
            return data_to_return, f'{return_msg}'
        except Exception as e:
            self.db.rollback()
            return None, f'{str(e)}'

    def update_consent_self(self, caretaker_relation_id: str, consent_value: bool):
        try:
            tmp_relation_for_caretaker: DBRelatives = self.db.query(DBRelatives).filter(
                DBRelatives.relation_id == caretaker_relation_id).one_or_none()

            tmp_relation_for_relative: DBRelatives = self.db.query(DBRelatives).filter(
                and_(DBRelatives.caretaker_id == tmp_relation_for_caretaker.relativeid,
                     DBRelatives.relativeid == tmp_relation_for_caretaker.caretaker_id)).one_or_none()

            if tmp_relation_for_relative is None:
                return None, 'Relation does not exist'

            try:
                tmp_relation_for_caretaker.consent = consent_value
                tmp_relation_for_caretaker.reverse_consent = consent_value
                tmp_relation_for_relative.consent = consent_value
                tmp_relation_for_relative.reverse_consent = consent_value
                self.db.commit()
            except sqlalchemy.exc.SQLAlchemyError as err:
                self.db.rollback()
                return None, f'{str(err)}'

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            relative_details_1: DBRelatives = tmp_relation_for_caretaker
            relative_details_2: DBRelatives = tmp_relation_for_relative
            if consent_value:
                return_msg = "Caretaker Access Granted"
            else:
                return_msg = "Caretaker Access Removed"

                consent_grant_data = ConsentEvent(
                    relation_id=relative_details_1.relation_id,
                    relative_one_id=relative_details_1.relativeid,
                    relative_one_name=relative_details_1.firstname + ' ' + relative_details_1.lastname,
                    relative_one_relation_with_relative_two=relative_details_2.relationship,
                    relative_two_id=relative_details_2.relativeid,
                    relative_two_name=relative_details_2.firstname + ' ' + relative_details_2.lastname,
                    relative_two_relation_with_relative_one=relative_details_1.relationship,
                    consent=False,
                    consent_duration=0,
                    event_date=datetime.datetime.now(),  # add or remove date
                    remarks='None')
                frbs_ctrl.consent_removed(consent_notif_data=consent_grant_data)
                text_local_controller = TextLocalController()
                text_local_controller.send_sms(template_name='CaretakerAccessRevokeRelativeOne',
                                               var_list=[consent_grant_data.relative_two_name],
                                               numbers=relative_details_1.mobile)
                text_local_controller.send_sms(template_name='CaretakerAccessRevokeRelativeTwo',
                                               var_list=[consent_grant_data.relative_one_name],
                                               numbers=relative_details_2.mobile)

            return True, f'{return_msg}'
        except Exception as e:
            self.db.rollback()
            return None, f'{str(e)}'
