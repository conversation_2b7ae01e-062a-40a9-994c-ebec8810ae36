from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse, FileResponse
from ..services.auth import get_admin_from_token
from ..DAOs.appointmentDAO import AppointmentDAO, DashBoardQueryFields, appointment_dashboard_aggregation, BaseMongoQueryFields, GetAppointmentById
from ..DTOs.appointmentDTO import AdminAppointmentListingDTO
import pandas as pd
from io import StringIO
from ..DataModels.appointment import Appointment, AppointmentStatus, AppointmentType
from ..DataModels.payment import PaymentStatus
from uuid import uuid4
from .payloads.appointment import CreateExtensionPayload, ToggleType
import datetime
from ..revenue_models import One_Time_Payment

from ..ayoo_utils import check_if_past_1_hr_prior
from ..database import get_db
from ..mongodb import mongodb_conn
from ..jitsi_meet import JitsiMeetController
from ..patient_controller import PatientController

appointments_router_dash = APIRouter(
    prefix="/appointments",
    dependencies=[Depends(get_admin_from_token)],
    tags=["appointments"]
)


@appointments_router_dash.post("/dashboard")
async def get_dashboard_data(query_params: DashBoardQueryFields):
    match_filters = query_params.apply_filters(None, AppointmentDAO())
    pipeline = appointment_dashboard_aggregation
    pipeline[0]["$match"] = match_filters
    li = AppointmentDAO().execute_pipeline(pipeline)
    return_data = {}
    for x in li:
        appointment_status = x.get("_id")
        if appointment_status == None:
            continue
        if appointment_status not in ["Completed", "No Show", "Booked"]:
            continue
        return_data[appointment_status] = {}
        for data in x.get("data"):
            if data.get("duration") not in [20, 30, 60]:
                continue
            duration = "%d" % data.get("duration")
            count = data.get("count")
            return_data[appointment_status][duration] = count
    return return_data


@appointments_router_dash.get("/download_appointment_data")
async def get_all_appointment_data():
    all_appointments = AppointmentDAO().list(0, 0)
    data = [x.dict() for x in AdminAppointmentListingDTO.create_from_appointment_list(
        all_appointments)]
    buffer = StringIO()
    pd.DataFrame(data).to_csv(buffer)
    buffer.seek(0)
    return StreamingResponse(
        iter(buffer.readlines()),
        media_type='text/csv',
        headers={
            "Content-Disposition": "attachment;filename=appointments.csv"
        }
    )


@appointments_router_dash.post("/extend")
async def extend_appointment(body: CreateExtensionPayload):

    async def get_appointment(func, ap_id):
        ap: Appointment = func(
            ap_id,
            key_name="appointment_id"
        )
        return ap

    ap_dao = AppointmentDAO()
    appointment: Appointment = await get_appointment(ap_dao.get, body.appointment_id)

    old_id = appointment.id
    del appointment.id

    amount = int(body.payment)
    payment_status = PaymentStatus.successful.value if amount == 0 else PaymentStatus.pending.value
    appointmentment_status = AppointmentStatus.booked.value if amount == 0 else AppointmentStatus.initiate.value

    new_appointment = Appointment(**dict(appointment))
    new_id = str(uuid4())
    new_appointment.appointment_id = new_id
    new_appointment.payment_status = payment_status
    new_appointment.status.status = appointmentment_status
    new_appointment.extension = True
    new_appointment.appointment_slot = appointment.end_date
    new_appointment.payment = amount
    end_time = appointment.end_date + datetime.timedelta(minutes=body.duration)
    new_appointment.end_date = end_time

    try:
        ap_dao.create(new_appointment)
    except:
        raise HTTPException(409, "error in creating extension appointment")
    ap_dao.patch(old_id, "is_extended", new_id)

    return new_id


@appointments_router_dash.post("/toggle_type/{appointment_id}")
async def toggle_type(appointment_id, reason: ToggleType):
    try:
        filter = GetAppointmentById(appointment_id=appointment_id)
        appointment: Appointment = AppointmentDAO().list(filter=filter)[0]
    except IndexError as e:
        raise HTTPException(404, "Appointment not found !!!!")
    except Exception as e:
        raise HTTPException(500, "Something went wrong !!!!")
    if appointment.status.status != "Booked":
        raise HTTPException(400, "Appointment not in 'Booked' status!!!!")
    if appointment.appointment_slot < datetime.datetime.now():
        raise HTTPException(400, "Appointment already started !!!!")
    if check_if_past_1_hr_prior(appointment.appointment_slot) and reason.reason in [None, ""]:
        detail = "Cannot change appointment type within 1 hour of appointment slot!!!!"
        raise HTTPException(400, detail)
    object_id = appointment.id
    del appointment.id
    appointment.toggle_type()
    if appointment.status.reason is None:
        appointment.status.reason = reason.reason
    else:
        appointment.status.reason += "\nAdminToggleReason: " + reason.reason
    update = AppointmentDAO().update(_id=object_id, _obj=appointment)
    if appointment.appointment_type == AppointmentType.virtual:
        jitsi_ctrl = JitsiMeetController(db=get_db(), mongo=mongodb_conn)
        if jitsi_ctrl.check_meeting_using_appointment_id(appointment_id=appointment.appointment_id) is None:
            jitsi_ctrl.create_meeting_link(
                appointment_id=appointment.appointment_id,
                case_id=appointment.caseid,
                doctorid=str(appointment.doctorid),
                patient_ids=[str(appointment.patient_id)],
                appointment_slot=appointment.appointment_slot
            )

    pc = PatientController(db=get_db(), mongo=mongodb_conn)
    notifications = pc.manage_notifications_for_booked_appointments(
        appointment_id=appointment.appointment_id,
        is_rescheduled_appointment=True,
        previous_appointment_id=appointment_id
    )
    return {"update": update, "notifications": notifications}
