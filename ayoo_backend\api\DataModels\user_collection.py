from datetime import datetime
from .basemodel import BaseMongoModel, BaseModel
from typing import Optional, List
from pydantic import Field, root_validator
from ..viewmodels import MaritalStatus, EmergencyContactDetails


class CaseObject(BaseModel):
    caseid: Optional[str]
    isOpen: Optional[str]
    notif_toggle: Optional[bool]
    prescriptions: Optional[dict]
    reports: Optional[dict]

class PrescriptionsObject(BaseModel):
    case_id: str
    date_open: datetime
    date_closed: Optional[datetime]
    is_open: bool

class UserCollection(BaseMongoModel):
    userid: str
    new_user: bool  
    address: Optional[str] = None
    state: Optional[str] = None
    pin: Optional[str] = None
    country: Optional[str] = None
    marital_status: Optional[MaritalStatus] = None
    language1: Optional[str] = None
    language2: Optional[str] = None
    personal_doctor_name: Optional[str] = None
    personal_doctor_phone: Optional[str] = None
    personal_doctor_email: Optional[str] = None
    emergency_contacts: Optional[List[EmergencyContactDetails]] = []
    count_cases: Optional[int] = 1
    medical_health_prescriptions: Optional[List[PrescriptionsObject]]
    therapist_prescriptions: Optional[List[PrescriptionsObject]]
    psychiatrist_prescriptions: Optional[List[PrescriptionsObject]]


    @root_validator
    @classmethod
    def set_count_cases(cls, values):
        count = 0
        print("root validating!!!!!")
        for t in ["medical_health_prescriptions", "therapist_prescriptions", "psychiatrist_prescriptions"]:
            if t in values and values[t] is not None:
                #print(t)
                for item in values[t]:
                    if item.is_open:
                        count += 1
        values["count_cases"] = count
        return values

