from PyPDF2 import Pdf<PERSON>ead<PERSON>, PdfWriter
from reportlab.lib.colors import Color
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from io import BytesIO
import datetime
from dateutil.parser import parse

from ayoo_backend.api.views import logger, loggers
from ayoo_backend.api.api_configs import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION_NAME, AWS_BUCKET_NAME, \
    PDF_FONT_FILE_PATH
from ayoo_backend.api.aws_s3 import AWSS3Client

from reportlab.lib.utils import ImageReader

from sqlalchemy.orm import scoped_session
import boto3
import io
import os


class PDF_Gen:
    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        self.page_number = 0  # Initialize page number
        self.date_of_prescription = datetime.datetime.now().strftime("%b %d, %Y")

    # date1 = "2023-08-01"
    # type1 = "virtual"

    def get_image_data_from_s3(self, url):
        # Extract bucket and object key from the URL
        # Assuming the URL is in the format: "https://s3.amazonaws.com/bucket_name/object_key"
        file_name = url.split("/")[-1]
        bucket = "ayoo-web-bucket"
        object_key = "doctor/signature/{}".format(file_name)

        # Initialize S3 client
        s3 = boto3.client('s3')
        # Fetch the image data
        try:
            response = s3.get_object(Bucket=bucket, Key=object_key)
            image_data = response['Body'].read()
        except:
            raise Exception("Image Data not fetched")
        return image_data

    def insert_newlines(self, input_string, interval=119):
        """
        Inserts a newline character followed by a dash ('-') after every specified interval in the input string.
        """
        output_string = ''
        for i in range(0, len(input_string), interval):
            # Slice the string to get a chunk of 'interval' characters
            chunk = input_string[i:i + interval]
            if i + interval < len(input_string):
                output_string += chunk + "\n-"
            else:
                output_string += chunk
        return output_string
    def size_word_wrap(self, c, text, x, y, width):
        wrap_width = width
        lines = []
        current_line = ""
        for word in text.split():
            # logger.info(c.stringWidth(current_line + word))
            if c.stringWidth(current_line + word) <= wrap_width:
                current_line += word + " "
            else:
                lines.append(current_line.strip())
                current_line = word + " "
        lines.append(current_line.strip())
        line = len(lines)
        in_y = (line - 1) * 11
        # c.setFont("Arial", 8)
        return in_y
    def draw_header(self, c, res):
        # Set font styles
        header_font = "Helvetica-Bold"
        header_font_size = 11
        text_font = "Helvetica"
        text_font_size = 8

        # Define header text for left section
        name = res['doctor_firstname'] + " " + res['doctor_lastname']
        if res['graduation'] != "":
            degree = res['graduation'] + ", " + res['masters']
        else:
            degree = res['masters']

        if 'fellowship' in res and res['fellowship'] != "":
            degree = degree + ', ' + res['fellowship']
            # fellowship = res['fellowship']
            # left_section_text.append(("{}".format(fellowship), text_font, text_font_size))
            # # left_section_text.insert(2, ("{}".format(fellowship), text_font, text_font_size))

        # Set position for left section text
        left_section_x = 35
        left_section_y = 750

        degree_text = degree
        wrap_width = 200  # Set width based on your layout
        degree_text_lines = []
        current_line = ""
        for word in degree_text.split():
            if c.stringWidth(current_line + word, text_font, text_font_size) <= wrap_width:
                current_line += word + " "
            else:
                degree_text_lines.append(current_line.strip())
                current_line = word + " "
        degree_text_lines.append(current_line.strip())  # Append the final line

        left_section_text = [
            ("{}".format(name), header_font, header_font_size)
            # ("{}".format(degree), text_font, text_font_size),
            # ("{}".format(res['license_no']), text_font, text_font_size)
        ]
        for line in degree_text_lines:
            left_section_text.append((line, text_font, text_font_size))

        license_no = res.get('license_no')

        if license_no not in [None, '']:
            license_no = 'Reg. No: '+ res['license_no']
            left_section_text.append(("{}".format(license_no), text_font, text_font_size))


        # Add degree lines to canvas
        # for line in lines:
        #     c.setFont(text_font, text_font_size)
        #     c.drawString(left_section_x, left_section_y, line)
        #     left_section_y -= text_font_size + 4  # Adjust vertical spacing

        global type1
        # Add left section text
        for line_text, line_font, line_font_size in left_section_text:
            c.setFont(line_font, line_font_size)
            c.drawString(left_section_x, left_section_y, line_text)
            left_section_y -= line_font_size + 4

        # Define header text for middle section
        middle_section_text = ""
        if type1 == "Virtual":
            middle_section_text = "{} Consultation".format(type1)
        middle_section_background_color = colors.lightgrey

        # Set position and size for middle section
        middle_section_x = 200
        middle_section_y = 750
        middle_section_width = 300
        middle_section_height = header_font_size + 4

        # Set background color for middle section
        c.setFillColor(middle_section_background_color)
        """c.rect(
            middle_section_x, middle_section_y - middle_section_height,
            middle_section_width, middle_section_height,
            fill=True, stroke=False
        )"""

        # Calculate the width of the text
        middle_section_text_width = c.stringWidth(middle_section_text, header_font, header_font_size)
        # global date1

        # Calculate the x-position to center-align the text
        middle_section_text_x = middle_section_x + (middle_section_width - middle_section_text_width) / 2

        # Calculate the y-position to vertically center the text within the middle section
        middle_section_text_y = middle_section_y - middle_section_height / 2 - header_font_size / 2

        # Add middle section text (center-aligned)
        c.setFont(header_font, 11)
        c.setFillColor("black")
        c.setFillColor("#d3337b")
        c.drawString(middle_section_x + 30 + 30 + 13, middle_section_y - 5, middle_section_text)

        c.setFont(text_font, 11)
        c.setFillColor("black")
        c.drawString(middle_section_x + 90, middle_section_y - 20, self.date_of_prescription)

        # Define path to the logo for the right section
        logo_path = os.path.join("ayoo_backend", "extras", "logo.png")
        # os.path.join("ayoo_backend", "extras","logo.png" )

        # Set position and size for the logo in the right section
        logo_width = 50
        logo_height = 50
        logo_x = letter[0] - logo_width - 30
        logo_y = middle_section_y - middle_section_height / 2 - logo_height / 2

        # Add the logo to the right section
        c.drawImage(logo_path, logo_x, logo_y, width=logo_width, height=logo_height)

        # Draw a line that spans the entire page with pink color
        line_y = logo_y - 20
        c.setLineWidth(0.8)  # Set the line width
        c.setStrokeColor("#d3337b")  # Set the line color
        c.line(30, line_y, letter[0] - 30, line_y)
        c.setFont("Arial", 8)
        c.setLineWidth(0.25)  # Set the line width
        c.setStrokeColor("#000000")
        return line_y

    def patient_details(self, c, x, y, res):
        # each line 25 then bottom 20
        box_y = 600
        box_x = 30
        box_width = int(letter[0]) - 60
        line = 3
        box_height_initial = y + 140
        box_height = box_height_initial - box_y
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        """c.rect(box_x, box_y+25, box_width, box_height-30, fill=True, stroke=False)
        c.setFillColor("black")"""
        c.setFont("Arial", 8)
        c.setFillColor("black")
        app = self.mongo_db['Appointments'].find_one({"$and": [
            {"patient_id": str(res['patient_id'])},
            {"caseid": str(res['case_id'])}
        ]})
        name = res['patient_details']['firstname'][0].capitalize() + res['patient_details']['firstname'][
                                                                     1:].lower() + " " + \
               res['patient_details']['lastname'][0].capitalize() + res['patient_details']['lastname'][1:].lower()
        dob_date = datetime.datetime.strptime(str(res['patient_details']['dob']), "%Y-%m-%d")
        current_date = datetime.datetime.now()
        age_timedelta = current_date - dob_date
        age_years = age_timedelta.days // 365
        age = age_years

        y += 130
        font_size = 8
        c.setFont("Helvetica-Bold", font_size)
        name_width = pdfmetrics.stringWidth(name, "Helvetica-Bold", font_size)
        c.drawString(35, y, name)

        c.setFont("Arial", font_size)
        c.drawString(35 + name_width, y, f" | {res['patient_details']['gender']} | {age} Yrs")


        # c.setFont("Helvetica-Bold", 8)
        # c.drawString(35, y, "{}".format(name))
        # c.setFont("Arial", 8)
        # c.drawString(135, y, f" | {res['patient_details']['gender']} | {age} Yrs")

        # c.drawString(135, y, "{}".format(res['patient_details']['gender']))
        # c.drawString(215, y, "{} Yrs".format(age))

        c.drawString(275, y, "AYOO ID: {}".format(res['patient_details'].get('ayoo_id','')))

        c.setFont("Arial", 8)
        # c.drawString(260, y, "{}".format(res['patient_id']))

        c.drawString(490, y, f"Case ID: {res['case_id']}")

        line_y = y - 10
        c.setLineWidth(0.8)  # Set the line width
        c.setStrokeColor("#d3337b")  # Set the line color
        c.line(30, line_y, letter[0] - 30, line_y)

        c.setLineWidth(0.25)  # Set the line width
        c.setStrokeColor("#000000")

        return line_y
        # Define header text for middle section
        """box_y=y-20
        #logger.info(box_y)
        box_height= box_height_initial-box_y
        #logger.info(box_height)
        box_x = 30
        box_width = int(letter[0])-60
        hex_color = "#f0f0f0"  
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor(colors.HexColor(hex_color))
        c.rect(box_x, box_y, box_width, box_height, fill=True)#, stroke=False)
        c.setFillColor("black")"""

    def word_wrap(self, c, text, x, y, width):
        wrap_width = width
        lines = []
        current_line = ""
        for word in text.split():
            if c.stringWidth(current_line + word) <= wrap_width:
                current_line += word + " "
            else:
                lines.append(current_line.strip())
                current_line = word + " "
        lines.append(current_line.strip())

        in_y = y
        # c.setFont("Arial", 8)
        for line in lines:
            c.drawString(x, in_y, line)
            in_y -= 8 + 3
        return in_y



    def assessment_details(self, c, x, y, res, case_type, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        # c.setFillColor(colors.HexColor(hex_color))
        # c.rect(box_x, box_y, box_width, box_height, fill=True)#, stroke=False)

        # critcal might revert to it
        y -= 20
        c.setFillColor(colors.HexColor(hex_color))

        c.setFillColor("black")
        c.setFont("Helvetica-Bold", 8)
        if case_type in ["Psychiatry", "MedicalHealth"]:
            c.drawString(35, y, "ICD Code")
            c.drawString(140, y, "Title")
            c.drawString(330, y, "Comments")
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])
            # logger.info(res)

            # logger.info(type(res))
            c.setFont("Arial", 8)
            i = 1
            for li in res:
                y -= 20
                c.setFillColor("black")
                max_in_y = 0
                in_y = self.size_word_wrap(c, "{}".format(li['icd_code']), 35, y, 140 - 35 - 15)
                max_in_y = in_y if in_y > max_in_y else max_in_y
                in_y = self.size_word_wrap(c, "{}".format(li['title']), 140, y, 330 - 140 - 15)
                max_in_y = in_y if in_y > max_in_y else max_in_y
                in_y = self.size_word_wrap(c, "{}".format(li['comments']), 330, y, letter[0] - 30 - 330)
                max_in_y = in_y if in_y > max_in_y else max_in_y

                in_y = self.word_wrap(c, "{}".format(li['icd_code']), 35, y, 140 - 35 - 15)
                in_y = self.word_wrap(c, "{}".format(li['title']), 140, y, 330 - 140 - 15)
                in_y = self.word_wrap(c, "{}".format(li['comments']), 330, y, letter[0] - 30 - 330)
                y -= max_in_y
                if y - max_in_y - 20 < 126 + 50 + 30:
                    # self.page_number += 1
                    # self.set_footer(c, dic=dic, page_number=self.page_number)
                    c.showPage()
                    y = self.draw_header(c, res=dic.get('doctor_details'))
                    x = 30
                    y = 550
                    j = 165
                    c.setFont("Arial", 8)
                    y = self.patient_details(c, x, y, res=dic)
                    y -= 20

                c.setDash([2, 1])
                c.line(35, y - 6, letter[0] - 30, y - 6)
                c.setDash([])
        else:
            c.drawString(35, y, "ICD Code")
            c.drawString(140, y, "Title")
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])
            # logger.info(res)

            # logger.info(type(res))
            c.setFont("Arial", 8)

            for li in res:
                y -= 20
                c.setFillColor("black")
                max_in_y = 0
                in_y = self.size_word_wrap(c, "{}".format(li['icd_code']), 35, y, 140 - 35 - 15)
                max_in_y = in_y if in_y > max_in_y else max_in_y
                in_y = self.size_word_wrap(c, "{}".format(li['title']), 140, y, letter[0] - 30 - 140)
                max_in_y = in_y if in_y > max_in_y else max_in_y

                in_y = self.word_wrap(c, "{}".format(li['icd_code']), 35, y, 140 - 35 - 15)
                in_y = self.word_wrap(c, "{}".format(li['title']), 140, y, letter[0] - 30 - 140)
                y -= max_in_y
                if y - max_in_y - 20 < 126 + 50 + 30:
                    # self.page_number += 1
                    # self.set_footer(c, dic=dic, page_number=self.page_number)
                    c.showPage()
                    y = self.draw_header(c, res=dic.get('doctor_details'))
                    x = 30
                    y = 550
                    j = 165
                    c.setFont("Arial", 8)
                    y = self.patient_details(c, x, y, res=dic)
                    y -= 20

                c.setDash([2, 1])
                c.line(35, y - 6, letter[0] - 30, y - 6)
                c.setDash([])
        return y - 10

    def lab_test_details(self, c, x, y, res, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 300
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11

        c.setFillColor(colors.HexColor(hex_color))
        c.setFillColor("black")
        c.setFont("Arial", 8)  # Set font to normal

        y_start = y  # Store the starting y-coordinate for pagination

        for li in res:
            y -= 20
            if y - 20 < 126 + 50 + 30:  # Check if there's enough space for the next lab test
                # Add pagination logic here
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20
                y_start = y  # Reset the starting y-coordinate for pagination on the new page

            c.setFillColor("black")
            c.drawString(35, y, "{}".format(li['test_name']))
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])

        # Return the current y-coordinate for pagination
        return y - 10

    def set_footer(self, c, dic, page_number, total_pages):
        # Define path to the logo for the footer
        # dic.get('doctor_details')['signature_img']

        if dic.get('doctor_details')['signature_img'] != '':
            image_data = self.get_image_data_from_s3(dic.get('doctor_details')['signature_img'])
            image = ImageReader(BytesIO(image_data))
            # c.drawImage(image, letter[0] - 130, 126, width=70, height=50)
            c.drawImage(image, letter[0] - 130, 146, width=70, height=50)
            name = dic.get('doctor_details')['doctor_firstname'] + " " + dic.get('doctor_details')['doctor_lastname']
            if dic.get('doctor_details')['graduation'] != "":
                degree = dic.get('doctor_details')['graduation'] + ", " + dic.get('doctor_details')['masters']
            else:
                degree = dic.get('doctor_details')['masters']

            c.setStrokeColor("#000000")
            c.setFillColor(colors.HexColor("#000000"))
            c.setFont("Helvetica-Bold", 10)

            c.drawString(letter[0] - 110 - 20, 130, "{}".format(name))
            c.setFont("Arial", 8)
            degree_text = degree
            degree_text_lines = []
            wrap_width = 130  # Set width based on your layout
            current_line = ""
            for word in degree_text.split():
                if c.stringWidth(current_line + word) <= wrap_width:
                    current_line += word + " "
                else:
                    degree_text_lines.append(current_line.strip())
                    current_line = word + " "
            degree_text_lines.append(current_line.strip())  # Append the final line

            y = 121  # Starting Y position from top
            text_font_size = 8
            line_height = text_font_size + 1  # or adjust as needed

            for line in degree_text_lines:
                c.drawString(letter[0] - 110 - 20, y, "{}".format(line))
                y -= line_height  # Move to next line below

            # c.drawString(letter[0] - 110 - 20, 121, "{}".format(degree))
            license_no = dic.get('doctor_details')['license_no']
            if license_no not in ['', None]:
                license_no = 'Reg- ' + license_no

                c.drawString(letter[0] - 110 - 20, y, "{}".format(license_no))

        logo_path = os.path.join("ayoo_backend", "extras", "footer_logo.png")

        #  Set position and size for the logo in the footer
        logo_width = 150
        logo_height = 50
        logo_x = letter[0] - logo_width - 30
        logo_y = 30

        # Add the logo to the footer
        c.drawImage(logo_path, logo_x, logo_y, width=logo_width, height=logo_height)

        # Draw a line that spans the entire page with pink color
        line_y = logo_y + logo_height + 5
        c.setLineWidth(0.8)  # Set the line width
        c.setStrokeColor("#d3337b")  # Set the line color
        c.line(30, line_y, letter[0] - 30, line_y)
        pink_color = "#d3337b"  # Pink color
        c.setFillColor(colors.HexColor(pink_color))
        c.drawString(95, line_y + 5,
                     "* In case of any adverse drug reactions, please withhold the medications and contact your care provider")

        # Draw page number in the middle
        c.setFont("Arial", 8)
        page_text = f"Page {page_number} of {total_pages}"
        page_text_width = c.stringWidth(page_text)
        page_text_x = (letter[0] - page_text_width) / 2
        page_text_y = line_y - 35  # Adjust this value to position the page number as desired

        # Set the color to light gray
        c.setFillColor(colors.HexColor("#888888"))  # Light gray color

        # Draw the page number text
        c.drawString(page_text_x, page_text_y, page_text)

        # Reset the color back to black for other drawings
        c.setFillColor("black")

        # Define the styles for the footer
        footer_style = getSampleStyleSheet()["Normal"]
        footer_style.alignment = 0  # Left align the text in the footer

        # Define the content for the address in the footer with new lines
        address_lines = [
            "7/A Nallur, 14th 'A' Cross",
            "HSR Layout, Sec 6, Bengaluru, Karnataka, 560102",
            "Tel: +91 81057 38878 , Email: <EMAIL>",
        ]

        # Calculate the height of the address block
        address_height = len(address_lines) * 15

        # Calculate the position of the address within the footer
        address_x = 30
        address_y = line_y - 20

        pink_color = "#d3337b"  # Pink color
        c.setFillColor(colors.HexColor(pink_color))

        # Draw "AYOO CARE" text
        c.setFont("Helvetica-Bold", 10)
        c.drawString(address_x, address_y, "AYOO Care")
        address_y -= 15

        # Draw each line of the address in the footer
        for line in address_lines:
            c.setFont("Helvetica", 10)
            c.drawString(address_x, address_y, line)
            address_y -= 15

        c.setLineWidth(0.25)  # Set the line width
        c.setStrokeColor("#000000")

    def general_medications_details(self, c, x, y, res, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")
        c.setFont("Arial", 8)
        y -= 20

        c.setFont("Helvetica-Bold", 8)

        # word_wrap(self, c, text, x, y, width)
        # in_y = self.size_word_wrap(c, "Start Date", 35, y, 85 - 35)
        in_y = self.word_wrap(c, "Start Date", 35, y, 50)
        in_y = self.word_wrap(c, "Medicine", 80, y, 55)
        in_y = self.word_wrap(c, "Brand", 155, y, 115)
        in_y = self.word_wrap(c, "Form / Dose", 215, y, 50)
        in_y = self.word_wrap(c, "Frequency", 290, y, 348)
        in_y = self.word_wrap(c, "Duration (Days)", 360, y, 50)
        in_y = self.word_wrap(c, "Instructions", 415, y, letter[0] - 445)
        y = in_y
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])

        c.setFont("Arial", 8)
        i = 1
        for li in res:
            med_slat = str(li['medicine_salt'])
            med_name = med_slat[0].capitalize() + med_slat[1:].lower()
            y -= 20
            c.setFillColor("black")
            i += 1
            date_object = datetime.datetime.strptime(li['start_date'], "%Y-%m-%d")
            output_string = date_object.strftime("%d-%m-%y")

            max_in_y = 0
            in_y = self.size_word_wrap(c, "{}".format(output_string), 35, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(med_name), 80, y, 60)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li['medicine_brand']), 155, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li['drug_form']), 215, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y

            if li.get('frequency', {}).get('custom', None) not in ['', None]:
                frequency = li.get('frequency', {}).get('custom', None)
            else:
                frequency = ""
                if li.get('frequency',{}).get('mor',None) not in ['', None]:
                    numeric_part = ''.join(filter(str.isdigit, li.get('frequency',{}).get('mor','')))
                    alphabetic_part = ''.join(filter(str.isalpha, li.get('frequency',{}).get('mor','')))
                    low = alphabetic_part.lower()
                    formatted_y = f"{numeric_part}{low}"
                    frequency += formatted_y + "MOR"
                else:
                    frequency += " "
                if li.get('frequency',{}).get('aft', None) not in ['', None]:
                    numeric_part = ''.join(filter(str.isdigit, li.get('frequency',{}).get('aft', '')))
                    alphabetic_part = ''.join(filter(str.isalpha, li.get('frequency',{}).get('aft', '')))
                    low = alphabetic_part.lower()
                    formatted_y = f"{numeric_part}{low}"
                    frequency += " " + formatted_y + "AFT"
                else:
                    frequency += " "
                if li.get('frequency',{}).get('eve', None) not in ['', None]:
                    numeric_part = ''.join(filter(str.isdigit, li.get('frequency',{}).get('eve', '')))
                    alphabetic_part = ''.join(filter(str.isalpha, li.get('frequency',{}).get('eve', '')))
                    low = alphabetic_part.lower()
                    formatted_y = f"{numeric_part}{low}"
                    frequency += " " + formatted_y + "EVE "
                else:
                    frequency += " "
            in_y = self.size_word_wrap(c, "{}".format(frequency), 290, y, 55)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{} days".format(li['duration_in_days']), 360, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li['instructions']), 415, y, letter[0] - 445)
            max_in_y = in_y if in_y > max_in_y else max_in_y

            if y - max_in_y - 20 < 126 + 50 + 30:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20

            in_y = self.word_wrap(c, "{}".format(output_string), 35, y, 50)
            in_y = self.word_wrap(c, "{}".format(med_name), 80, y, 60)
            in_y = self.word_wrap(c, "{}".format(li['medicine_brand']), 155, y, 50)
            in_y = self.word_wrap(c, "{}".format(li['drug_form']), 215, y, 50)
            in_y = self.word_wrap(c, "{}".format(frequency), 290, y, 55)
            in_y = self.word_wrap(c, "{} days".format(li['duration_in_days']), 360, y, 50)
            in_y = self.word_wrap(c, "{}".format(li['instructions']), 415, y, letter[0] - 445)
            y -= max_in_y
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])

        return y - 10

    def sos_medications_details(self, c, x, y, res, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#135DB2"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("#135DB2")
        c.setFont("Arial", 8)
        y -= 20

        c.setFont("Helvetica-Bold", 8)

        in_y = self.word_wrap(c, "Medicine", 35, y, 70)
        in_y = self.word_wrap(c, "Brand", 120, y, 50)
        in_y = self.word_wrap(c, "Form / Dose", 190, y, 60)
        in_y = self.word_wrap(c, "Instructions: Pharmacy", 270, y, 130)
        in_y = self.word_wrap(c, "Instructions: Patient", 415, y, letter[0] - 440)

        y = in_y
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])

        c.setFont("Arial", 8)
        i = 1
        for li in res:
            med_slat = str(li['medicine_salt'])
            med_name = med_slat[0].capitalize() + med_slat[1:].lower()
            y -= 20
            c.setFillColor("#1F75D7")
            i += 1

            max_in_y = 0
            in_y = self.size_word_wrap(c, "{}".format(med_name), 35, y, 70)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('medicine_brand', '')), 120, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('drug_form', '')), 190, y, 60)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('instructions_for_pharmacy', '')), 270, y, 130)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('instructions_for_patient', '')), 415, y, letter[0] - 440)
            max_in_y = in_y if in_y > max_in_y else max_in_y

            if y - max_in_y - 20 < 126 + 50 + 30:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20

            c.setFillColor("#1F75D7")

            in_y = self.word_wrap(c, "{}".format(med_name), 35, y, 70)
            in_y = self.word_wrap(c, "{}".format(li.get('medicine_brand', '')), 120, y, 50)
            in_y = self.word_wrap(c, "{}".format(li.get('drug_form', '')), 190, y, 60)
            in_y = self.word_wrap(c, "{}".format(li.get('instructions_for_pharmacy', '')), 270, y, 130)
            in_y = self.word_wrap(c, "{}".format(li.get('instructions_for_patient', '')), 415, y, letter[0] - 440)

            y -= max_in_y
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])

        return y - 10

    def recommendations_details_therapy(self, c, x, y, res, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")
        c.setFont("Arial", 8)
        y -= 20

        c.setFont("Helvetica-Bold", 8)

        # word_wrap(self, c, text, x, y, width)
        # in_y = self.size_word_wrap(c, "Start Date", 35, y, 85 - 35)
        in_y = self.word_wrap(c, "Date", 35, y, 50)
        in_y = self.word_wrap(c, "Comments", 90, y, 460)
        y = in_y
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])

        c.setFont("Arial", 8)
        i = 1
        for li in res:
            y -= 20
            c.setFillColor("black")
            i += 1
            created_at = li['created_at'].date()
            date_object = created_at.strftime("%d/%m/%y")

            max_in_y = 0
            in_y = self.size_word_wrap(c, "{}".format(date_object), 35, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('comments','')), 90, y, 460)
            max_in_y = in_y if in_y > max_in_y else max_in_y


            if y - max_in_y - 20 < 126 + 50 + 30:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20
                c.setFont("Arial", 8)
                hex_color = "#f0f0f0"
                c.setFillColor(colors.HexColor(hex_color))
                c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                c.setFillColor("black")
                c.drawString(35, y, "RECOMMENDATION")
                c.setDash([2, 1])

                y -= 20
                c.setFont("Helvetica-Bold", 8)

                in_y = self.word_wrap(c, "Date", 35, y, 50)
                in_y = self.word_wrap(c, "Comments", 90, y, 460)
                y = in_y
                c.setDash([2, 1])
                c.line(35, y - 6, letter[0] - 30, y - 6)
                c.setDash([])

                c.setFont("Arial", 8)
                i = 1
                y -= 20

            in_y = self.word_wrap(c, "{}".format(date_object), 35, y, 50)
            in_y = self.word_wrap(c, "{}".format(li.get('comments','')), 90, y, 460)
            y -= max_in_y
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])

        return y - 10

    def recommendations_details_medical_mental_health(self, c, x, y, res, dic):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")
        c.setFont("Arial", 8)
        y -= 20

        c.setFont("Helvetica-Bold", 8)

        # word_wrap(self, c, text, x, y, width)
        # in_y = self.size_word_wrap(c, "Start Date", 35, y, 85 - 35)
        in_y = self.word_wrap(c, "Date", 35, y, 50)
        in_y = self.word_wrap(c, "Goal", 90, y, 90)
        in_y = self.word_wrap(c, "Action/Activities", 190, y, 100)
        in_y = self.word_wrap(c, "Comments", 310, y, 270)
        y = in_y
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])

        c.setFont("Arial", 8)
        i = 1
        for li in res:
            y -= 20
            c.setFillColor("black")
            i += 1
            created_at = li['created_at'].date()
            date_object = created_at.strftime("%d/%m/%y")

            max_in_y = 0
            in_y = self.size_word_wrap(c, "{}".format(date_object), 35, y, 50)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('goal','')), 90, y, 90)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('activities','')), 190, y, 100)
            max_in_y = in_y if in_y > max_in_y else max_in_y
            in_y = self.size_word_wrap(c, "{}".format(li.get('comments','')), 310, y, 270)
            max_in_y = in_y if in_y > max_in_y else max_in_y


            if y - max_in_y - 20 < 126 + 50 + 30:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20
                c.setFont("Arial", 8)
                hex_color = "#f0f0f0"
                c.setFillColor(colors.HexColor(hex_color))
                c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                c.setFillColor("black")
                recommendation_heading = "WORK UP PLAN" if dic.get('case_type') == "MedicalHealth" else "RECOMMENDATION"
                c.drawString(35, y, recommendation_heading)
                c.setDash([2, 1])

                y -= 20
                c.setFont("Helvetica-Bold", 8)

                in_y = self.word_wrap(c, "Date", 35, y, 50)
                in_y = self.word_wrap(c, "Goal", 90, y, 90)
                in_y = self.word_wrap(c, "Action/Activities", 190, y, 100)
                in_y = self.word_wrap(c, "Comments", 310, y, 270)
                y = in_y
                c.setDash([2, 1])
                c.line(35, y - 6, letter[0] - 30, y - 6)
                c.setDash([])

                c.setFont("Arial", 8)
                i = 1
                y -= 20

            in_y = self.word_wrap(c, "{}".format(date_object), 35, y, 50)
            in_y = self.word_wrap(c, "{}".format(li.get('goal','')), 90, y, 90)
            in_y = self.word_wrap(c, "{}".format(li.get('activities','')), 190, y, 100)
            in_y = self.word_wrap(c, "{}".format(li.get('comments','')), 310, y, 270)
            y -= max_in_y
            c.setDash([2, 1])
            c.line(35, y - 6, letter[0] - 30, y - 6)
            c.setDash([])

        return y - 10

    def recommendations_details1(self, c, x, y, res):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")

        y -= 20
        c.setFillColor("black")

        c.setFont("Arial", 8)
        c.setStrokeColor("#000000")
        c.setFillColor("black")
        c.setFont("Arial", 8)

        c.setFont("Helvetica-Bold", 8)

        # Define your text and the desired width for wrapping
        text = res.get('recommendations', {}).get('comments')

        appointment_date = str(res['recommendations']['created_at'].date())
        date_obj = datetime.datetime.strptime(appointment_date, "%Y-%m-%d")
        formatted_date = date_obj.strftime("%b %d, %Y")
        c.setStrokeColor("#000000")
        c.setFont("Arial", 8)
        lines = self.insert_newlines(text)
        comment_text = "Comments" if res['case_type'] in ["Psychiatry", "MedicalHealth"] else "Recommendation"
        c.setFont("Helvetica-Bold", 8)
        c.drawString(35, y, "Date")
        c.drawString(150, y, comment_text)
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])
        c.setFont("Arial", 8)
        y -= 20
        c.drawString(35, y, "{}".format(formatted_date))
        for line in lines.split("\n"):
            if y < 126 + 50 + 30:
                # self.page_number += 1
                # self.set_footer(c, dic=res, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=res['doctor_details'])
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=res)
                y -= 20
                # y=680
            c.drawString(150, y, line)
            y -= 8 + 3
        c.setDash([2, 1])
        c.line(35, y - 6, letter[0] - 30, y - 6)
        c.setDash([])
        return y - 10

    def follow_up_details(self, c, x, y, res):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")
        c.setFont("Arial", 8)
        y -= 20
        """c.setFillColor(colors.HexColor(hex_color))
        c.rect(32, y-3, box_width, 12, fill=True, stroke=False)
        c.setFillColor("black")
        
        c.drawString(35, y, "NEXT APPOINTMENT")"""
        # c.setFont("Arial", 8)
        c.setFont("Helvetica-Bold", 8)

        next_appointment = res.get('next_appointment', '')
        if next_appointment not in ['', None, 'None']:
            next_appointment = next_appointment.strftime('%d/%m/%Y')
            c.drawString(35, y, "{}".format(next_appointment))
            y -= 13

        appointment_type = res.get('appointment_type', '')
        if appointment_type not in [None, '', 'None']:
            c.drawString(35, y, "{}".format(res.get('appointment_type', '')))
        c.setFont("Arial", 8)

        return y

    def referral_details(self, c, x, y, res):
        box_y = y
        box_x = 30
        box_width = int(letter[0]) - 57
        hex_color = "#f0f0f0"
        header_font = "Helvetica-Bold"
        header_font_size = 10
        text_font = "Helvetica"
        text_font_size = 11
        c.setFillColor("black")
        c.setFont("Arial", 8)
        y -= 20
        for elem in res:
            c.setFont("Helvetica-Bold", 8)
            c.drawString(35, y, "{}".format(elem.get('referred_to_name', '')))
            y -= 13
        return y

    def gen_pdf(self, dic, doc_id):
        client = boto3.client(
            's3',
            # aws_access_key_id=AWS_ACCESS_KEY_ID,
            # aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME
        )
        # global date1
        global type1

        appointment_date = str(dic.get('appointment_slot').date())
        date_obj = datetime.datetime.strptime(appointment_date, "%Y-%m-%d")
        # date1 = date_obj.strftime("%b %d, %Y")
        type1 = dic['appointment_type']
        # Create a canvas object

        # pdfmetrics.registerFont(TTFont('Arial', 'C:\Windows\Fonts\Arial.ttf'))
        # pdfmetrics.registerFont(TTFont('Arial', '/usr/share/fonts/truetype/msttcorefonts/Arial.ttf'))
        pdfmetrics.registerFont(TTFont('Arial', PDF_FONT_FILE_PATH))

        buffer = io.BytesIO()
        # pdf_filename = "output.pdf"
        # buffer = open(pdf_filename, "wb")

        # Create a Canvas object
        c = canvas.Canvas(buffer, pagesize=letter)
        y = self.draw_header(c, res=dic.get('doctor_details'))
        x = 30
        y = 550
        j = 165
        c.setFont("Arial", 8)
        y = self.patient_details(c, x, y, res=dic)
        if dic.get('case_type') in ["Psychiatry", "Therapy", "MedicalHealth"]:

            if dic['assessment'] != None:
                y -= 20
                c.setFont("Arial", 8)
                if dic.get('assessment'):
                    hex_color = "#f0f0f0"
                    c.setFillColor(colors.HexColor(hex_color))
                    c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                    c.setFillColor("black")
                    c.drawString(35, y, "PROVISIONAL DIAGNOSIS")
                    y = self.assessment_details(c, x, y, res=dic.get('assessment'),
                                                case_type=dic.get('case_type'), dic=dic)

        if dic.get('case_type') in ["Psychiatry", "MedicalHealth"]:
            if dic.get('medication') != None:
                y -= 20
                c.setFont("Arial", 8)
                if dic.get('medication'):

                    # Un-comment this if medication require to start from next page
                    # line = len(dic.get('medication')['meds'])
                    # height = (line + 1) * 20
                    # if y - height < 126 + 50 + 30:
                    #     self.page_number += 1
                    #     self.set_footer(c, dic=dic, page_number=self.page_number)
                    #     c.showPage()
                    #     y = self.draw_header(c, res=dic.get('doctor_details'))
                    #     x = 30
                    #     y = 550
                    #     j = 165
                    #     c.setFont("Arial", 8)
                    #     y = self.patient_details(c, x, y, res=dic)
                    #     y -= 20

                    c.setFont("Arial", 8)
                    hex_color = "#f0f0f0"
                    c.setFillColor(colors.HexColor(hex_color))
                    c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                    c.setFillColor("black")
                    c.drawString(35, y, "MEDICATION")

                    # Start a new page
                    sos_meds = [meds for meds in dic.get("medication") if meds.get('start_date') is None]
                    general_meds = [meds for meds in dic.get("medication") if meds.get('start_date') is not None]

                    sorted_data = sorted(general_meds, key=lambda x: (x.get('start_date')))

                    # sorted_data = sorted(dic.get("medication"), key=lambda x: (x.get('start_date')))
                    date_list = []
                    name_list_gen_med = []
                    output_list_gen_med = []

                    for s in sorted_data:
                        if s.get('start_date') not in date_list:
                            date_list.append(s.get('start_date'))
                    for d in date_list:
                        matching_items = [item for item in sorted_data if item.get('start_date') == d]
                        sorted_data1 = sorted(matching_items, key=lambda x: (x.get('medicine_salt')))
                        for s in sorted_data1:
                            if s.get('medicine_salt') not in name_list_gen_med:
                                name_list_gen_med.append(s.get('medicine_salt'))
                                matching_items1 = [item for item in sorted_data if
                                                   item.get('medicine_salt') == s.get('medicine_salt')]
                                for m in matching_items1:
                                    output_list_gen_med.append(m)

                    if output_list_gen_med:
                        y = self.general_medications_details(c, x, y, res=output_list_gen_med, dic=dic)

                    output_list_sos_med = sorted(sos_meds, key=lambda x: (x.get('medicine_salt')))

                    # line = len(output_list_sos_med[0])
                    # height = (line + 1) * 20
                    # if y - height < 126 + 50 + 30:
                    #     self.page_number += 1
                    #     self.set_footer(c, dic=dic, page_number=self.page_number)
                    #     c.showPage()
                    #     y = self.draw_header(c, res=dic.get('doctor_details'))
                    #     x = 30
                    #     y = 550
                    #     j = 165
                    #     c.setFont("Arial", 8)
                    #     y = self.patient_details(c, x, y, res=dic)
                    #     y -= 20
                    if output_list_sos_med:

                        c.setFont("Arial", 8)
                        hex_color = "#E5E3E4"
                        c.setFillColor(colors.HexColor(hex_color))
                        # rectangle_width = 100  # or any appropriate width less than letter[0] - 30 - 33
                        # c.rect(33, y - 3, rectangle_width, 12, fill=True, stroke=False)
                        c.rect(33, y - 25, letter[0] - 30 - 460, 12, fill=True, stroke=False)
                        # c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                        c.setFillColor("#135DB2")
                        c.drawString(35, y - 22, "TAKE AS NEEDED")
                        # c.drawString(35, y, "TAKE AS NEEDED")
                        y = self.sos_medications_details(c, x, y - 20, res=output_list_sos_med, dic=dic)

        if dic.get('recommendations') not in [None, {}, []]:
            y -= 20
            line = 2
            height = 33
            if y - height < 126:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20

            c.setFont("Arial", 8)
            hex_color = "#f0f0f0"
            c.setFillColor(colors.HexColor(hex_color))
            c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
            c.setFillColor("black")
            recommendation_heading = "WORK UP PLAN" if dic.get('case_type') == "MedicalHealth" else "RECOMMENDATION"
            c.drawString(35, y, recommendation_heading)
            if dic.get('case_type') == "Therapy":
                y = self.recommendations_details_therapy(c, x, y, res=dic.get('recommendations'), dic=dic)
            else:
                y = self.recommendations_details_medical_mental_health(c, x, y, res=dic.get('recommendations'), dic=dic)


        if dic.get('referrals') not in ['', {}, None, []]:
            y -= 20
            line = 2
            height = 33
            if y - height < 126:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20

            hex_color = "#f0f0f0"
            c.setFillColor(colors.HexColor(hex_color))
            c.rect(33, y - 3, 130, 12, fill=True, stroke=False)
            c.setFillColor("black")
            c.setFont("Arial", 8)
            c.drawString(35, y, "REFERRAL")
            y = self.referral_details(c, x, y, res=dic.get('referrals'))

        if dic.get('follow_up') not in ['', {}, None, []]:
            y -= 20
            line = 2
            height = 33
            if y - height < 126:
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20

            hex_color = "#f0f0f0"
            c.setFillColor(colors.HexColor(hex_color))
            c.rect(33, y - 3, 130, 12, fill=True, stroke=False)
            c.setFillColor("black")
            c.setFont("Arial", 8)
            c.drawString(35, y, "FOLLOW UP")
            y = self.follow_up_details(c, x, y, res=dic.get('follow_up'))
        # self.page_number += 1
        # self.set_footer(c, dic=dic, page_number=self.page_number)

        if dic.get('case_type') in ["Psychiatry", "MedicalHealth"]:

            if len(dic.get('lab_tests', [])) > 0:
                y -= 20
                c.setFont("Arial", 8)

                line = len(dic.get('lab_tests'))
                height = (line + 1) * 20
                c.showPage()
                y = self.draw_header(c, res=dic.get('doctor_details'))
                x = 30
                y = 550
                j = 165
                c.setFont("Arial", 8)
                y = self.patient_details(c, x, y, res=dic)
                y -= 20
                hex_color = "#f0f0f0"
                c.setFillColor(colors.HexColor(hex_color))
                c.rect(33, y - 3, letter[0] - 30 - 33, 12, fill=True, stroke=False)
                c.setFillColor("black")
                c.drawString(35, y, "LAB/MEDICAL TEST")
                y = self.lab_test_details(c, x, y, res=dic.get('lab_tests'), dic=dic)
                # self.page_number += 1
                # self.set_footer(c, dic=dic, page_number=self.page_number)
        c.save()
        # buffer.close()
        buffer.seek(0)

        existing_pdf = PdfReader(buffer)
        output_pdf = PdfWriter()

        total_pages = len(existing_pdf.pages)

        for page_number in range(1, int(total_pages) + 1):
            packet = BytesIO()
            can = canvas.Canvas(packet, pagesize=letter)
            self.set_footer(can, dic, page_number, total_pages)
            can.save()

            packet.seek(0)
            new_pdf = PdfReader(packet)
            page = existing_pdf.pages[page_number - 1]
            page.merge_page(new_pdf.pages[0])
            output_pdf.add_page(page)

        final_buffer = BytesIO()
        output_pdf.write(final_buffer)
        final_buffer.seek(0)

        cid = dic['case_id']
        uid = dic['patient_id']
        app_id = dic['appointment_id']
        img_key = doc_id
        object_key = f'patient/{uid}/{cid}/prescriptions/{app_id}/{img_key}'

        try:
            try:
                client.put_object(Body=final_buffer, Bucket=AWS_BUCKET_NAME, Key=object_key)
            except Exception as e:
                print('put objct in s3 error: ', str(e))
            location = client.get_bucket_location(Bucket=AWS_BUCKET_NAME)['LocationConstraint']

            url = f"https://s3-{location}.amazonaws.com/{AWS_BUCKET_NAME}/{object_key}"
            print(url)
            return dict(
                s3_object_key=object_key,
                s3_url=url
            )
        except Exception as e:
            loggers['logger4'].info("error occurred as : " + str(e))
            err = str(e)
            return err

    def add_expired_watermark(self, prescription_s3_object_key:str):
        if prescription_s3_object_key in [None, '']:
            return

        #print('prescription_s3_object_key: ', prescription_s3_object_key)

        client = boto3.client(
            's3',
            # aws_access_key_id=AWS_ACCESS_KEY_ID,
            # aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME
        )
        s3 = boto3.client('s3', region_name=AWS_REGION_NAME)

        original_pdf = s3.get_object(Bucket=AWS_BUCKET_NAME, Key=prescription_s3_object_key)['Body'].read()

        input_pdf = PdfReader(io.BytesIO(original_pdf))
        output_pdf = PdfWriter()

        watermark_buffer = io.BytesIO()
        c = canvas.Canvas(watermark_buffer, pagesize=letter)
        c.setFont("Helvetica-Bold", 100)
        c.setFillColor(Color(0.4, 0.4, 0.4, alpha=0.3))
        c.saveState()
        c.translate(350, 380)
        c.rotate(40)
        c.drawCentredString(0, 0, "EXPIRED")
        c.restoreState()
        c.save()
        watermark_buffer.seek(0)

        watermark_pdf = PdfReader(watermark_buffer)
        watermark_page = watermark_pdf.pages[0]

        for page in input_pdf.pages:
            page.merge_page(watermark_page)
            output_pdf.add_page(page)

        result_buffer = io.BytesIO()
        output_pdf.write(result_buffer)
        result_buffer.seek(0)

        expired_key = prescription_s3_object_key.replace(".pdf", "-expired.pdf")
        try:
            try:
                client.put_object(Body=result_buffer, Bucket=AWS_BUCKET_NAME, Key=expired_key)
            except Exception as e:
                print('put objct in s3 error: ', str(e))
            location = client.get_bucket_location(Bucket=AWS_BUCKET_NAME)['LocationConstraint']
            #print('location: ', location)

            url = f"https://s3-{location}.amazonaws.com/{AWS_BUCKET_NAME}/{expired_key}"
            #print('expired url: ', url)
            return dict(
                s3_object_key=expired_key,
                s3_url=url
            )
        except Exception as e:
            loggers['logger4'].info("error occurred as : " + str(e))
            err = str(e)
            return err
