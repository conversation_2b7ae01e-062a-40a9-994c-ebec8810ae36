from fastapi import APIRouter, Depends, HTTPException
from typing import Optional
from ..jwt import get_valid_admin_from_token, oauth2_scheme_admin
from ..view_controller import Admin<PERSON><PERSON>roller, UserController
from ..chat_controller import ChatController
from ..mongodb import mongodb_conn
from ..database import init_db
from ..api_configs import DATABASE_URL, OTP_GENERATOR
from .models import UserCount, UserCountReturnBody
from datetime import datetime
from ..DAOs.allPatientsDAO import AllPatientsDAO, UsersWithAppointments
from ..DAOs.appointmentDAO import AppointmentDAO, user_appointment_aggregation, admin_dashboard_past_week_appointments_by_day

router = APIRouter()

postgres_db = init_db(DATABASE_URL)
admin_ctrl = AdminController(db=postgres_db, mongo=mongodb_conn)
user_ctrl = UserController(
    db=postgres_db, otp_generator=OTP_GENERATOR, mongo=mongodb_conn)
chat_ctrl = ChatController(db=postgres_db, mongo=mongodb_conn)


@router.post('/user/count', tags=["dashboard"])
async def get_user_count(token: str = Depends(oauth2_scheme_admin), req_body: Optional[UserCount] = None) -> UserCountReturnBody:


    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    all_users_count = AllPatientsDAO().get_count()
    all_user_appointment_details = AppointmentDAO().execute_pipeline(user_appointment_aggregation)
    users_with_appointment = [x.get("_id") for x in all_user_appointment_details if x.get("count") > 0]
    user_count_with_appointment = AllPatientsDAO().get_count(filters = UsersWithAppointments(userid = users_with_appointment))
    
    #print(datetime.now())
    last_weeks_appointment_data = AppointmentDAO().execute_pipeline(admin_dashboard_past_week_appointments_by_day)
    #print(datetime.now())
    if admin_check is not None:
        all_users = user_ctrl.user_count()
        resigstered_count = user_ctrl.get_user_last_seven_days(
            req_body.start_date)
        last_week_count = admin_ctrl.get_appointments_in_range(
            req_body.start_date)

        return UserCountReturnBody(
            resigstered_count=all_users,
            users=resigstered_count.user_list,
            user_count=resigstered_count.user_count,
            new_appointment_list=last_week_count.new_appointment_list,
            repeat_appointment_list=last_week_count.repeat_appointment_list,
            appointment_count= last_weeks_appointment_data,
            users_with_appointment = user_count_with_appointment,
            all_users_count = all_users_count

        )

    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')
