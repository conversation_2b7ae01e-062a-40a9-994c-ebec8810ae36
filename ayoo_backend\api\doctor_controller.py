import random
import uuid
import datetime
from datetime import <PERSON>elta
import calendar
import copy
import enum
import time
from typing import Optional

import pymongo
from fastapi import HTTPException

import sqlalchemy.exc
import json
from sqlalchemy import or_, and_
from sqlalchemy.orm import scoped_session

from . import dbmodels
from .api_configs import OTP_GENERATOR, OTP_EXPIRY_MINUTES, OTP_MAX_RETRIES, VIRTUAL_SLOTS_FETCH_DAYS
from .ayoo_utils import phone_number_parser, encrypt_password, check_encrypted_password, \
    encode_db_doctor, get_time, db_doctor_from_json, calcualte_appointment_duration
from .dbmodels import DBBlockSlots, DBUser, DBClinic, DBDoctor, DBGuest, DBRelatives, TransactionTypes, \
    OTPStore, DBClinicAndDoctors, DBPracticeAreaMeta, DBSpecializationMeta
from .doctormodels import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DoctorProfileImageView, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Doctors<PERSON>nfo, \
    DoctorAndClinic, DoctorT<PERSON>, DoctorS<PERSON>ial<PERSON>, \
    AlliedHealthProfessional, DoctorChangePasswordView, DoctorAndClinicMapping, RequestAvailableSlots, \
    SearchDoctorsBasedOnSpecialization, UploadDoctorProfileImagesByAdmin, VirtualAppointment, VirtualAppointmentBooking, \
    RequestAppointmentList, ResponseAppointmentList, RequestDoctorsVirtualAvailableSlots, \
    ResponseDoctorsVirtualAvailableSlots, \
    SpecializationAndAbbreviation as spclnabr, UpdateDoctorSignUpView, UpdateDoctorAndClinicMapping, \
    VirtualAppointmentCopy, \
    UploadDoctorProfileImages, DoctorProfileImageView, SearchDoctorsBasedOnSpecialization, \
    UploadDoctorProfileImagesByAdmin, \
    UploadDoctorProfileImages, UploadDoctorSignatureImages, DoctorProfileImageView, VirtualAppointmentCopy, \
    ExtendEndDateOfCaseId, CloseActiveCaseId, \
    ListOfSpecialization, PatientDirectoryResponse, SearchByAreaOrSpecialization, SearchPatientsByName, \
    SearchSlotsOnADate
from .metadata_controller import MetadataController
from .text_local_service.text_local_controller import TextLocalController
from .viewmodels import UserLoginView, VerifyOtpView, City
from .patient_models import CheckDoctorAvailableSlot, CheckMappingForClinic, DeleteVirtualSlotsView, LogoutRequest
from .jitsi_meet import JitsiMeetController
from .aws_s3 import AWSS3Client
from .views import logger, loggers
from .DAOs.slotDAO import SlotDAO, SlotListingQueryFilter
from .DataModels.slots import SlotTypes
from .services.slots import modify_slot_list
from .DTOs.adminslots import BookingSlotListingDTO


class DoctorController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        self.otp_generator = OTP_GENERATOR

    def __generate_ayoo_id(self, specialization):

        mongo_collection = self.mongo_db['DoctorsInfo']

        metadata_ctrl = MetadataController(db=self.db, mongo=self.mongo)

        specialization_data = metadata_ctrl.get_specialization_by_name(specialization=specialization)
        if specialization_data is None:
            raise Exception('Specialization not found')

        doctors_count = (mongo_collection.count_documents({
            'ayoo_id_initials': specialization_data.specialization_acronym,
        }))
        specialization_prefix = specialization_data.specialization_acronym
        count_length_str = str(doctors_count + 1)
        ayooid_trailing_part = count_length_str.zfill(4)
        ayooid = specialization_prefix + ayooid_trailing_part
        return ayooid

    def __get_doctor(self, login_or_email) -> DBDoctor:
        from . import dbmodels

        resp: DBDoctor  # = None
        if '@' in login_or_email:
            resp = self.db.query(dbmodels.DBDoctor). \
                filter_by(email=login_or_email) \
                .one_or_none()
        else:
            mobile = phone_number_parser(login_or_email)
            resp = self.db.query(dbmodels.DBDoctor). \
                filter_by(mobile=mobile) \
                .one_or_none()

        return resp

    def __get_doctor_by_ayooid(self, ayooid) -> DBDoctor:
        from . import dbmodels

        resp: DBDoctor  # = None
        resp = self.db.query(dbmodels.DBDoctor).filter_by(ayooid=ayooid).one_or_none()
        return resp

    def __get_doctor_by_id(self, doctorid: str) -> Optional[DBDoctor]:
        from . import dbmodels

        resp: DBDoctor  # = None
        resp = self.db.query(dbmodels.DBDoctor).filter(
            DBDoctor.doctorid == doctorid).one_or_none()
        return resp

    def get_doctor_by_id(self, doctorid: str) -> Optional[DBDoctor]:
        from . import dbmodels
        return self.db.query(dbmodels.DBDoctor).filter(DBDoctor.doctorid == doctorid).one_or_none()

    def __get_clinics_for_doctor(self, doctorid: str, clinicid: str) -> Optional[DBClinicAndDoctors]:
        from . import dbmodels

        resp: DBClinicAndDoctors  # = None
        resp = self.db.query(dbmodels.DBClinicAndDoctors). \
            filter_by(doctorid=doctorid, clinicid=clinicid) \
            .one_or_none()
        return resp

    def get_specialization_field_of_doctor(self, specialization: str = None, doctor_id: str = None):
        try:
            if specialization is None and doctor_id is None:
                raise Exception('Either of the specialization or doctor id is required')
            if doctor_id is not None:
                doctor_data = self.mongo_db['DoctorsInfo'].find_one({'doctorid': doctor_id})
                specialization = doctor_data['specialization']

            specialization_field: DBSpecializationMeta = self.db.query(DBSpecializationMeta).filter_by(
                specialization=specialization).one_or_none()

            return specialization_field  # returns None if found None
        except Exception as e:
            return None, str(e)

    def __get_virtual_slots(self, doctorid: str):
        mongo_collection = self.mongo_db['VirtualSlots']
        return mongo_collection.find_one(dict(doctorid=doctorid))

    def __get_slots_by_mappingid(self, mappingid: str) -> Optional[DoctorAndClinic]:
        mongo_collection = self.mongo_db['DoctorAndClinic']
        return mongo_collection.find_one(dict(mappingid=mappingid))

    def __get_patient_details(self, patientid: str):
        from ayoo_backend.api import dbmodels

        patient_data = {}

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).first()
        if resp_relative:
            patient_data['firstname'] = resp_relative.firstname
            patient_data['lastname'] = resp_relative.lastname
            patient_data['dob'] = resp_relative.birthdate
            patient_data['email'] = resp_relative.email
            patient_data['mobile'] = resp_relative.mobile
            patient_data['gender'] = resp_relative.gender
            patient_data['caretaker_id'] = resp_relative.caretaker_id

            return patient_data

        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            patient_data['firstname'] = resp_user.firstname
            patient_data['lastname'] = resp_user.lastname
            patient_data['dob'] = resp_user.birthdate
            patient_data['email'] = resp_user.email
            patient_data['mobile'] = resp_user.mobile
            patient_data['gender'] = resp_user.gender
            patient_data['caretaker_id'] = ""

            return patient_data
        else:
            return None

    def __get_clinic_details(self, clinicid: str):
        from . import dbmodels

        resp: DBClinic = self.db.query(dbmodels.DBClinic).filter_by(
            clinicid=clinicid).one_or_none()
        if resp:
            return resp
        else:
            return None

    def get_clinic_by_id(self, clinicid: str):
        return self.__get_clinic_details(clinicid=clinicid)

    def signup(self, signup_doctormodel: DoctorSignUpView):
        try:
            tmp_user = self.__get_doctor(signup_doctormodel.email)
            if tmp_user:
                return None, '', f'{signup_doctormodel.email} already exists with mobile {signup_doctormodel.mobile}'

            ayooid = self.__generate_ayoo_id(
                specialization=signup_doctormodel.specialization)

            check_ayooid = self.__get_doctor_by_ayooid(signup_doctormodel.ayooid)
            if check_ayooid:
                return None, '', f'Ayooid {signup_doctormodel.ayooid} already exists with mobile {signup_doctormodel.mobile}'

            uid = str(uuid.uuid4())
            passwd = encrypt_password(signup_doctormodel.password)
            logger.info('Signup encrypted password =' + passwd)
            mobile = phone_number_parser(signup_doctormodel.mobile)
            dob = datetime.datetime.strptime(signup_doctormodel.dob, '%Y-%m-%d')

            db_doc = DBDoctor(
                doctorid=uid, firstname=signup_doctormodel.firstname,
                lastname=signup_doctormodel.lastname, dob=dob,
                email=signup_doctormodel.email, mobile=mobile,
                encrptedpassword=passwd, gender=signup_doctormodel.gender,
                ayooid=ayooid, consulting_duration_virtual=signup_doctormodel.consulting_duration_virtual,
                consulting_duration_clinic=signup_doctormodel.consulting_duration_clinic,
                consulting_fees_virtual=signup_doctormodel.consulting_fees_virtual,
                consulting_fees_clinic=signup_doctormodel.consulting_fees_clinic
            )

            try:
                self.db.add(db_doc)

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for adding doctor, with mobile {str(db_doc.mobile)}'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal Error code {err} for adding doctor, with mobile {str(db_doc.mobile)}'
            family_doctor_active = False
            if signup_doctormodel.family_doctor_active:
                family_doctor_active = signup_doctormodel.family_doctor_active
            elif signup_doctormodel.specialization == 'Physician':
                family_doctor_active = True

            # if there is no slot available for 30 minutes, don't pass it in the request body. Pass the slots only if there is some availability

            all_durations = [20, 30, 60, 85, 90]
            virtual_consultation_and_fees = []
            durations = []
            for elem in signup_doctormodel.virtual_consultation_and_fees:
                durations.append(dict(elem)['slot_duration'])
                virtual_consultation_and_fees.append(dict(elem))

            not_in_all_durations = [x for x in all_durations if x not in durations]
            for elem in not_in_all_durations:
                virtual_consultation_and_fees.append({
                    'slot_duration': elem,
                    'fees': None
                })

            clinic_consultation_and_fees = []
            durations = []
            for elem in signup_doctormodel.clinic_consultation_and_fees:
                durations.append(dict(elem)['slot_duration'])
                clinic_consultation_and_fees.append(dict(elem))

            not_in_all_durations = [x for x in all_durations if x not in durations]
            for elem in not_in_all_durations:
                clinic_consultation_and_fees.append({
                    'slot_duration': elem,
                    'fees': None
                })

            metadata_ctrl = MetadataController(db=self.db, mongo=self.mongo)

            specialization_data = metadata_ctrl.get_specialization_by_name(specialization=signup_doctormodel.specialization)
            if specialization_data is None:
                raise Exception('Specialization not found')

            doc_info = DoctorsInfo(
                doctorid=uid, profilename=signup_doctormodel.profilename,
                doctortype=signup_doctormodel.doctortype,
                specialization=signup_doctormodel.specialization,
                ayoo_id_initials=specialization_data.specialization_acronym,
                languages=signup_doctormodel.languages, graduation=signup_doctormodel.graduation,
                masters=signup_doctormodel.masters,
                degree=signup_doctormodel.degree,
                additional_qualification=signup_doctormodel.additional_qualification,
                fellowship=signup_doctormodel.fellowship, residency=signup_doctormodel.residency,
                license=signup_doctormodel.license,
                experience=signup_doctormodel.experience,
                practice_area=signup_doctormodel.practice_area,
                interest_area=signup_doctormodel.interest_area,
                consultation_symptoms=signup_doctormodel.consultation_symptoms,
                awards=signup_doctormodel.awards, homeaddress=signup_doctormodel.homeaddress,
                bio=signup_doctormodel.bio,
                working_hour_starts_at=signup_doctormodel.working_hour_starts_at,
                working_hour_ends_at=signup_doctormodel.working_hour_ends_at,
                family_doctor_active=family_doctor_active,
                is_offering_couple_therapy=signup_doctormodel.is_offering_couple_therapy,
                is_offering_family_therapy=signup_doctormodel.is_offering_family_therapy,
                virtual_consultation_and_fees=virtual_consultation_and_fees,
                clinic_consultation_and_fees=clinic_consultation_and_fees,
                display_sequence= signup_doctormodel.display_sequence
            )

            try:
                self.db.commit()
                mongo_collection = self.mongo_db['DoctorsInfo']
                mongo_collection.insert_one(dict(doc_info))

            except Exception as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal Error code {err} for adding doctor, with mobile {str(db_doc.mobile)}'

            return db_doc, ayooid, 'Doctor added'
        except Exception as e:
            self.db.rollback()
            return None, '', f'Error occurred while adding doctor as: {str(e)}'

    def login(self, login_view_model: DoctorLoginView):
        # login_or_email = str(login_view_model.mobile_or_email).strip()
        ayooid = str(login_view_model.ayooid).strip()
        password = login_view_model.password
        logger.info('login password' + password)
        resp: DBDoctor = self.__get_doctor_by_ayooid(ayooid=ayooid)
        if resp:
            get_doctor_active_status = self.is_doctor_active(doctor_id=resp.doctorid)
            if not get_doctor_active_status:
                # raise Exception('Doctor is inactive')
                return None, 'Doctor not found'
            logger.info('stored encrypted passwd' + resp.encrptedpassword)
            if check_encrypted_password(password, resp.encrptedpassword):
                resp_obj = copy.deepcopy(resp)
                resp_obj.encrptedpassword = ''
                return resp_obj, 'Login Success'
            else:
                return None, 'password mismatch'
        else:
            return None, 'ayoo id not found'

    def get_by_id(self, doctorid: str):
        try:
            from . import dbmodels

            doctor_data: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                DBDoctor.doctorid == doctorid).one_or_none()

            if doctor_data:

                mongo_collection = self.mongo_db['DoctorsInfo']
                doctor_info = mongo_collection.find_one(
                    dict(doctorid=doctor_data.doctorid))
                # logger.info(doctor_info)
                if doctor_info:
                    specialization_data: DBSpecializationMeta = self.db.query(dbmodels.DBSpecializationMeta).filter(
                        DBSpecializationMeta.specialization == doctor_info.get('specialization')).one_or_none()
                    doctor_clinic_info, msg = self.get_doctor_clinic_info(doctor_id=str(doctor_data.doctorid))

                    clinic_details = []
                    if doctor_clinic_info is not None:
                        for clinic in doctor_clinic_info:
                            clinic_details.append(clinic['clinic_info'])

                    from ayoo_backend.api.aws_s3 import AWSS3Client
                    aws_client = AWSS3Client()
                    signature = doctor_info.get('signature')

                    if signature not in [None, '']:
                        signature = aws_client.get_presigned_url(full_url=signature)

                    doctor_details = {
                        'doctorid': doctor_data.doctorid,
                        'firstname': doctor_data.firstname,
                        'lastname': doctor_data.lastname,
                        'email': doctor_data.email,
                        'mobile': doctor_data.mobile,
                        'dob': doctor_data.dob,
                        'gender': doctor_data.gender,
                        'ayooid': doctor_data.ayooid,
                        'consulting_duration_virtual': doctor_data.consulting_duration_virtual,
                        'consulting_duration_clinic': doctor_data.consulting_duration_clinic,
                        'consulting_fees_virtual': doctor_data.consulting_fees_virtual,
                        'consulting_fees_clinic': doctor_data.consulting_fees_clinic,
                        'profilename': doctor_info['profilename'],
                        'doctortype': doctor_info['doctortype'],
                        'specialization': doctor_info['specialization'],
                        'prescription_type': specialization_data.prescription_type if specialization_data is not None else None,
                        'specialization_field': specialization_data.specialization_field if specialization_data is not None else None,
                        'languages': doctor_info['languages'],
                        'graduation': doctor_info['graduation'],
                        'masters': doctor_info['masters'],
                        'additional_qualification': doctor_info['additional_qualification'],
                        'fellowship': doctor_info['fellowship'],
                        'residency': doctor_info['residency'],
                        'experience': doctor_info['experience'],
                        'practice_area': doctor_info['practice_area'],
                        'interest_area': doctor_info.get('interest_area', []),
                        'consultation_symptoms': doctor_info.get('consultation_symptoms', []),
                        'awards': doctor_info['awards'],
                        'homeaddress': doctor_info['homeaddress'],
                        'bio': doctor_info['bio'],
                        'degree': doctor_info['degree'] if 'degree' in doctor_info else '',
                        'license': doctor_info['license'] if 'license' in doctor_info else '',
                        'signature': signature,
                        'is_active': doctor_info['is_active'],
                        'is_offering_couple_therapy': doctor_info.get('is_offering_couple_therapy', False),
                        'is_offering_family_therapy': doctor_info.get('is_offering_family_therapy', False),
                        'working_hour_starts_at': doctor_info['working_hour_starts_at'],
                        'working_hour_ends_at': doctor_info['working_hour_ends_at'],
                        'family_doctor_active_status': doctor_info['family_doctor_active'],
                        'clinics_attached': clinic_details,
                        'image_id': doctor_info['image_id'] if 'image_id' in doctor_info.keys() else '',
                        'profile_image_url': doctor_info[
                            'profile_image_url'] if 'profile_image_url' in doctor_info.keys() else '',
                        'virtual_consultation_and_fees': doctor_info[
                            'virtual_consultation_and_fees'] if 'virtual_consultation_and_fees' in doctor_info.keys() else [],
                        'clinic_consultation_and_fees': doctor_info[
                            'clinic_consultation_and_fees'] if 'clinic_consultation_and_fees' in doctor_info.keys() else [],
                        'display_sequence': doctor_info.get('display_sequence',0)
                    }

                    # logger.info(doctor_details)
                    return doctor_details, 'Doctor profile'
                else:
                    return None, 'Doctor not found'
            else:
                return None, 'Doctor not found'

        except Exception as e:
            self.db.rollback()

    def update_by_id(self, doctorid: str, doctor_data: DoctorSignUpView):
        from . import dbmodels

        get_doctor: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
            DBDoctor.doctorid == doctorid).one_or_none()
        if get_doctor is None:
            return None, f'{doctorid} does not exist'

        mobile = phone_number_parser(doctor_data.mobile)
        dob = datetime.datetime.strptime(doctor_data.dob, '%Y-%m-%d')

        get_doctor.firstname = doctor_data.firstname
        get_doctor.lastname = doctor_data.lastname
        get_doctor.dob = dob
        get_doctor.email = doctor_data.email
        get_doctor.mobile = mobile
        get_doctor.gender = doctor_data.gender
        get_doctor.consulting_duration_virtual = doctor_data.consulting_duration_virtual
        get_doctor.consulting_duration_clinic = doctor_data.consulting_duration_clinic
        get_doctor.consulting_fees_virtual = doctor_data.consulting_fees_virtual
        get_doctor.consulting_fees_clinic = doctor_data.consulting_fees_clinic
        try:
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating doctor, with mobile {str(doctor_data.mobile)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating doctor, with mobile {str(doctor_data.mobile)}'

        virtual_consultation_and_fees = []
        for elem in doctor_data.virtual_consultation_and_fees:
            virtual_consultation_and_fees.append(dict(elem))

        clinic_consultation_and_fees = []
        for elem in doctor_data.clinic_consultation_and_fees:
            clinic_consultation_and_fees.append(dict(elem))

        doc_info = DoctorsInfo(doctorid=doctorid, profilename=doctor_data.profilename,
                               doctortype=doctor_data.doctortype, specialization=doctor_data.specialization,
                               languages=doctor_data.languages, graduation=doctor_data.graduation,
                               masters=doctor_data.masters,
                               additional_qualification=doctor_data.additional_qualification,
                               fellowship=doctor_data.fellowship, residency=doctor_data.residency,
                               experience=doctor_data.experience, practice_area=doctor_data.practice_area,
                               interest_area=doctor_data.interest_area,
                               consultation_symptoms=doctor_data.consultation_symptoms,
                               awards=doctor_data.awards, homeaddress=doctor_data.homeaddress, bio=doctor_data.bio,
                               license=doctor_data.license,
                               signaturee=doctor_data.signature, degree=doctor_data.degree,
                               working_hour_starts_at=doctor_data.working_hour_starts_at,
                               working_hour_ends_at=doctor_data.working_hour_ends_at,
                               is_offering_couple_therapy=doctor_data.is_offering_couple_therapy,
                               is_offering_family_therapy=doctor_data.is_offering_family_therapy,
                               virtual_consultation_and_fees=virtual_consultation_and_fees,
                               clinic_consultation_and_fees=clinic_consultation_and_fees,
                               display_sequence=doctor_data.display_sequence
                               )

        doctor_info_to_dict = dict(doc_info)
        try:
            mongo_collection = self.mongo_db['DoctorsInfo']
            mongo_collection.find_one_and_update({"doctorid": doctorid}, {
                "$set": doctor_info_to_dict
            })
            doctor_info_to_dict['firstname'] = get_doctor.firstname
            doctor_info_to_dict['lastname'] = get_doctor.lastname
            doctor_info_to_dict['dob'] = get_doctor.dob
            doctor_info_to_dict['email'] = get_doctor.email
            doctor_info_to_dict['mobile'] = get_doctor.mobile
            doctor_info_to_dict['gender'] = get_doctor.gender
            doctor_info_to_dict['ayooid'] = get_doctor.ayooid
            doctor_info_to_dict['consulting_duration_virtual'] = get_doctor.consulting_duration_virtual
            doctor_info_to_dict['consulting_duration_clinic'] = get_doctor.consulting_duration_clinic
            doctor_info_to_dict['consulting_fees_virtual'] = get_doctor.consulting_fees_virtual
            doctor_info_to_dict['consulting_fees_clinic'] = get_doctor.consulting_fees_clinic

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code mongo - {err} for updating doctor, with mobile {str(doctor_data.mobile)}'

        return doctor_info_to_dict, 'Doctor updated'

    def __handle_verified_otp(self, response: OTPStore):
        if response.transactionType == TransactionTypes.FORGOT_PASSWORD:
            db_doctor = db_doctor_from_json(response.transactionObject)
            stored_doctor: DBDoctor = self.db.query(DBDoctor).filter_by(
                doctorid=db_doctor.doctorid).one_or_none()
            stored_doctor.encrptedpassword = db_doctor.encrptedpassword
            self.db.commit()
            return db_doctor, f'Updated password for {stored_doctor.mobile}, {stored_doctor.email}, {stored_doctor.ayooid}'

        else:
            return None, 'Method not supported'

    def verify_otp(self, otp_viewmodel: VerifyOtpView):
        from . import dbmodels
        response: OTPStore = self.db.query(dbmodels.OTPStore). \
            filter_by(transactionid=otp_viewmodel.transaction_id).one_or_none()
        if response:
            try:
                if response.otpRetries < OTP_MAX_RETRIES and response.otpExpires > get_time():
                    if response.generatedOtp == otp_viewmodel.otp:
                        return self.__handle_verified_otp(response)
                    else:
                        response.otpRetries = response.otpRetries + 1
                        self.db.commit()
                        return None, 'Invalid OTP'
                elif response.otpRetries >= OTP_MAX_RETRIES:
                    return None, 'Max otp tries exceeded'
                else:
                    # logger.info(response.otpExpires + get_time())
                    return None, 'OTP Expired'

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, f'DB Error code {err} for doctor {str(otp_viewmodel.transaction_id)}'
            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, f'Internal Error code {err} for {str(otp_viewmodel.transaction_id)}'
        else:
            return None, 'No transaction found ' + str(otp_viewmodel.transaction_id)

    def __store_doctor_and_send_otp(self, db_doctor: DBDoctor, transaction_type=TransactionTypes.FORGOT_PASSWORD):
        serialized = encode_db_doctor(db_doctor)
        transaction_id = str(uuid.uuid4())
        otp = random.randint(1001, 9999)
        expiry = get_time(OTP_EXPIRY_MINUTES * 60)
        otp_obj = OTPStore(transaction_id=transaction_id, mobile=db_doctor.mobile,
                           transaction_type=transaction_type, transaction_object=serialized,
                           generated_otp=otp, otp_expires=expiry)

        try:
            self.db.add(otp_obj)
            self.db.commit()
        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for storing OTP doctor {str(db_doctor.mobile)} ' \
                         f'transactionType {str(transaction_type)}'
        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding doctor {str(db_doctor.mobile)} {str(transaction_type)}'

        text_local_controller = TextLocalController()
        text_local_controller.send_sms(template_name='ForgetPassword', var_list=[str(otp)], numbers=db_doctor.mobile)

        self.otp_generator.send_otp(transaction_id, otp, db_doctor.mobile)
        return otp_obj, 'Otp dispatched'

    def forgot_password(self, doctorlogin_viewmodel: DoctorLoginView):
        tmp_user = self.__get_doctor_by_ayooid(doctorlogin_viewmodel.ayooid)
        if not tmp_user:
            return None, f'{doctorlogin_viewmodel.ayooid} does not exist'
        encrptedpassword = encrypt_password(doctorlogin_viewmodel.password)

        # we have to do this as updating the password in the raw object causes it to create a new transcation
        new_doctor = DBDoctor(doctorid=tmp_user.doctorid, firstname=tmp_user.firstname, lastname=tmp_user.lastname,
                              email=tmp_user.email, mobile=tmp_user.mobile, encrptedpassword=encrptedpassword,
                              dob=tmp_user.dob, gender=tmp_user.gender, ayooid=tmp_user.ayooid)

        return self.__store_doctor_and_send_otp(new_doctor, transaction_type=TransactionTypes.FORGOT_PASSWORD)

    def change_password(self, doctorid: str, change_password_view: DoctorChangePasswordView):
        stored_doctor: DBDoctor = self.db.query(
            DBDoctor).filter_by(doctorid=doctorid).one_or_none()
        if stored_doctor:
            logger.info('stored encrypted passwd' + stored_doctor.encrptedpassword)
            if check_encrypted_password(change_password_view.current_password, stored_doctor.encrptedpassword):
                stored_doctor.encrptedpassword = encrypt_password(
                    change_password_view.new_password)
                resp_obj = copy.deepcopy(stored_doctor)
                resp_obj.encrptedpassword = ''
                self.db.commit()
                return resp_obj, 'password changed successfully'
            else:
                return None, 'current password mismatch'
        else:
            return None, 'doctorid not found'

    def check_valid_work_timings(self, doctor_start_time: str, doctor_end_time: str, clinic_start_time: str,
                                 clinic_end_time: str, slot_start: str = 'None', slot_end: str = 'None'):

        clinic_start_time = datetime.datetime.strptime(clinic_start_time, "%I:%M %p").time()
        clinic_end_time = datetime.datetime.strptime(clinic_end_time, "%I:%M %p").time()
        doctor_start_time = datetime.datetime.strptime(doctor_start_time, "%I:%M %p").time()
        doctor_end_time = datetime.datetime.strptime(doctor_end_time, "%I:%M %p").time()

        valid_slot = ((doctor_start_time < clinic_end_time) and (doctor_end_time > clinic_start_time) and
                      (clinic_start_time != doctor_end_time) and (clinic_end_time != doctor_start_time))

        if slot_start != 'None' and slot_end != 'None':
            slot_start = datetime.datetime.strptime(slot_start, "%I:%M %p").time()

            slot_end = datetime.datetime.strptime(slot_end, "%I:%M %p").time()
            valid_slot = (valid_slot and ((slot_start >= doctor_start_time) and (slot_start < doctor_end_time) and
                                          (slot_end <= doctor_end_time) and (slot_end > doctor_start_time) and
                                          (slot_start >= clinic_start_time) and (slot_start < clinic_end_time) and
                                          (slot_end <= clinic_end_time) and (slot_end > clinic_start_time)))

        return valid_slot

    def map_clinic_and_doctor(self, mapping_data: DoctorAndClinicMapping, doctorid=None):
        if not doctorid:
            doctorid = mapping_data.doctorid

        check_if_assigned = self.__get_clinics_for_doctor(
            doctorid, mapping_data.clinicid)
        if check_if_assigned:
            return None, f'This clinic has already been mapped with the doctor id {doctorid}.'

        mappingid = str(uuid.uuid4())

        clinic_info: DBClinic = self.db.query(dbmodels.DBClinic).filter(
            DBClinic.clinicid == mapping_data.clinicid).one_or_none()

        clinic_start_time = clinic_info.starts_at
        clinic_end_time = clinic_info.ends_at

        doctor_info = self.mongo_db['DoctorsInfo'].find_one(dict(doctorid=doctorid))
        doctor_start_time = doctor_info['working_hour_starts_at']
        doctor_end_time = doctor_info['working_hour_ends_at']

        valid_slot = self.check_valid_work_timings(doctor_start_time=doctor_start_time, doctor_end_time=doctor_end_time,
                                                   clinic_start_time=clinic_start_time, clinic_end_time=clinic_end_time)

        if (valid_slot):
            # logger.info('yes')
            mapping = DBClinicAndDoctors(
                mappingid=mappingid, clinicid=mapping_data.clinicid, doctorid=doctorid)

            try:
                self.db.add(mapping)
                self.db.commit()
            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, f'DB Error code {err} for mapping clinic and doctor'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, f'Internal Error code {err} for mapping clinic and doctor'

            sunday_slots = []
            monday_slots = []
            tuesday_slots = []
            wednesday_slots = []
            thursday_slots = []
            friday_slots = []
            saturday_slots = []

            discarded_slots = []

            availability_type = 'InClinic'

            if mapping_data.sunday:
                # logger.info(mapping_data.sunday)
                for slots in mapping_data.sunday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        sunday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Sunday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.monday:
                for slots in mapping_data.monday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        monday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Monday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.tuesday:
                for slots in mapping_data.tuesday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        tuesday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Tuesday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.wednesday:
                for slots in mapping_data.wednesday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        wednesday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Wednesday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.thursday:
                for slots in mapping_data.thursday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        thursday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Thursday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.friday:
                for slots in mapping_data.friday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        friday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Friday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))
            if mapping_data.saturday:
                for slots in mapping_data.saturday:
                    valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                    doctor_end_time=doctor_end_time,
                                                                    clinic_start_time=clinic_start_time,
                                                                    clinic_end_time=clinic_end_time,
                                                                    slot_start=slots['starts_at'],
                                                                    slot_end=slots['ends_at'])
                    if valid_day_slots:
                        saturday_slots.append(dict(
                            slotid=str(uuid.uuid4()),
                            starts_on=datetime.datetime.strptime(
                                slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                            ends_on=datetime.datetime.strptime(
                                slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                            starts_at=slots['starts_at'],
                            ends_at=slots['ends_at'],
                            availability_type=availability_type,
                            is_active=True
                        ))
                    else:
                        discarded_slots.append(dict(day='Saturday',
                                                    starts_on=datetime.datetime.strptime(
                                                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                                    ends_on=datetime.datetime.strptime(
                                                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                    starts_at=slots['starts_at'],
                                                    ends_at=slots['ends_at']))

            mapping_info = DoctorAndClinic(mappingid=mappingid, sunday=sunday_slots, monday=monday_slots,
                                           tuesday=tuesday_slots, wednesday=wednesday_slots, thursday=thursday_slots,
                                           friday=friday_slots, saturday=saturday_slots)

            try:
                mongo_collection = self.mongo_db['DoctorAndClinic']
                mongo_collection.insert_one(dict(mapping_info))

            except Exception as e:
                self.db.rollback()
                err = str(e)
                return None, f'Internal Error code {err} for mapping clinic and doctor'

            mapped_data = self.__get_slots_by_mappingid(mappingid=mappingid)

            mapped_data['doctorid'] = doctorid
            mapped_data['clinicid'] = mapping_data.clinicid
            logger.info(f'Mapped Slots: \n, {mapped_data}')
            logger.info(f'\n\n Discarded Slots: \n{discarded_slots}')
            return mapped_data, 'Doctor mapped with clinic'

        else:
            # logger.info({"clinic_start_time": clinic_start_time, "clinic_end_time": clinic_end_time,
            #        "doctor_start_time": doctor_start_time, "doctor_end_time": doctor_end_time})
            return None, f'Clinic and Doctor are having different working time.'

    def update_mapped_clinic_doctor(self, mapping_data: UpdateDoctorAndClinicMapping):
        try:
            mappingid = str(mapping_data.mappingid)
            mapping_res = self.__get_slots_by_mappingid(mappingid=mappingid)
            if not mapping_res:
                return None, 'mappingid not found'
            mapping_details: DBClinicAndDoctors = self.db.query(dbmodels.DBClinicAndDoctors).filter(
                DBClinicAndDoctors.mappingid == mappingid).one_or_none()

            clinic_info: DBClinic = self.db.query(dbmodels.DBClinic).filter(
                DBClinic.clinicid == mapping_details.clinicid).one_or_none()

            clinic_start_time = clinic_info.starts_at
            clinic_end_time = clinic_info.ends_at

            doctor_info = self.mongo_db['DoctorsInfo'].find_one(dict(doctorid=mapping_details.doctorid))
            doctor_start_time = doctor_info['working_hour_starts_at']
            doctor_end_time = doctor_info['working_hour_ends_at']

            valid_slot = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                       doctor_end_time=doctor_end_time,
                                                       clinic_start_time=clinic_start_time,
                                                       clinic_end_time=clinic_end_time)

            if valid_slot:

                sunday_slots = []
                monday_slots = []
                tuesday_slots = []
                wednesday_slots = []
                thursday_slots = []
                friday_slots = []
                saturday_slots = []

                discarded_slots = []
                availability_type = 'InClinic'

                if mapping_data.sunday:
                    logger.info(mapping_data.sunday)
                    for slots in mapping_data.sunday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            sunday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Sunday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.monday:
                    for slots in mapping_data.monday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            monday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Monday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.tuesday:
                    for slots in mapping_data.tuesday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            tuesday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Tuesday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.wednesday:
                    for slots in mapping_data.wednesday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            wednesday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Wednesday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.thursday:
                    for slots in mapping_data.thursday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            thursday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Thursday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.friday:
                    for slots in mapping_data.friday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            friday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Friday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))
                if mapping_data.saturday:
                    for slots in mapping_data.saturday:
                        valid_day_slots = self.check_valid_work_timings(doctor_start_time=doctor_start_time,
                                                                        doctor_end_time=doctor_end_time,
                                                                        clinic_start_time=clinic_start_time,
                                                                        clinic_end_time=clinic_end_time,
                                                                        slot_start=slots['starts_at'],
                                                                        slot_end=slots['ends_at'])
                        if valid_day_slots:
                            saturday_slots.append(dict(
                                slotid=str(uuid.uuid4()),
                                starts_on=datetime.datetime.strptime(
                                    slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                                ends_on=datetime.datetime.strptime(
                                    slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                starts_at=slots['starts_at'],
                                ends_at=slots['ends_at'],
                                availability_type=availability_type,
                                is_active=True
                            ))
                        else:
                            discarded_slots.append(dict(day='Saturday',
                                                        starts_on=datetime.datetime.strptime(
                                                            slots['starts_on'], '%Y-%m-%d') if slots[
                                                            'starts_on'] else '',
                                                        ends_on=datetime.datetime.strptime(
                                                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                                                        starts_at=slots['starts_at'],
                                                        ends_at=slots['ends_at']))

                mapping_info = DoctorAndClinic(mappingid=mappingid, sunday=sunday_slots, monday=monday_slots,
                                               tuesday=tuesday_slots, wednesday=wednesday_slots,
                                               thursday=thursday_slots,
                                               friday=friday_slots, saturday=saturday_slots)
                mongo_collection = self.mongo_db['DoctorAndClinic']
                mongo_collection.find_one_and_update({"mappingid": mappingid}, {
                    "$set": dict(mapping_info)
                })
                mapped_data = self.__get_slots_by_mappingid(mappingid=mappingid)

                mapped_data['doctorid'] = mapping_details.doctorid
                mapped_data['clinicid'] = mapping_details.clinicid
                return mapped_data, 'mapping updated successfully'

        except Exception as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating clinic and doctor'

    def delete_by_mappingid(self, map: CheckMappingForClinic):
        try:
            mapping_sql: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(
                mappingid=str(map.mappingid)).one_or_none()
            mapping_mongo = self.mongo_db['DoctorAndClinic'].find_one({"mappingid": str(map.mappingid)})
            if mapping_sql and mapping_mongo:
                self.db.delete(mapping_sql)
                self.db.commit()
                self.mongo_db['DoctorAndClinic'].delete_one({"mappingid": str(map.mappingid)})
                return 'mapping deleted Successfully', 'mapping found and deleted'
            else:
                return None, f'mappingid: {str(map.mappingid)} not found'
        except Exception as e:
            return None, f'Error occurred as : {str(e)} while deleting mapping for doctor and clinic'

    def create_virtual_slots(self, mapping_data: VirtualAppointment, doctorid: str):
        check_if_assigned = self.__get_virtual_slots(doctorid)
        if check_if_assigned:
            return None, f'Doctor\'s virtual appointments are already created {doctorid}.'

        sunday_slots = []
        monday_slots = []
        tuesday_slots = []
        wednesday_slots = []
        thursday_slots = []
        friday_slots = []
        saturday_slots = []

        # availability_type = 'Virtual'

        if mapping_data.sunday:
            for slots in mapping_data.sunday:
                sunday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.monday:
            for slots in mapping_data.monday:
                monday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.tuesday:
            for slots in mapping_data.tuesday:
                tuesday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.wednesday:
            for slots in mapping_data.wednesday:
                wednesday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.thursday:
            for slots in mapping_data.thursday:
                thursday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.friday:
            for slots in mapping_data.friday:
                friday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.saturday:
            for slots in mapping_data.saturday:
                saturday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )
        mapping_info = VirtualAppointmentBooking(doctorid=doctorid, sunday=sunday_slots, monday=monday_slots,
                                                 tuesday=tuesday_slots, wednesday=wednesday_slots,
                                                 thursday=thursday_slots,
                                                 friday=friday_slots, saturday=saturday_slots)

        try:
            mongo_collection = self.mongo_db['VirtualSlots']
            mongo_collection.insert_one(dict(mapping_info))

        except Exception as e:
            err = str(e)
            loggers['logger9'].error("status_code=409, error occured as : " + str(e))
            return None, f'Internal Error code {err} for virtual appointment booking'

        mapped_data = self.__get_virtual_slots(doctorid=doctorid)
        mapped_data['doctorid'] = doctorid

        return mapped_data, 'Virtual Appointment Created'

    def get_doctors_appointments(self, doctorid: str, request_data: RequestAppointmentList):
        appointments = []
        if request_data.starts_from:
            date_from = datetime.datetime.strptime(
                request_data.starts_from, '%Y-%m-%d')
        else:
            date_from = None

        if request_data.till:
            date_till = datetime.datetime.strptime(
                request_data.till, '%Y-%m-%d')
        else:
            date_till = date_from + \
                        datetime.timedelta(hours=+22, minutes=+59, seconds=+59)

        logger.info(f"/doctor/my/appointments, date_from:{date_from}, date_till:{date_till}")

        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                'doctorid': doctorid,
                '$and': [
                    {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                    {'is_active': True},
                    {'is_confirmed': True},
                    {'status.status': {"$in":['Booked', 'Completed']}},
                    {"extension": { "$ne": True }}
                ]
            }, sort=[('appointment_slot', 1)]
        )
        )
        logger.info(f"/doctor/my/appointments, appointments_list length:{len(appointments_list)}")
        if appointments_list:
            for docs in appointments_list:
                patient = self.__get_patient_details(
                    patientid=docs['patient_id'])
                if patient is not None:
                    clinic = None
                    if docs['clinicid']:
                        clinic_details = self.__get_clinic_details(
                            clinicid=docs['clinicid'])
                        if clinic_details:
                            clinic = dict(
                                clinic_id=clinic_details.clinicid,
                                clinic_name=clinic_details.name,
                                clince_mobile=clinic_details.mobile,
                                clinic_starts_at=clinic_details.starts_at,
                                clinic_ends_at=clinic_details.ends_at,
                                clinic_address=clinic_details.address
                            )
                    # Appending jitsi meeting link info in the response
                    meeting_link = ''
                    meeting_code = None
                    if docs['appointment_type'] == 'Virtual':
                        jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                        meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                            appointment_id=str(docs['appointment_id']))
                        meeting_link = meeting_info[
                            'doctor_joining_link'] if meeting_info is not None and 'doctor_joining_link' in meeting_info else None
                        meeting_code = meeting_info[
                            'doctor_meeting_code'] if meeting_info is not None and 'doctor_meeting_code' in meeting_info else None

                    slot_duration = int((docs['end_date'] - docs['appointment_slot']).total_seconds() / 60)
                    specialization_data = self.get_specialization_field_of_doctor(specialization=None, doctor_id=doctorid)

                    appointments.append(
                        ResponseAppointmentList(
                            appointment_id=docs['appointment_id'],
                            caseid=docs['caseid'],
                            symptoms=docs['symptoms'],
                            symptoms_audio_clip=docs['symptoms_audio_clip'],
                            additional_notes=docs.get('additional_notes',''),
                            appointment_slot=docs['appointment_slot'],
                            slot_duration=slot_duration,
                            end_date=docs['end_date'],
                            appointment_type=docs['appointment_type'],
                            appointment_for=docs['appointment_for'],
                            patient=patient,
                            clinic=clinic,
                            meeting_link=meeting_link,
                            meeting_code=meeting_code,
                            doctor_specialization=specialization_data.specialization if specialization_data is not None else None,
                            doctor_specialization_field=specialization_data.specialization_field if specialization_data is not None else None
                        )
                    )
        if len(appointments):
            resp = appointments
            return resp, ''
        else:
            return "No appointments", "No appointments found"

    def get_doctors_list_for_slots_availability(self, request_data: RequestDoctorsVirtualAvailableSlots):
        try:
            if request_data.availability_type == 'Virtual' and request_data.clinic_id != '':
                raise Exception('Clinic does not have virtual availability type for doctors')

            all_specialization_fields = [row[0] for row in
                                         self.db.query(DBSpecializationMeta.specialization_field).distinct().all()]


            mongo_collection_doctors_info = self.mongo_db['DoctorsInfo']
            if request_data.specialization and request_data.specialization not in ['', 'All']:
                search_words = [request_data.specialization.title()]


                if request_data.specialization in all_specialization_fields:
                    search_words = [row[0] for row in self.db.query(DBSpecializationMeta.specialization).filter(
                        DBSpecializationMeta.specialization_field == request_data.specialization.title()).all()]


                doctors_list = list(mongo_collection_doctors_info.find(
                    {"$and": [{"$or": [
                        {"specialization": {'$in': search_words}},
                        {"practice_area": {'$in': search_words}}
                    ]}, {"is_active": True}]}
                )
                )
                # print("Original: ", doctors_list)
            else:
                doctors_list = list(mongo_collection_doctors_info.find({"is_active": True}))
                # print("Fallback: ", doctors_list)

            if request_data.clinic_id and request_data.clinic_id != '':
                # filter doctors
                mapped_doctors_with_clinic: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(
                    clinicid=str(request_data.clinic_id)).all()
                mapped_doctors_list = []
                for doctor in mapped_doctors_with_clinic:
                    if request_data.specialization and request_data.specialization not in ['', 'All']:

                        search_words = [request_data.specialization.title()]

                        if request_data.specialization in all_specialization_fields:
                            search_words = [row[0] for row in self.db.query(DBSpecializationMeta.specialization).filter(
                                DBSpecializationMeta.specialization_field == request_data.specialization.title()).all()]

                        doc_data = mongo_collection_doctors_info.find_one(
                            {"$and": [
                                {"doctorid": doctor.doctorid}, {"is_active": True},
                                {"$or": [
                                    {"specialization": {'$in': search_words}},
                                    {"practice_area": {'$in': search_words}}
                                ]}
                            ]}
                        )
                        if doc_data is not None:
                            mapped_doctors_list.append(doc_data)
                    else:
                        doc_data = mongo_collection_doctors_info.find_one(
                            {"$and": [
                                {"doctorid": doctor.doctorid}, {"is_active": True}
                            ]}
                        )
                        if doc_data is not None:
                            mapped_doctors_list.append(doc_data)
                doctors_list = mapped_doctors_list

            return doctors_list
        except Exception as e:
            raise Exception(str(e))

    def remove_booked_slots(self, available_slots, available_time_slots, booked_slots, slot_duration):
        format = '%I:%M %p'
        appointments_list = sorted(booked_slots, key=lambda x: x['appointment_slot'])
        if len(available_slots) > 0:
            slot_end_time = available_time_slots[-1] + datetime.timedelta(minutes=slot_duration)
            for booking in appointments_list:
                booking_start_time = booking['appointment_slot']
                booking_end_time = booking['end_date']

                for slot in available_time_slots:
                    av_slot_start = slot
                    av_slot_end = av_slot_start + datetime.timedelta(minutes=slot_duration)

                    if av_slot_end > slot_end_time:
                        if av_slot_start.strftime(format) in available_slots:
                            available_slots.remove(av_slot_start.strftime(format))
                            continue

                    if av_slot_start < booking_start_time:

                        if av_slot_end <= booking_start_time:
                            ...
                        else:
                            if av_slot_start.strftime(format) in available_slots:
                                available_slots.remove(av_slot_start.strftime(format))

                    elif av_slot_start == booking_start_time:
                        if av_slot_start.strftime(format) in available_slots:
                            available_slots.remove(av_slot_start.strftime(format))

                        if av_slot_end <= booking_start_time:
                            if av_slot_end.strftime(format) in available_slots:
                                available_slots.remove(av_slot_end.strftime(format))
                        else:
                            ...
                    else:
                        if av_slot_start >= booking_end_time:
                            ...
                        else:
                            if av_slot_start.strftime(format) in available_slots:
                                available_slots.remove(av_slot_start.strftime(format))

        return available_slots

    def remove_past_slots_for_today(self, search_date: str, slots_available: list):
        available_slots_same_date = []
        if datetime.datetime.strptime(str(search_date), '%Y-%m-%d') == datetime.datetime.strptime(
                str(datetime.date.today()), '%Y-%m-%d'):
            now = datetime.datetime.now()
            current_time = now.strftime("%I:%M %p")

            for av_slot in slots_available:
                if datetime.datetime.strptime(str(av_slot), '%I:%M %p') <= \
                        datetime.datetime.strptime(str(current_time), '%I:%M %p'):
                    continue
                else:
                    available_slots_same_date.append(str(av_slot))

            if len(available_slots_same_date):
                return available_slots_same_date
            else:
                return []

        else:
            return slots_available

    def get_all_available_consultation_durations_for_a_doctor(self, doctor_id: str, availability_type: str):
        doctor_info, msg = self.get_by_id(doctorid=doctor_id)
        if availability_type == "Virtual":
            key_name = 'virtual'
        elif availability_type == "InClinic":
            key_name = 'clinic'
        else:
            return []

        all_slots = doctor_info[f'{key_name}_consultation_and_fees']
        if len(all_slots) == 0:
            return []

        return all_slots

    def get_virtual_slots_availability(self, request_data: RequestDoctorsVirtualAvailableSlots, user_id: str = None,
                                       list_of_doctors: list = None):
        if request_data.search_date is None or request_data.search_date == '':
            return None, 'Enter search date'

        now = datetime.date.today()
        search_dt = str(request_data.search_date)
        if datetime.datetime.strptime(str(search_dt), '%Y-%m-%d') < datetime.datetime.strptime(str(now), '%Y-%m-%d'):
            return None, f'old date'

        try:
            weekday_number = datetime.datetime.strptime(request_data.search_date, '%Y-%m-%d').weekday()
            weekday = calendar.day_name[weekday_number]
            weekday = weekday.lower()
        except ValueError:
            return None, 'Date needs to be in YYYY-mm-dd format'

        date_from = datetime.datetime.strptime(request_data.search_date, '%Y-%m-%d')
        date_till = date_from + datetime.timedelta(hours=+22, minutes=+59, seconds=+59)

        return tuple(
                BookingSlotListingDTO.do(search_date = date_from, specialization = request_data.specialization, availability_type = request_data.availability_type, clinic_id = request_data.clinic_id)
        )

    def get_all_doctors(self):
        try:
            all_doctor_resp = self.db.query(DBDoctor).all()
            all_doctor_list = []
            for val in all_doctor_resp:
                doctor_resp = self.mongo_db['DoctorsInfo'].find_one(dict(doctorid=str(val.doctorid)))
                if doctor_resp:
                    active_status = doctor_resp[
                        'is_active'] if 'is_active' in doctor_resp.keys() else "active status not set"
                    image_exist, msg = self.get_doctor_profile_image(doctor_id=val.doctorid)
                    image_url = image_exist['profile_image_url'] if image_exist else None
                    all_doctor_list.append({'doctorid': val.doctorid,
                                            'firstname': val.firstname,
                                            'lastname': val.lastname,
                                            'email': val.email,
                                            'mobile': val.mobile,
                                            'dob': val.dob,
                                            'gender': val.gender,
                                            'ayooid': val.ayooid,
                                            'consulting_duration_virtual': val.consulting_duration_virtual,
                                            'consulting_duration_clinic': val.consulting_duration_clinic,
                                            'consulting_fees_virtual': val.consulting_fees_virtual,
                                            'consulting_fees_clinic': val.consulting_fees_clinic,
                                            'profilename': doctor_resp['profilename'],
                                            'doctortype': doctor_resp['doctortype'],
                                            'specialization': doctor_resp['specialization'],
                                            'languages': doctor_resp['languages'],
                                            'graduation': doctor_resp['graduation'],
                                            'masters': doctor_resp['masters'],
                                            'additional_qualification': doctor_resp['additional_qualification'],
                                            'fellowship': doctor_resp['fellowship'],
                                            'residency': doctor_resp['residency'],
                                            'experience': doctor_resp['experience'],
                                            'practice_area': doctor_resp['practice_area'],
                                            'interest_area': doctor_resp.get('interest_area', []),
                                            'consultation_symptoms': doctor_resp.get('consultation_symptoms', []),
                                            'awards': doctor_resp['awards'],
                                            'homeaddress': doctor_resp['homeaddress'],
                                            'bio': doctor_resp['bio'],
                                            'degree': doctor_resp['degree'],
                                            'signature': doctor_resp['signature'] if 'signature' in doctor_resp else '',
                                            'license': doctor_resp['license'],
                                            'is_active': active_status,
                                            'profile_image_url': image_url,
                                            'family_doctor_active': doctor_resp['family_doctor_active'],
                                            'is_offering_couple_therapy': doctor_resp.get('is_offering_couple_therapy', False),
                                            'is_offering_family_therapy': doctor_resp.get('is_offering_family_therapy', False),
                                            'virtual_consultation_and_fees': doctor_resp[
                                                'virtual_consultation_and_fees'] if 'virtual_consultation_and_fees' in doctor_resp else [],
                                            'clinic_consultation_and_fees': doctor_resp[
                                                'clinic_consultation_and_fees'] if 'clinic_consultation_and_fees' in doctor_resp else []
                                            })

            return all_doctor_list, "Returned All Doctor"

        except Exception as e:
            return None, f'error occurred as {str(e)} while getting all Doctors'

    def update_by_id_admin(self, doctorid: str, doctor_data: UpdateDoctorSignUpView):
        from . import dbmodels

        get_doctor: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
            DBDoctor.doctorid == doctorid).one_or_none()
        if get_doctor is None:
            return None, f'{doctorid} does not exist'

        mobile = phone_number_parser(doctor_data.mobile) if doctor_data.mobile else get_doctor.mobile
        dob = datetime.datetime.strptime(doctor_data.dob, '%Y-%m-%d') if doctor_data.dob else get_doctor.dob
        new_password = encrypt_password(
            str(doctor_data.password)) if doctor_data.password else get_doctor.encrptedpassword

        doc_db = DBDoctor(firstname=doctor_data.firstname, lastname=doctor_data.lastname, dob=dob,
                          email=doctor_data.email, mobile=mobile, encrptedpassword=new_password,
                          gender=doctor_data.gender,
                          consulting_duration_virtual=doctor_data.consulting_duration_virtual,
                          consulting_duration_clinic=doctor_data.consulting_duration_clinic,
                          consulting_fees_virtual=doctor_data.consulting_fees_virtual,
                          consulting_fees_clinic=doctor_data.consulting_fees_clinic)
        try:
            get_doctor.update(doc_db)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating doctor, with doctorid {str(doctorid)} by admin'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating doctor, doctorid {str(doctorid)} by admin'
        try:
            mongo_collection = self.mongo_db['DoctorsInfo']
            existing_doctor = mongo_collection.find_one(dict(doctorid=doctorid))

            # if there is no slot available for 30 minutes, don't pass it in the request body. Pass the slots only if there is some availability

            all_durations = [20, 30, 60, 85, 90]
            virtual_consultation_and_fees = []
            durations = []
            for elem in doctor_data.virtual_consultation_and_fees:
                durations.append(dict(elem)['slot_duration'])
                virtual_consultation_and_fees.append(dict(elem))

            not_in_all_durations = [x for x in all_durations if x not in durations]
            for elem in not_in_all_durations:
                virtual_consultation_and_fees.append({
                    'slot_duration': elem,
                    'fees': None
                })
            virtual_consultation_and_fees.sort(key=lambda x: (x['fees'] is None, x['fees']))

            clinic_consultation_and_fees = []
            durations = []
            for elem in doctor_data.clinic_consultation_and_fees:
                durations.append(dict(elem)['slot_duration'])
                clinic_consultation_and_fees.append(dict(elem))

            not_in_all_durations = [x for x in all_durations if x not in durations]
            for elem in not_in_all_durations:
                clinic_consultation_and_fees.append({
                    'slot_duration': elem,
                    'fees': None
                })
            clinic_consultation_and_fees.sort(key=lambda x: (x['fees'] is None, x['fees']))
            
            consulting_duration_clinic = clinic_consultation_and_fees[0]['slot_duration']
            
            consulting_duration_virtual = virtual_consultation_and_fees[0]['slot_duration']
          
            consulting_fees_clinic = clinic_consultation_and_fees[0]['fees']
  
            consulting_fees_virtual = virtual_consultation_and_fees[0]['fees']
            get_doctor.consulting_duration_clinic = consulting_duration_clinic
            get_doctor.consulting_duration_virtual = consulting_duration_virtual
            get_doctor.consulting_fees_clinic = consulting_fees_clinic
            get_doctor.consulting_fees_virtual = consulting_fees_virtual
            self.db.commit()

            doc_info = DoctorsInfo(doctorid=doctorid,
                                   profilename=doctor_data.profilename if doctor_data.profilename else existing_doctor[
                                       'profilename'],
                                   doctortype=doctor_data.doctortype if doctor_data.doctortype else existing_doctor[
                                       'doctortype'],
                                   specialization=doctor_data.specialization if doctor_data.specialization else
                                   existing_doctor['specialization'],
                                   ayoo_id_initials=existing_doctor.get('ayoo_id_initials'),
                                   languages=doctor_data.languages if doctor_data.languages else existing_doctor[
                                       'languages'],
                                   graduation=doctor_data.graduation if doctor_data.graduation else existing_doctor[
                                       'graduation'],
                                   masters=doctor_data.masters if doctor_data.masters else existing_doctor['masters'],
                                   additional_qualification=doctor_data.additional_qualification if doctor_data.additional_qualification else
                                   existing_doctor['additional_qualification'],
                                   fellowship=doctor_data.fellowship if doctor_data.fellowship else existing_doctor[
                                       'fellowship'],
                                   residency=doctor_data.residency if doctor_data.residency else existing_doctor[
                                       'residency'],
                                   experience=doctor_data.experience if doctor_data.experience else existing_doctor[
                                       'experience'],
                                   practice_area=doctor_data.practice_area if doctor_data.practice_area else
                                   existing_doctor['practice_area'],
                                   interest_area=doctor_data.interest_area if doctor_data.interest_area else
                                   existing_doctor['interest_area'],
                                   consultation_symptoms=doctor_data.consultation_symptoms if doctor_data.consultation_symptoms else
                                   existing_doctor['consultation_symptoms'],
                                   awards=doctor_data.awards if doctor_data.awards else existing_doctor['awards'],
                                   homeaddress=doctor_data.homeaddress if doctor_data.homeaddress else existing_doctor[
                                       'homeaddress'],
                                   bio=doctor_data.bio if doctor_data.bio else existing_doctor['bio'],
                                   degree=doctor_data.degree if doctor_data.degree else existing_doctor['degree'],
                                   is_active=existing_doctor['is_active'],
                                   is_offering_couple_therapy=doctor_data.is_offering_couple_therapy if doctor_data.is_offering_couple_therapy is not None else
                                   existing_doctor.get('is_offering_couple_therapy', False),
                                   is_offering_family_therapy=doctor_data.is_offering_family_therapy if doctor_data.is_offering_family_therapy is not None else
                                   existing_doctor.get('is_offering_family_therapy', False),
                                   license=doctor_data.license if doctor_data.license else existing_doctor['license'],
                                   signature=doctor_data.signature if doctor_data.signature else existing_doctor[
                                       'signature'] if 'signature' in existing_doctor else '',
                                   working_hour_starts_at=doctor_data.working_hour_starts_at if doctor_data.working_hour_starts_at else
                                   existing_doctor['working_hour_starts_at'],
                                   working_hour_ends_at=doctor_data.working_hour_ends_at if doctor_data.working_hour_ends_at else
                                   existing_doctor['working_hour_ends_at'],
                                   virtual_consultation_and_fees=virtual_consultation_and_fees,
                                   clinic_consultation_and_fees=clinic_consultation_and_fees,
                                   display_sequence=doctor_data.display_sequence,
                                   )

            doctor_info_to_dict = dict(doc_info)

            mongo_collection.find_one_and_update({"doctorid": doctorid}, {
                "$set": doctor_info_to_dict
            })
            get_doctor_after: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                DBDoctor.doctorid == doctorid).one_or_none()
           
            doctor_info_to_dict['firstname'] = get_doctor_after.firstname
            doctor_info_to_dict['lastname'] = get_doctor_after.lastname
            doctor_info_to_dict['dob'] = get_doctor_after.dob
            doctor_info_to_dict['email'] = get_doctor_after.email
            doctor_info_to_dict['mobile'] = get_doctor_after.mobile
            doctor_info_to_dict['gender'] = get_doctor_after.gender
            doctor_info_to_dict['ayooid'] = get_doctor_after.ayooid
            doctor_info_to_dict['consulting_duration_virtual'] = get_doctor_after.consulting_duration_virtual
            doctor_info_to_dict['consulting_duration_clinic'] = get_doctor_after.consulting_duration_clinic
            doctor_info_to_dict['consulting_fees_virtual'] = get_doctor_after.consulting_fees_virtual
            doctor_info_to_dict['consulting_fees_clinic'] = get_doctor_after.consulting_fees_clinic

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code mongo - {err} for updating doctor, with doctorid {str(doctorid)} by admin'

        return doctor_info_to_dict, 'Doctor updated'

    def delete_doctor_by_id(self, doctorid: str):
        try:
            get_doctor: DBDoctor = self.db.query(DBDoctor).filter(
                DBDoctor.doctorid == doctorid).one_or_none()
            if get_doctor is None:
                return None, f'{doctorid} does not exist'
            doc_obj = copy.deepcopy(get_doctor)
            self.db.delete(get_doctor)
            self.db.commit()
            self.mongo_db["DoctorsInfo"].delete_one({"doctorid": doctorid})
            return doc_obj, 'doctor deleted successfully'
        except Exception as e:
            return None, f'Exception occurred as {str(e)} while deleting doctor by admin'

    def get_doctor_profile_image(self, doctor_id: str):
        try:
            check_info_exist = self.mongo_db['DoctorsInfo'].find_one({"doctorid": str(doctor_id)})
            if 'image_id' in check_info_exist.keys() and 'profile_image_url' in check_info_exist.keys():
                return check_info_exist, 'doctor profile image found'
            else:
                return None, 'doctor profile image not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting doctor profile image for doctorid: {doctor_id}'

    def upload_doctor_profile_image(self, profile_view: UploadDoctorProfileImages, doctor_id: str):
        try:
            s3_instance = AWSS3Client()
            image_id = str(uuid.uuid4())
            generated_url, msg = s3_instance.upload_doctor_profile_iamge_to_s3(
                image_str=str(profile_view.profile_image_encoded), image_id=str(image_id))
            if generated_url is None:
                return None, msg
            profile_image_info = DoctorProfileImageView(
                image_id=image_id,
                profile_image_url=generated_url,
            )

            self.mongo_db['DoctorsInfo'].find_one_and_update({"doctorid": str(doctor_id)}, {
                "$set": dict(profile_image_info)})

            return dict(profile_image_info), 'successfully uploaded'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while uploading the doctor profile image'

    def upload_doctor_signature_image(self, profile_view: UploadDoctorSignatureImages, doctor_id: str):
        try:
            s3_instance = AWSS3Client()
            image_id = profile_view.doctor_id
            generated_url, msg = s3_instance.upload_doctor_signature_iamge_to_s3(
                image_str=str(profile_view.signature_image_encoded), image_id=image_id)
            if generated_url is None:
                return None, msg
            """profile_image_info = DoctorProfileImageView(
                image_id=image_id,
                profile_image_url=generated_url,
            )"""

            self.mongo_db['DoctorsInfo'].find_one_and_update({"doctorid": str(doctor_id)}, {
                "$set": {"signature": generated_url}})

            return generated_url, 'successfully uploaded'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while uploading the doctor profile image'

    def delete_doctor_profile_image(self, doctor_id: str):
        try:
            check_doctor_image_exist, msg = self.get_doctor_profile_image(doctor_id=str(doctor_id))
            if check_doctor_image_exist:
                self.mongo_db['DoctorsInfo'].find_one_and_update({"doctorid": doctor_id},
                                                                 {"$unset": {"image_id": "", "profile_image_url": ""}})
                return check_doctor_image_exist, 'successfully deleted'
            else:
                return None, f'profile image not found for doctorid: {str(doctor_id)}'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while uploading the doctor profile image'

    def get_all_specialist_doctors(self, specialist: SearchDoctorsBasedOnSpecialization):
        try:
            if specialist.specialization:
                search_words = [specialist.specialization.title()]
                if specialist.specialization.title() == 'Other Concern' or specialist.specialization.title() == 'Other Concerns':
                    search_words = ['Other Concern', 'Other Concerns']
                doctor_resp_all = self.mongo_db['DoctorsInfo'].find({"$or": [
                    {"specialization": specialist.specialization.title()},
                    {"practice_area": {'$in': search_words}}]})

            else:
                doctor_resp_all = self.mongo_db['DoctorsInfo'].find({})

            all_doctor_list = []
            if len(list(doctor_resp_all.clone())):
                for doctor_resp in doctor_resp_all:
                    val = self.db.query(DBDoctor).filter_by(doctorid=doctor_resp['doctorid']).one_or_none()
                    if val:
                        # active_status = doctor_resp['is_active'] if 'is_active' in doctor_resp.keys() else \
                        #     "active status not set"
                        doctor_clinic_info, msg = self.get_doctor_clinic_info(doctor_id=str(doctor_resp['doctorid']))
                        clinic_details = []
                        if doctor_clinic_info is not None:
                            for clinic in doctor_clinic_info:
                                clinic_details.append(clinic['clinic_info'])

                        doctor_profile_picture = {
                            'image_id': doctor_resp[
                                'image_id'] if 'image_id' in doctor_resp else None,
                            'profile_image_url': doctor_resp[
                                'profile_image_url'] if 'profile_image_url' in doctor_resp else None
                        }

                        if doctor_resp['is_active'] == True:
                            all_doctor_list.append(
                                {
                                    'doctorid': val.doctorid,
                                    'firstname': val.firstname,
                                    'lastname': val.lastname,
                                    'email': val.email,
                                    'mobile': val.mobile,
                                    'dob': val.dob,
                                    'gender': val.gender,
                                    'ayooid': val.ayooid,
                                    'consulting_duration_virtual': val.consulting_duration_virtual,
                                    'consulting_duration_clinic': val.consulting_duration_clinic,
                                    'consulting_fees_virtual': val.consulting_fees_virtual,
                                    'consulting_fees_clinic': val.consulting_fees_clinic,
                                    'profilename': doctor_resp['profilename'],
                                    'doctortype': doctor_resp['doctortype'],
                                    'specialization': doctor_resp['specialization'],
                                    'languages': doctor_resp['languages'],
                                    'graduation': doctor_resp['graduation'],
                                    'masters': doctor_resp['masters'],
                                    'additional_qualification': doctor_resp['additional_qualification'],
                                    'fellowship': doctor_resp['fellowship'],
                                    'residency': doctor_resp['residency'],
                                    'experience': doctor_resp['experience'],
                                    'practice_area': doctor_resp['practice_area'],
                                    'interest_area': doctor_resp.get('interest_area', []),
                                    'consultation_symptoms': doctor_resp.get('consultation_symptoms', []),
                                    'awards': doctor_resp['awards'],
                                    'homeaddress': doctor_resp['homeaddress'],
                                    'bio': doctor_resp['bio'],
                                    'is_active': doctor_resp['is_active'],
                                    'family_doctor_active_status': doctor_resp['family_doctor_active'],
                                    'is_offering_couple_therapy': doctor_resp.get('is_offering_couple_therapy', False),
                                    'is_offering_family_therapy': doctor_resp.get('is_offering_family_therapy', False),
                                    'clinic_attached': clinic_details,
                                    'doctor_profile_picture': doctor_profile_picture
                                }
                            )

            return all_doctor_list, "Returned All Doctor"

        except Exception as e:
            return None, f'error occurred as {str(e)} while getting all Doctors'

    def upload_doctor_profile_image_by_admin(self, profile_view: UploadDoctorProfileImagesByAdmin):
        try:
            profile_image_info, msg = self.upload_doctor_profile_image(
                profile_view=UploadDoctorProfileImages(profile_image_encoded=profile_view.profile_image_encoded),
                doctor_id=profile_view.doctorid)

            if profile_image_info is None:
                return None, msg
            return dict(profile_image_info), 'successfully uploaded'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while uploading the doctor profile image'

    def create_virtual_slots_admin(self, mapping_data: VirtualAppointmentCopy):
        doctorid = mapping_data.doctorid
        check_if_assingned = self.__get_virtual_slots(doctorid)
        if check_if_assingned:
            return None, f'Doctor\'s virtual appointments are already created {doctorid}.'

        sunday_slots = []
        monday_slots = []
        tuesday_slots = []
        wednesday_slots = []
        thursday_slots = []
        friday_slots = []
        saturday_slots = []

        # availability_type = 'Virtual'

        if mapping_data.sunday:
            for slots in mapping_data.sunday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Sunday. Start and end time must be in the format 'HH:MM AM/PM'."
                sunday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        # logger.info(sunday_slots)
        if mapping_data.monday:
            for slots in mapping_data.monday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Monday. Start and end time must be in the format 'HH:MM AM/PM'."
                monday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.tuesday:
            for slots in mapping_data.tuesday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Tuesday. Start and end time must be in the format 'HH:MM AM/PM'."
                tuesday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.wednesday:
            for slots in mapping_data.wednesday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Wednesday. Start and end time must be in the format 'HH:MM AM/PM'."
                wednesday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.thursday:
            for slots in mapping_data.thursday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Thursday. Start and end time must be in the format 'HH:MM AM/PM'."
                thursday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.friday:
            for slots in mapping_data.friday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Friday. Start and end time must be in the format 'HH:MM AM/PM'."
                friday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        if mapping_data.saturday:
            for slots in mapping_data.saturday:
                try:
                    datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                    datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                except Exception:
                    return None, f"Invalid time format for Saturday. Start and end time must be in the format 'HH:MM AM/PM'."
                saturday_slots.append(dict(
                    slotid=str(uuid.uuid4()),
                    starts_on=datetime.datetime.strptime(
                        slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                    ends_on=datetime.datetime.strptime(
                        slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                    starts_at=slots['starts_at'],
                    ends_at=slots['ends_at'],
                    availability_type=slots['availability_type'],
                    is_active=True
                )
                )

        mapping_info = VirtualAppointmentBooking(doctorid=doctorid, sunday=sunday_slots, monday=monday_slots,
                                                 tuesday=tuesday_slots, wednesday=wednesday_slots,
                                                 thursday=thursday_slots,
                                                 friday=friday_slots, saturday=saturday_slots)

        try:
            mongo_collection = self.mongo_db['VirtualSlots']
            mongo_collection.insert_one(dict(mapping_info))

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for virtual appointment booking'

        mapped_data = self.__get_virtual_slots(doctorid=doctorid)
        mapped_data['doctorid'] = doctorid

        return mapped_data, 'Virtual Appointment Created'

    def get_all_appointment_case_id(self, case_id: str):
        try:
            all_case = self.mongo_db['Appointments'].find(dict(caseid=str(case_id)))
            if len(list(all_case.clone())):
                return all_case, 'case_id found'
            else:
                return None, 'case_id not found'
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting all appointments for case_id {case_id}'

    def extend_end_date_of_caseid(self, extend_view: ExtendEndDateOfCaseId):
        try:
            case_resp, msg = self.get_all_appointment_case_id(case_id=str(extend_view.case_id))
            if case_resp:
                date_extended = datetime.datetime.strptime(str(extend_view.extended_date), '%Y-%m-%d')
                for appointment in case_resp:
                    appointment['end_date'] = date_extended
                    self.mongo_db['Appointments'].find_one_and_update(
                        {"appointment_id": str(appointment['appointment_id'])},
                        {
                            "$set": dict(appointment)
                        })
                return str(extend_view.case_id), 'extended date successfully'
            else:
                return None, msg
        except Exception as e:
            return None, f'error occurred as {str(e)} while extending end date for case_id:{str(extend_view.case_id)}'

    def close_active_caseid(self, active_view: CloseActiveCaseId):
        try:
            case_resp, msg = self.get_all_appointment_case_id(case_id=str(active_view.case_id))
            if case_resp:
                date_closed = datetime.datetime.today()
                for appointment in case_resp:
                    appointment['is_active'] = False
                    appointment['end_date'] = date_closed
                    self.mongo_db['Appointments'].find_one_and_update(
                        {"appointment_id": str(appointment['appointment_id'])},
                        {
                            "$set": dict(appointment)
                        })
                return str(active_view.case_id), 'close active case_id successfully'
            else:
                return None, msg
        except Exception as e:
            return None, f'error occurred as {str(e)} while closing active case_id: {str(active_view.case_id)}'

    def get_all_appointment_caseid_list(self, active_view: CloseActiveCaseId):
        try:
            case_resp, msg = self.get_all_appointment_case_id(case_id=str(active_view.case_id))
            logger.info(f'the case resp is : {case_resp}')
            if case_resp:
                case_list = []
                for appointment in case_resp:
                    logger.info(appointment)
                    case_list.append({'appointment_id': appointment['appointment_id'],
                                      'appointment_type': appointment['appointment_type'],
                                      'appointment_for': appointment['appointment_for'],
                                      'symptoms': appointment['symptoms'],
                                      'symptoms_audio_clip': appointment['symptoms_audio_clip'],
                                      'additional_notes': appointment['additional_notes'],
                                      'clinicid': appointment['clinicid'],
                                      'doctorid': appointment['doctorid'],
                                      'appointment_slot': appointment['appointment_slot'],
                                      'is_active': appointment['is_active'],
                                      'is_confirmed': appointment['is_confirmed'],
                                      'payment': appointment['payment'],
                                      'caseid': appointment['caseid'],
                                      'patient_id': appointment['patient_id'],
                                      'booked_by': appointment['booked_by'],
                                      'created_at': appointment['created_at'],
                                      'end_date': appointment['end_date']})
                return case_list, 'case_id found successfully'
            else:
                return None, msg
        except Exception as e:
            return None, f'error occurred as {str(e)} while getting all appointments for' \
                         f' case_id: {str(active_view.case_id)}'

    def available_slot_copy(self, doctorid: str, start_time, end_time, duration, search_date, slot_id):
        starts_at = start_time
        ends_at = end_time
        date_from = datetime.datetime.strptime(search_date, '%Y-%m-%d')
        date_till = date_from + datetime.timedelta(hours=+22, minutes=+59, seconds=+59)
        time_format = '%I:%M %p'
        available_from = datetime.datetime.strptime(starts_at, time_format)
        available_till = datetime.datetime.strptime(ends_at, time_format)
        slot = available_from
        available_slots = []
        while slot < available_till:
            available_slots.append(slot.strftime(time_format))
            # logger.info(slot.strftime(time_format))
            slot = slot + datetime.timedelta(hours=0, minutes=int(duration))

        # check appointments for same date
        appointments_list = []
        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                'doctorid': doctorid,
                '$and': [
                    {'appointment_slot': {
                        '$gte': date_from, '$lte': date_till}},
                    {'appointment_type': 'InClinic'},
                    {'is_active': True},
                    {'is_confirmed': True}
                ]
            },
            {'appointment_slot'}
        )
        )

        # remove already booked slot
        if appointments_list:
            for appointments in appointments_list:
                appointment_time = datetime.datetime.strftime(
                    appointments['appointment_slot'], '%H:%M:%S')
                if appointment_time in available_slots:
                    available_slots.remove(
                        appointment_time)

        # Removed Blocked Slots

        if available_slots:
            available_slots_copy = copy.deepcopy(available_slots)
            # slot_id = None
            slot_db_res: DBBlockSlots
            slot_db_res = self.db.query(DBBlockSlots).filter_by(doctor_id=str(doctorid)).filter_by(
                slot_id=str(slot_id)).all()
            if slot_db_res:
                for slot in available_slots_copy:
                    block_slot = f'{search_date} {slot}'
                    now_dt = datetime.datetime.now()
                    expiry_at_unix = time.mktime(now_dt.timetuple())
                    for slot_res in slot_db_res:
                        if float(slot_res.expiry_at) > expiry_at_unix:
                            block_slot_dt = datetime.datetime.strptime(str(block_slot), '%Y-%m-%d %I:%M %p')
                            block_slot_dtf = block_slot_dt.strftime('%Y-%m-%d%I:%M %p')
                            if slot_res.block_slot == block_slot_dtf:
                                available_slots.remove(slot)

        # remove past slots
        available_slots_same_date = []
        if datetime.datetime.strptime(str(search_date), '%Y-%m-%d') == datetime.datetime.strptime(
                str(datetime.date.today()), '%Y-%m-%d'):
            now = datetime.datetime.now()
            current_time = now.strftime("%I:%M %p")
            for av_slot in available_slots:
                # logger.info(f'comparing time is : {str(av_slot)}')
                if datetime.datetime.strptime(str(av_slot), time_format) <= \
                        datetime.datetime.strptime(str(current_time), time_format):
                    continue
                else:
                    available_slots_same_date.append(str(av_slot))
            return available_slots_same_date

        return available_slots

    def date_day_after_increase_copy(self, initial_date, increase_day):
        start_date = datetime.datetime.strptime(str(initial_date), "%Y-%m-%d")
        end_date = start_date + datetime.timedelta(days=increase_day)
        weekday_no = end_date.weekday()
        day_name = calendar.day_name[weekday_no].lower()
        return str(end_date), str(day_name)

    def get_clinic_doctor_availability_copy(self, search_view: CheckDoctorAvailableSlot):
        try:
            if datetime.datetime.strptime(str(search_view.search_date), '%Y-%m-%d') < \
                    datetime.datetime.strptime(str(datetime.date.today()), '%Y-%m-%d'):
                return None, f'old date'
            doctor_res: DBDoctor
            doctor_res = self.db.query(DBDoctor).filter_by(doctorid=str(search_view.doctorid)).one_or_none()
            if doctor_res is None:
                return None, 'doctorid not found'

            clinic_res: DBClinic
            clinic_res = self.db.query(DBClinic).filter_by(clinicid=str(search_view.clinicid)).one_or_none()
            if clinic_res is None:
                return None, 'clinicid not found'

            mapping_res: DBClinicAndDoctors
            mapping_res = self.db.query(DBClinicAndDoctors).filter_by(clinicid=str(search_view.clinicid)).filter_by(
                doctorid=str(search_view.doctorid)).one_or_none()
            if mapping_res is None:
                return None, 'doctor is not mapped with this clinic'
            slot_id_list = []
            slot_res = self.mongo_db['DoctorAndClinic'].find_one({"mappingid": str(mapping_res.mappingid)})
            increased_date, increased_day_name = self.date_day_after_increase_copy(
                initial_date=str(search_view.search_date),
                increase_day=0)
            date_ymd = increased_date.split(" ")[0]
            if len(slot_res[str(increased_day_name)]) == 0:
                return slot_id_list, 'No available slot on this date'
            else:
                for val in slot_res[str(increased_day_name)]:
                    if val['availability_type'] == 'InClinic' and val['is_active'] is True \
                            and datetime.datetime.strptime(str(date_ymd), '%Y-%m-%d') <= \
                            datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):
                        slot_av = None
                        slot_av = self.available_slot_copy(doctorid=str(doctor_res.doctorid),
                                                           start_time=val['starts_at'],
                                                           end_time=val['ends_at'],
                                                           duration=doctor_res.consulting_duration_clinic,
                                                           search_date=date_ymd,
                                                           slot_id=val['slotid'])
                        if len(slot_av) == 0:
                            continue
                        else:
                            slot_id_list.append({'slotid': str(val['slotid']),
                                                 'starts_on': val['starts_on'],
                                                 'ends_on': val['ends_on'],
                                                 'starts_at': val['starts_at'],
                                                 'ends_at': val['ends_at'],
                                                 'available_slot': slot_av})
                    else:
                        continue
            if len(slot_id_list):
                return slot_id_list, 'Available Slot Found'
            else:
                return slot_id_list, 'Available Slot Not Found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting doctor available slot for clinic'

    def get_doctor_virtual_availability_slots(self, request_data: RequestAvailableSlots, doctorid: str):
        now = datetime.date.today()
        doctor_info = self.__get_doctor_by_id(
            doctorid=doctorid)
        if doctor_info:
            doctors_consulting_duration = doctor_info.consulting_duration_virtual

        appointments = []
        if request_data.starts_from:
            date_from = datetime.datetime.strptime(
                request_data.starts_from, '%Y-%m-%d')
        else:
            date_from = None

        if request_data.till:
            date_till = datetime.datetime.strptime(
                request_data.till, '%Y-%m-%d')
        else:
            date_till = date_from + \
                        datetime.timedelta(hours=+22, minutes=+59, seconds=+59)

        dt_list = []
        wk_list = []
        slots1 = []
        slots = []
        new_date_data_dict = {}
        new_list = []
        newlist1 = []
        newlist2 = []
        newlist3 = []

        delta = timedelta(days=1)

        while date_from <= date_till:
            dt_list.append(date_from.strftime("%Y-%m-%d"))
            date_from += delta

        for dt_i in dt_list:
            try:
                weekday_number = datetime.datetime.strptime(
                    dt_i, '%Y-%m-%d').weekday()
                weekday = calendar.day_name[weekday_number]
                weekday = weekday.lower()
                if weekday not in wk_list:
                    wk_list.append(weekday)
            except ValueError:
                return None, 'Date needs to be in YYYY-mm-dd format'

        mongo_collection_virtual_slots = self.mongo_db['VirtualSlots']

        for wk_i in wk_list:
            slots1 = list(mongo_collection_virtual_slots.find(
                {
                    'doctorid': doctorid,
                    '$and': [
                        {f'{wk_i}.starts_on': {'$lte': date_from}},

                        {f'{wk_i}.ends_on': {'$gte': date_till}}
                    ],
                },
                {'doctorid', f'{wk_i}'}

            )
            )

            slots = slots + slots1
        date_dict_list = []
        for dt_i in dt_list:
            weekday_number = datetime.datetime.strptime(
                dt_i, '%Y-%m-%d').weekday()
            weekday = calendar.day_name[weekday_number]
            weekday = weekday.lower()
            date_dict = {dt_i: weekday}
            date_dict_list.append(date_dict)

        for varx in slots:
            data1 = dict(varx)
            data = copy.deepcopy(data1)
            key_list = list(data.keys())
            day_index = key_list[2]
            data_var = data[day_index]
            new_date_data_dict[day_index] = data_var

        available_slots = []
        for idx in date_dict_list:
            day_element = list(idx.values())[0]
            if day_element in new_date_data_dict:
                new_date_data_dict_ele = new_date_data_dict[day_element]

                for varidx in new_date_data_dict_ele:
                    availability_slotid = varidx['slotid']
                    starts_at = varidx['starts_at']
                    ends_at = varidx['ends_at']
                    format = '%I:%M %p'
                    available_from = datetime.datetime.strptime(
                        starts_at, format)
                    available_till = datetime.datetime.strptime(
                        ends_at, format)
                    slot = available_from
                    while slot <= available_till:
                        available_slots.append(slot.strftime(format))
                        new_list.append({slot.strftime(format): idx})
                        slot = slot + timedelta(hours=0, minutes=doctors_consulting_duration)

        for new_ele in new_list:
            for key1, value1 in new_ele.items():
                newlist1.append(value1)
                newlist2.append(key1)
        for newele1 in newlist1:
            for k, v in newele1.items():
                newlist3.append(k)

        res = [a + " " + b for a, b in zip(newlist3, newlist2)]
        resc = copy.deepcopy(res)
        # Get doctors appointment
        appointments_list = []
        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                'doctorid': doctorid,
                '$and': [
                    {'appointment_type': 'Virtual'},
                    {'is_active': True},
                    {'is_confirmed': True}
                ]
            },
            {'appointment_slot'}
        )

        )
        # remove booked slots
        booked_slots = []
        if appointments_list:
            for appointments in appointments_list:
                appointment_time = datetime.datetime.strftime(
                    appointments['appointment_slot'], "%Y-%m-%d %I:%M %p")
                booked_slots.append(appointment_time)
        for booked_date in booked_slots:
            for final_date in resc:
                if booked_date == final_date:
                    resc.remove(final_date)
        # remove past slots
        resc1 = copy.deepcopy(resc)
        now_dt = now.strftime("%Y-%m-%d %I:%M %p")
        for slot in resc1:
            if datetime.datetime.strptime(now_dt, "%Y-%m-%d %I:%M %p") >= datetime.datetime.strptime(slot,
                                                                                                     "%Y-%m-%d %I:%M %p"):
                resc1.remove(slot)

        # some lists/dicts used for data manipulation
        clinic_list = []
        final_virtual = resc1
        clinic_deets = {}
        final_inclinic = []
        final_inclinic1 = {}
        final_inclinic2 = {}
        final_resp = {}
        newtup = ()

        final_resp['Virtual'] = final_virtual
        clinic_query: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(doctorid=str(doctorid)).all()
        for clinicidx in clinic_query:
            clinic_list.append(clinicidx.clinicid)
        for clinicinfo in clinic_list:
            clideets = self.__get_clinic_details(clinicid=clinicinfo)
            clinic_deets['clinicid'] = clinicinfo
            clinic_deets['name'] = clideets.name
            clinic_deets['address'] = clideets.address
            clinic_deets['lat'] = clideets.lat
            clinic_deets['lon'] = clideets.lon
            clinic_deets['services'] = clideets.name
            clinic_deets['city'] = clideets.city
            final_inclinic.append(copy.deepcopy(clinic_deets))

            for idx1 in date_dict_list:
                for datek, datev in idx1.items():
                    search_view_data = CheckDoctorAvailableSlot(doctorid=doctorid, clinicid=clinicinfo,
                                                                search_date=datek)
                    inclinic_avail_list, msg = self.get_clinic_doctor_availability_copy(search_view=search_view_data)
                    if len(inclinic_avail_list):
                        newtup = (copy.deepcopy(inclinic_avail_list), copy.deepcopy(clinic_deets))
                        final_inclinic1[datek] = (copy.deepcopy(newtup), idx1)
                        final_inclinic2[clinicinfo] = copy.deepcopy(final_inclinic1)

        final_resp['InClinic'] = final_inclinic2
        return

    def update_virtual_slots_admin(self, mapping_data: VirtualAppointmentCopy):
        try:
            doctorid = mapping_data.doctorid

            existing_slots = self.__get_virtual_slots(doctorid=doctorid)
            if existing_slots is None:
                return None, 'No existing slots found. Add slots first.'

            sunday_slots = []
            monday_slots = []
            tuesday_slots = []
            wednesday_slots = []
            thursday_slots = []
            friday_slots = []
            saturday_slots = []

            # availability_type = 'Virtual'

            if mapping_data.sunday:
                for slots in mapping_data.sunday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Sunday. Start and end time must be in the format 'HH:MM AM/PM'."
                    sunday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.monday:
                for slots in mapping_data.monday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Monday. Start and end time must be in the format 'HH:MM AM/PM'."
                    monday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.tuesday:
                for slots in mapping_data.tuesday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Tuesday. Start and end time must be in the format 'HH:MM AM/PM'."
                    tuesday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.wednesday:
                for slots in mapping_data.wednesday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Wednesday. Start and end time must be in the format 'HH:MM AM/PM'."
                    wednesday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.thursday:
                for slots in mapping_data.thursday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Thursday. Start and end time must be in the format 'HH:MM AM/PM'."
                    thursday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.friday:
                for slots in mapping_data.friday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Friday. Start and end time must be in the format 'HH:MM AM/PM'."
                    friday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            if mapping_data.saturday:
                for slots in mapping_data.saturday:
                    try:
                        datetime.datetime.strptime(slots['starts_at'], "%I:%M %p")
                        datetime.datetime.strptime(slots['ends_at'], "%I:%M %p")
                    except Exception:
                        return None, f"Invalid time format for Saturday. Start and end time must be in the format 'HH:MM AM/PM'."
                    saturday_slots.append(dict(
                        slotid=str(uuid.uuid4()),
                        starts_on=datetime.datetime.strptime(
                            slots['starts_on'], '%Y-%m-%d') if slots['starts_on'] else '',
                        ends_on=datetime.datetime.strptime(
                            slots['ends_on'], '%Y-%m-%d') if slots['ends_on'] else '',
                        starts_at=slots['starts_at'],
                        ends_at=slots['ends_at'],
                        availability_type=slots['availability_type'],
                        is_active=True
                    )
                    )

            mapping_info = VirtualAppointmentBooking(doctorid=doctorid, sunday=sunday_slots, monday=monday_slots,
                                                     tuesday=tuesday_slots, wednesday=wednesday_slots,
                                                     thursday=thursday_slots,
                                                     friday=friday_slots, saturday=saturday_slots)

            try:
                mongo_collection = self.mongo_db['VirtualSlots']
                mongo_collection.find_one_and_update({"doctorid": mapping_data.doctorid}, {"$set": dict(mapping_info)})


            except Exception as e:
                err = str(e)
                loggers['logger9'].error(
                    "/admin/update/virtual_slots_for_doctor : status_code=409, error occured as : " + str(e))
                return None, f'Internal Error code {err} for virtual slots updating'

            mapped_data = self.__get_virtual_slots(doctorid=doctorid)
            mapped_data['doctorid'] = doctorid

            return mapped_data, 'Virtual Slots Updated'
        except Exception as err:
            loggers['logger9'].error(
                "/admin/update/virtual_slots_for_doctor : status_code=409, error occured as : " + str(e))
            return None, str(err)

    def delete_virtual_slots_admin(self, request_data: DeleteVirtualSlotsView):
        try:
            doctorid = request_data.doctorid
            slots = self.mongo_db["VirtualSlots"].find_one({"doctorid": doctorid})
            slots_obj = copy.deepcopy(slots)
            self.mongo_db["VirtualSlots"].delete_one({"doctorid": doctorid})
            return slots_obj, 'Virtual slots deleted successfully'
        except Exception as e:
            loggers['logger9'].error("/admin/delete/virtual_slots : status_code=409, error occured as : " + str(e))
            return None, f'Exception occurred as {str(e)} while deleting Virtual slots by admin'

    def update_family_doctor_status(self, doctor_id: str, family_doctor: bool):
        try:
            doctor_exist = self.mongo_db['DoctorsInfo'].find_one({"doctorid": str(doctor_id)})
            if not doctor_exist:
                return None, 'doctor not exists in database'
            doctor_exist['family_doctor_active'] = family_doctor
            self.mongo_db['DoctorsInfo'].find_one_and_update({"doctorid": str(doctor_id)}, {
                "$set": dict(doctor_exist)
            })
            return {"doctorid": doctor_exist['doctorid'],
                    "family_doctor_active": doctor_exist['family_doctor_active']}, 'updated successfully'
        except Exception as e:
            return None, f'error occurred as {str(e)} while updating doctor status'

    def get_family_doctor_status(self, doctor_id: str):
        try:
            doctor_exist = self.mongo_db['DoctorsInfo'].find_one({"doctorid": str(doctor_id)})
            if not doctor_exist:
                return None, 'doctor not exists in database'
            return {"doctorid": doctor_exist['doctorid'],
                    "family_doctor_active": doctor_exist['family_doctor_active'] if
                    'family_doctor_active' in doctor_exist.keys() else False}, 'get family_doctor_active status successfully'
        except Exception as e:
            return None, f'error occurred as {str(e)} while updating doctor status'

    def update_all_records_doctors_family(self):
        try:
            doctor_exist = self.mongo_db['DoctorsInfo'].find()
            if len(list(doctor_exist.clone())):
                for doctor in doctor_exist:
                    if 'family_doctor_active' in doctor.keys():
                        continue
                    else:
                        doctor['family_doctor_active'] = True if doctor['specialization'] == 'Physician' else False
                        # if doctor['specialization'] == 'Physician':
                        #     doctor['family_doctor_active'] = True
                        # else:
                        #     doctor['family_doctor_active'] = False
                        self.mongo_db['DoctorsInfo'].find_one_and_update({"doctorid": doctor['doctorid']}, {
                            "$set": dict(doctor)
                        })
                return 'successfully updated', 'successfully updated'
        except Exception as e:
            return None, f'error occurred as {str(e)} while updating all records of family_doctors field of DoctorsInfo'

    def get_patient_details(self, patientid: str):
        from . import dbmodels

        patient_data = {}
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            patient_data['firstname'] = resp_user.firstname
            patient_data['lastname'] = resp_user.lastname
            patient_data['dob'] = resp_user.birthdate
            patient_data['email'] = resp_user.email
            patient_data['mobile'] = resp_user.mobile
            patient_data['gender'] = resp_user.gender

            return patient_data

        resp_guest: DBGuest = self.db.query(dbmodels.DBGuest).filter(
            DBGuest.guestid == patientid).one_or_none()
        if resp_guest:
            patient_data['firstname'] = resp_guest.firstname
            patient_data['lastname'] = resp_guest.lastname
            patient_data['dob'] = resp_guest.birthdate
            patient_data['email'] = resp_guest.email
            patient_data['mobile'] = resp_guest.mobile
            patient_data['gender'] = resp_guest.gender

            return patient_data

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).one_or_none()
        if resp_relative:
            patient_data['firstname'] = resp_relative.firstname
            patient_data['lastname'] = resp_relative.lastname
            patient_data['dob'] = resp_relative.birthdate
            patient_data['email'] = resp_relative.email
            patient_data['mobile'] = resp_relative.mobile
            patient_data['gender'] = resp_relative.gender

            return patient_data
        else:
            return None

    def patient_directory_of_doctors(self, doctor_id: str, page_number: int = 1, page_size: int = 16):
        try:
            skip = (page_number - 1) * page_size
            pipeline = [
                {"$match": {"doctorid": doctor_id}},
                {"$sort": {"_id": -1}},
                {"$group": {
                    "_id": "$patient_id",
                    "latest_patient": {"$max": "$appointment_slot"}
                }},
                {"$sort": {"latest_patient": -1}},
                {"$skip": skip},
                {"$limit": page_size},
                {"$project": {"_id": 0, "patient_id": "$_id"}}
            ]

            all_patients = list(self.mongo_db['Appointments'].aggregate(pipeline))

            new_patient_details = []
            active_patient_details = []

            for new_patient in all_patients:
                resp: DBUser = self.db.query(dbmodels.DBUser).filter(DBUser.userid == new_patient['patient_id'], DBUser.is_deleted==False).one_or_none()
                if resp is not None:
                    new_patient_details.append({
                        'userid' : resp.userid,
                        'care_taker_id' : None,
                        'care_taker_name' : None,
                        'firstname' : resp.firstname,
                        'lastname' : resp.lastname,
                        'birthdate' : resp.birthdate,
                        'mobile' : resp.mobile,
                        'email' : resp.email,
                        'gender' : resp.gender
                    })
                else:
                    resp= self.db.query(dbmodels.DBRelatives, dbmodels.DBUser).join(DBUser, DBRelatives.caretaker_id == DBUser.userid).filter(DBRelatives.relativeid == new_patient['patient_id']).one_or_none()
                    if resp is not None:
                        relative, user = resp
                        new_patient_details.append({
                            'userid' : relative.relativeid,
                            'care_taker_id' : relative.caretaker_id,
                            'care_taker_name' : user.firstname + ' ' + user.lastname if user is not None else None,
                            'firstname' : relative.firstname,
                            'lastname' : relative.lastname,
                            'birthdate' : relative.birthdate,
                            'mobile' : relative.mobile,
                            'email' : relative.email,
                            'gender' : relative.gender
                        })

            return {"new_patients": new_patient_details,
                    "active_patients": active_patient_details}, 'successfully returned'
        except Exception as e:
            return None, f'error occurred as {str(e)} while fetching patient directory'


    def get_doctor_clinic_info(self, doctor_id: str):
        try:
            mapping_res: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(doctorid=doctor_id).all()
            if mapping_res:
                doctor_clinic_list = []
                for cd in mapping_res:
                    clinic_info = self.get_clinic_by_id(str(cd.clinicid))
                    mapping_info = self.mongo_db['DoctorAndClinic'].find_one(dict(mappingid=str(cd.mappingid)))
                    if mapping_info:
                        doctor_clinic_list.append({'mappingid': mapping_info["mappingid"],
                                                   'doctorid': str(cd.doctorid),
                                                   'clinic_info': {'clinicid': clinic_info.clinicid,
                                                                   'name': clinic_info.name,
                                                                   'address': clinic_info.address,
                                                                   'lat': clinic_info.lat,
                                                                   'lon': clinic_info.lon,
                                                                   'city': clinic_info.city
                                                                   },
                                                   'sunday': mapping_info["sunday"],
                                                   'monday': mapping_info["monday"],
                                                   'tuesday': mapping_info["tuesday"],
                                                   'wednesday': mapping_info["wednesday"],
                                                   'thursday': mapping_info["thursday"],
                                                   'friday': mapping_info["friday"],
                                                   'saturday': mapping_info["saturday"]})
                return doctor_clinic_list, 'mapping found'
            else:
                return None, 'doctor is not mapped with any clinic'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting all clinic mapping information'

    def get_doctors_by_area(self, area: str):
        try:
            all_doctors: DBDoctor = self.db.query(dbmodels.DBDoctor).all()
            logger.info('gettiing doctors by area function')
            clinic_list = []

            for doctor in all_doctors:
                clinic_mapping, msg = self.get_doctor_clinic_info(doctor_id=str(doctor.doctorid))
                if clinic_mapping:
                    if len(clinic_mapping):
                        for clinic in clinic_mapping:
                            if clinic['clinic_info']['city'] == area:
                                clinic_list.append(clinic['doctorid'])

            # logger.info(len(all_doctors))
            if len(clinic_list) == 0:
                return None, f'No clinic found at given location {area}'
            return clinic_list, 'clinics found'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while searching clinic and doctors with location {area}'

    def get_doctors(self, request_body: SearchByAreaOrSpecialization):
        try:

            if request_body.search_word:
                specialists_found, msg = self.get_all_specialist_doctors(
                    specialist=SearchDoctorsBasedOnSpecialization(specialization=request_body.search_word))
                if specialists_found is not None:
                    return specialists_found, 'specialists found'

                logger.info('Specialization not available, searching by area')
                if City.has_value(request_body.search_word):
                    doctors = []
                    doctors_in_area, msg = self.get_doctors_by_area(str(request_body.search_word))
                    for id in doctors_in_area:
                        info, msg = self.get_by_id(doctorid=id)
                        if info is not None and info['is_active'] == True:
                            doctors.append(info)
                    if doctors is None or len(doctors) == 0:
                        return None, msg

                    return doctors, 'doctors found'
                else:
                    return None, 'Enter valid city name or specialization to search doctors'

            else:
                return None, 'Enter a key word to search doctors'


        except Exception as e:
            return None, f'Error occurred as {str(e)} while searching doctors in area or speciality'

    def search_patients_by_name(self, request_body: SearchPatientsByName):
        try:
            resp_user_list = self.db.query(DBUser).filter(
                and_(
                    or_(
                        DBUser.firstname.op('~*')(r'' + request_body.patient_name),
                        DBUser.lastname.op('~*')(r'' + request_body.patient_name)
                    ),
                    DBUser.is_deleted == False
                )
            ).all()
            patient_filter = [user.userid for user in resp_user_list]

            resp_relative_list = self.db.query(DBRelatives).filter(or_(
                DBRelatives.firstname.op('~*')(r'' + request_body.patient_name), DBRelatives.lastname.op('~*')(r'' + request_body.patient_name))).all()
            patient_filter.extend([relative.relativeid for relative in resp_relative_list])

            appointments = list(self.mongo_db['Appointments'].aggregate([
                {'$match': {
                    '$and': [
                        {'patient_id': {'$in': patient_filter}},
                        {'doctorid': str(request_body.doctor_id)}
                    ]
                }},
                {'$group': {
                    '_id': '$patient_id',
                    'appointment': {'$first': '$$ROOT'}
                }},
                {'$replaceRoot': {'newRoot': '$appointment'}},
                {'$project': {'_id': False}}
            ]))

            from ayoo_backend.api.mental_health_controller import MentalHealthController
            mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo_db)
            data_to_send = []
            doctor_specialization_data = self.get_specialization_field_of_doctor(doctor_id=str(request_body.doctor_id))
            for appointment in appointments:
                patient_id =str(appointment.get('patient_id'))
                case_id =str(appointment.get('caseid'))
                case_type = mh_ctrl.get_case_type(patient_id=patient_id, case_id=case_id)
                patient_details= mh_ctrl.get_patient_details(patientid=patient_id)
                if patient_details:
                    patient_details = dict(
                        name=patient_details['firstname'] + ' ' + patient_details['lastname'],
                        email=patient_details['email'],
                        gender=patient_details['gender'],
                        dob=patient_details['dob'],
                        family_doctor=patient_details['family_doctor'] if 'family_doctor' in patient_details else None
                    )
                data_to_send.append({
                    'patient_id': patient_id,
                    'case_type': case_type if case_type is not None else 'New',
                    'case_id': case_id,
                    'case_status': 'Active' if appointment.get('case_open') else 'Closed',
                    'patient_details': patient_details,
                    'doctor_specialization': doctor_specialization_data.specialization if doctor_specialization_data is not None else None,
                    'doctor_specialization_field': doctor_specialization_data.specialization_field if doctor_specialization_data is not None else None
                })

            return {
                'msg': 'Case found',
                'case_details': data_to_send
            }

        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while searching patients with name {request_body.patient_name}')

    def available_slot(self, doctorid: str, start_time, end_time, duration, search_date, slot_id):
        starts_at = start_time
        ends_at = end_time
        date_from = datetime.datetime.strptime(search_date, '%Y-%m-%d')
        date_till = date_from + datetime.timedelta(hours=+22, minutes=+59, seconds=+59)
        time_format = '%I:%M %p'
        available_from = datetime.datetime.strptime(starts_at, time_format)
        available_till = datetime.datetime.strptime(ends_at, time_format)
        slot = available_from
        available_slots = []
        while slot < available_till:
            available_slots.append(slot.strftime(time_format))
            slot = slot + datetime.timedelta(hours=0, minutes=int(duration))

        # check appointments for same date
        appointments_list = []
        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                '$and': [{'doctorid': doctorid},
                         {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                         {'appointment_type': 'Virtual'},
                         {'is_active': True},
                         {'is_confirmed': True}
                         ]
            }
        )
        )

        # remove already booked slot
        if appointments_list:
            for appointments in appointments_list:
                appointment_time = datetime.datetime.strftime(
                    appointments['appointment_slot'], time_format)
                if appointment_time in available_slots:
                    available_slots.remove(
                        appointment_time)

        # Removed Blocked Slots

        if available_slots:
            available_slots_copy = copy.deepcopy(available_slots)
            # slot_id = None
            slot_db_res: DBBlockSlots
            slot_db_res = self.db.query(DBBlockSlots).filter_by(doctor_id=str(doctorid)).filter_by(
                slot_id=str(slot_id)).all()
            if slot_db_res:
                for slot in available_slots_copy:
                    block_slot = f'{search_date} {slot}'
                    now_dt = datetime.datetime.now()
                    expiry_at_unix = time.mktime(now_dt.timetuple())
                    for slot_res in slot_db_res:
                        if float(slot_res.expiry_at) > expiry_at_unix:
                            block_slot_dt = datetime.datetime.strptime(str(block_slot), '%Y-%m-%d %I:%M %p')
                            block_slot_dtf = block_slot_dt.strftime('%Y-%m-%d%I:%M %p')
                            if slot_res.block_slot == block_slot_dtf:
                                available_slots.remove(slot)

        # remove past slots
        available_slots_same_date = []
        if datetime.datetime.strptime(str(search_date), '%Y-%m-%d') == datetime.datetime.strptime(
                str(datetime.date.today()), '%Y-%m-%d'):
            now = datetime.datetime.now()
            current_time = now.strftime("%I:%M %p")
            for av_slot in available_slots:
                if datetime.datetime.strptime(str(av_slot), time_format) <= \
                        datetime.datetime.strptime(str(current_time), time_format):
                    continue
                else:
                    available_slots_same_date.append(str(av_slot))
            return available_slots_same_date

        return available_slots

    def get_virtual_availability_slots_for_a_date(self, request_data: SearchSlotsOnADate, doctor_id: str):
        now = datetime.date.today()

        if request_data.search_date:
            search_dt = str(request_data.search_date)
            if datetime.datetime.strptime(str(search_dt), '%Y-%m-%d') < datetime.datetime.strptime(str(now),
                                                                                                   '%Y-%m-%d'):
                return None, f'old date'

            else:

                try:
                    weekday_number = datetime.datetime.strptime(
                        request_data.search_date, '%Y-%m-%d').weekday()
                    weekday = calendar.day_name[weekday_number]
                    weekday = weekday.lower()

                    slot_res = self.mongo_db['VirtualSlots'].find_one(
                        {"doctorid": str(doctor_id)})
                    if len(slot_res[str(weekday)]) == 0:
                        return None, 'No available slots for the day'
                    else:
                        slot_id_list = []
                        doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                            doctorid=str(doctor_id)
                        ).one_or_none()
                        for val in slot_res[str(weekday)]:
                            if val['availability_type'] == 'Virtual' and val['is_active'] is True \
                                    and datetime.datetime.strptime(str(request_data.search_date), '%Y-%m-%d') <= \
                                    datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):

                                slot_av = None
                                slot_av = self.available_slot(doctorid=str(doctor_id),
                                                              start_time=val['starts_at'],
                                                              end_time=val['ends_at'],
                                                              duration=doctor_res_duration.consulting_duration_virtual,
                                                              search_date=request_data.search_date,
                                                              slot_id=val['slotid'])
                                if len(slot_av) == 0:
                                    return None, 'No slots'
                                else:
                                    slot_id_list.append({'slotid': str(val['slotid']),
                                                         'starts_on': val['starts_on'],
                                                         'ends_on': val['ends_on'],
                                                         'starts_at': val['starts_at'],
                                                         'ends_at': val['ends_at'],
                                                         'available_slot': slot_av})
                            else:
                                continue

                        return slot_id_list, ''
                except ValueError:
                    return None, 'Date needs to be in YYYY-mm-dd format'
                except Exception as e:
                    return None, f'Error occurred as {str(e)} while getting slots'
        else:
            return None, 'Enter date for checking appointment slots.'

    def get_experience(self, doctor):
        experience = doctor["experience"]
        if experience:
            try:
                return -int(experience.split()[0])
            except (ValueError, IndexError):
                pass
        return 0

    def all_doctors_name_and_ids(self):
        try:
            all_doctors = self.db.query(dbmodels.DBDoctor).all()
            doctor_data = []
            for doctor in all_doctors:
                doctor_details, msg = self.get_by_id(doctorid=doctor.doctorid)
                # "Error occurred as 'NoneType' object is not subscriptable while getting doctors"
                # This error means the doctor id is in psql but somehow it is deleted from mongodb
                if doctor_details and doctor_details['is_active']:
                    doctor_data.append({
                        'doctor_id': doctor_details['doctorid'],
                        'firstname': doctor_details['firstname'],
                        'lastname': doctor_details['lastname'],
                        'gender': doctor_details['gender'],
                        'consulting_fees_virtual': doctor_details['consulting_fees_virtual'],
                        'consulting_fees_clinic': doctor_details['consulting_fees_clinic'],
                        'specialization': doctor_details['specialization'],
                        'specialization_field': doctor_details['specialization_field'],
                        'practice_area': doctor_details['practice_area'],
                        'interest_area': doctor_details.get('interest_area', []),
                        'consultation_symptoms': doctor_details.get('consultation_symptoms', []),
                        'experience': doctor_details['experience'],
                        'bio': doctor_details['bio'],
                        'degree': doctor_details['degree'],
                        'dob': doctor_details['dob'],
                        'license': doctor_details['license'],
                        'signature': doctor_details['signature'] if 'signature' in doctor_details else '',
                        'image_id': doctor_details['image_id'],
                        'profile_image_url': doctor_details['profile_image_url'],
                        'languages': doctor_details['languages'],
                        'graduation': doctor_details['graduation'],
                        'masters': doctor_details['masters'],
                        'consulting_duration_virtual': calcualte_appointment_duration(doctor_details['consulting_duration_virtual']),
                        'consulting_duration_clinic': calcualte_appointment_duration(doctor_details['consulting_duration_clinic']),
                        'display_sequence': doctor_details.get('display_sequence',0)
                    })

            sorted_doctor_data = sorted(doctor_data, key=lambda d: (d['display_sequence'], self.get_experience(d)))
            return sorted_doctor_data, 'data found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting doctors'

    def weekdays(self, weekday):
        days = ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
        index = days.index(weekday)
        return list(days[index:] + days)[:7]

    def get_first_available_slot_based_on_practice_area(self):
        try:
            now = datetime.date.today()
            practice_areas_and_doctors = []

            all_practice_area = self.db.query(DBPracticeAreaMeta).distinct(DBPracticeAreaMeta.practice_area_field).all()
            practice_areas_field = []
            for practice_area_field in all_practice_area:
                practice_areas_field.append(practice_area_field.practice_area_field)

            practice_areas = []

            for elem in practice_areas_field:
                p_area = self.db.query(DBPracticeAreaMeta).filter(DBPracticeAreaMeta.practice_area_field == elem).all()

                for p in p_area:
                    practice_areas.append(p.practice_area)

            dates_to_check = []

            number_of_days_to_check = VIRTUAL_SLOTS_FETCH_DAYS

            for i in range(number_of_days_to_check):
                next_day = now + timedelta(days=i)
                dates_to_check.append(str(next_day))

            for practice_area in practice_areas:
                #print("Practice Area:", practice_area)
                for date in dates_to_check:
                    get_slots, resp_msg = self.get_virtual_slots_availability(
                        request_data=RequestDoctorsVirtualAvailableSlots(
                            specialization=practice_area,
                            search_date=date
                        ), user_id=None)

                    if get_slots is not None:
                        practice_areas_and_doctors.append({
                            'practice_area': practice_area,
                            'date': date,
                            'doctors_and_slots': get_slots
                        })
                        break
                    else:
                        if dates_to_check.index(date) == number_of_days_to_check - 1:
                            practice_areas_and_doctors.append({
                                'practice_area': practice_area,
                                'doctors_and_slots': get_slots
                            })
                        continue

            for practice_area_slots in practice_areas_and_doctors:
                if practice_area_slots['doctors_and_slots'] is not None:
                    if len(practice_area_slots['doctors_and_slots']) > 0:
                        doc_and_slots = []

                        for doctor in practice_area_slots['doctors_and_slots']:
                            data = dict(doctor)
                            min_key = None

                            for key, value in data['slots'].items():
                                if len(value.get('available_slots', [])) > 0:
                                    if min_key is None or int(key) < int(min_key):
                                        min_key = key

                            if min_key is not None:
                                doc_and_slots.append(dict(
                                    doctorid=data['doctorid'],
                                    firstname=data['firstname'],
                                    lastname=data['lastname'],
                                    gender=data['gender'],
                                    # bio=data['bio'],
                                    graduation=data['graduation'],
                                    masters=data['masters'],
                                    doctortype=data['doctortype'],
                                    specialization=data['specialization'],
                                    languages=data['languages'],
                                    practice_areas=data['practice_areas'],
                                    interest_area= data.get('interest_area', []),
                                    consultation_symptoms= data.get('consultation_symptoms', []),
                                    availability_slotid=data['availability_slotid'],
                                    doctor_profile_picture=data['doctor_profile_picture'],
                                    consultation_duration=int(min_key),
                                    first_available_slot=data['slots'][min_key]['available_slots'][0]
                                ))
                        practice_area_slots['doctors_and_slots'] = sorted(doc_and_slots,
                                                                          key=lambda x: datetime.datetime.strptime(
                                                                              x['first_available_slot'], '%I:%M %p'))

            return practice_areas_and_doctors, 'slots found'


        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting doctors and respective slots'

    def deactivate_doctor_account(self, logged_in_user: str):
        try:
            get_doctor_active_status = self.is_doctor_active(doctor_id=logged_in_user)
            if not get_doctor_active_status:
                raise Exception('Doctor not found')
            self.mongo_db['DoctorsInfo'].find_one_and_update(dict(doctorid=logged_in_user),
                                                             {'$set': dict(is_active=False)})
            return {'message': 'Doctor ID deactivated'}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting doctor id')

    def is_doctor_active(self, doctor_id: str):
        try:
            doctor = self.mongo_db['DoctorsInfo'].find_one(dict(doctorid=doctor_id, is_active=True))
            return doctor
        except Exception as e:
            raise Exception(f'Error occurred as {e} while getting active status for doctor')

    def activate_doctor_account(self, doctor_id: str):
        try:

            self.mongo_db['DoctorsInfo'].find_one_and_update(dict(doctorid=doctor_id),
                                                             {'$set': dict(is_active=True)})
            return {'message': 'Doctor ID activated'}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while activating doctor id')

    def deactivate_doctor_account1(self, doctor_id: str):
        try:
            self.mongo_db['DoctorsInfo'].find_one_and_update(dict(doctorid=doctor_id),
                                                             {'$set': dict(is_active=False)})
            return {'message': 'Doctor ID deactivated'}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deactivating doctor id')

    def logout(self, user_id, data:LogoutRequest):
            try:
                device_collection = self.mongo_db["UserDeviceInfo"]
                if data and data.device_id:
                    device_collection.delete_one({ "user_id": user_id, "device_id": data.device_id, "user_type":"Doctor" })
                # To Do Invalidate user token and other settings if any
                return {f'Doctor Logged out'}, None

            except Exception as e:
                return None, f'{str(e)}'
