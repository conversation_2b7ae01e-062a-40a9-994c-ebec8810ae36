from cgi import test
from datetime import date, datetime, time

from sqlalchemy import Boolean
from .database import Base
from enum import Enum
from typing import Optional, List, Dict, Union
from bson import ObjectId

from pydantic import BaseModel, Field
from .viewmodels import Gender, City
from .doctormodels import ListOfSpecialization


class PatientRelationType(str, Enum):
    Child = 'Child'
    Spouse = 'Spouse'
    Others = 'Others'
    Parent = 'Parent'
    Self = 'Self'
    Family = 'Family'
    Couple = 'Couple'


class MentalHealthPatientRelationType(str, Enum):
    Child = 'Child'
    Spouse = 'Spouse'
    Parent = 'Parent'
    Self = 'Self'


class GetUserById(BaseModel):
    userid: str


class AppointmentType(str, Enum):
    InClinic = 'InClinic'
    Virtual = 'Virtual'


class AppointmentFollowUpType(str, Enum):
    Paid = 'Paid'
    Free = 'Free'


class AppointmentCareType(str, Enum):
    MedicalHealth = 'Medical Health'
    MentalHealth = 'Mental Health'


class UpdateAppointmentStatus(BaseModel):
    appointment_id: str
    status: str
    reason: Optional[str] = ''
    comment: Optional[str] = ''

class PatientList(BaseModel):
    firstname: str
    lastname: str
    dob: str
    email: Optional[str]
    mobile: str
    gender: Gender
    relation: PatientRelationType


class AppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: Optional[List] = []
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: Optional[str]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]
    slot_duration: Optional[int] = 0
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid
    care_type: AppointmentCareType
    is_rescheduled_appointment: Optional[bool] = False
    created_at: Optional[datetime]


class AppointmentBookingByAdmin(BaseModel):
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    patientid: Optional[str]
    clinicid: Optional[str]
    doctorid: Optional[str]
    patients: List[PatientList]
    # firstname: Optional[str]
    # lastname: Optional[str]
    # dob: Optional[str]
    # email: Optional[str]
    # mobile: Optional[str]
    # gender: Optional[Gender]
    appointment_slot: Optional[str]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]
    amount: str
    promo_code: Optional[str] = ''
    slot_duration: Optional[int] = 0
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid
    care_type: AppointmentCareType
    is_rescheduled_appointment: Optional[bool] = False
    appointment_cancellation_details: Optional[UpdateAppointmentStatus] = None
    booked_by: Optional[str] = ''
    created_at: Optional[datetime]


class AppointmentById(BaseModel):
    appointment_id: str


class DeleteAppointmentResponseView(BaseModel):
    appointment_id: str
    patient_id: str
    status: str



class AppointmentBookingForOthers(BaseModel):
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: Optional[str]
    patients: List[PatientList]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]
    slot_duration: Optional[int] = 0
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid
    care_type: AppointmentCareType
    is_rescheduled_appointment: Optional[bool] = False
    created_at: Optional[datetime]


class GuestAppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: Optional[str]
    firstname: str
    lastname: str
    dob: str
    email: Optional[str]
    mobile: str
    gender: Gender
    password: Optional[str]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]
    slot_duration: Optional[int] = 30
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid
    care_type: AppointmentCareType


class GuestAppointmentBookingForOthers(BaseModel):
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: Optional[str]
    firstname: str
    lastname: str
    dob: str
    email: Optional[str]
    mobile: str
    gender: Gender
    password: Optional[str]
    patient_firstname: str
    patient_lastname: str
    patient_dob: str
    patient_email: Optional[str]
    patient_mobile: str
    patient_gender: Gender
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]
    slot_duration: Optional[int] = 30
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid
    care_type: AppointmentCareType


class AppointmentBookingDetails(BaseModel):
    appointment_id: str
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    children_included: Optional[bool] = False
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    doctor_name: str = ''
    appointment_slot: datetime
    is_active: bool = True
    is_confirmed: bool
    payment: Optional[str]
    caseid: Optional[str] | None
    patient_id: str
    patients: Optional[List] = []
    booked_by: str
    created_at: datetime
    end_date: Optional[datetime]
    status: Optional[dict]
    booked_by_name: Optional[str]
    payment_status: Optional[str]
    is_first_appointment: Optional[bool] = False
    case_open: bool = True
    follow_up_type: AppointmentFollowUpType = AppointmentFollowUpType.Paid  # applicable only if the appointment is not first appointment
    care_type: AppointmentCareType
    booked_via: str = "app"
    previous_appointments: List[dict] = Field(default_factory=list)
    is_couple_or_family_therapy: bool = False


class appointmentstatus(BaseModel):
    status: list[str]


class appointmentstatusdel(BaseModel):
    status_id: str


class appointmentreason(BaseModel):
    reason: list[str]


class appointmentreasondel(BaseModel):
    reason_id: str


class AppointmentBookingResponse(BaseModel):
    appointment_id: str
    patient_name: Optional[str] = ''
    caseid: str | None
    appointment_slot: str
    reason_for_visit: List[str]
    # symptoms_audio_clip: Optional[str]
    service_provider: List[dict]
    payment: str
    end_time: str


class RequestGuestAppointmentList(BaseModel):
    firstname: str
    lastname: str
    dob: str
    email: Optional[str]
    mobile: str
    gender: Gender
    starts_from: str
    till: Optional[str]


class ResponseMemberAppointmentList(BaseModel):
    appointment_id: str
    caseid: str | None
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: datetime
    appointment_type: str
    appointment_for: str
    patient: Optional[dict] = {}
    clinic: Optional[dict] = {}
    doctor: Optional[dict] = {}
    additional_notes: Optional[str] | None = ''


class ResponseAppointmentsList(BaseModel):
    appointments: Optional[List] = []


class ResponseGuestAppointmentList(BaseModel):
    appointments: Optional[List[dict]]


class SearchClinicView(BaseModel):
    specialist: str
    city: Optional[City] = None
    lat: Optional[float] = None
    lon: Optional[float] = None
    search_date: Optional[str] = None

    class Config:
        use_enum_values = True


class SearchClinicResponse(BaseModel):
    search_result: Optional[List] = []
    search_status: Optional[str] = ""


class Active(str, Enum):
    Active = 'Active'
    InActive = 'InActive'


class AddSymptomsImageView(BaseModel):
    symptom_name: str
    active_status: Active
    symptom_image_encoded: str
    symptom_image_hexcode: str

    class Config:
        use_enum_values = True


class AddSymptomsImageResponse(BaseModel):
    symptom_id: str
    symptom_name: str
    active_status: str
    symptom_image_url: str
    symptom_image_hexcode: str


class UpdateSymptomsView(BaseModel):
    symptom_id: str
    symptom_name: Optional[str]
    active_status: Optional[Active]
    symptom_image_encoded: Optional[str]
    symptom_image_hexcode: Optional[str]

    class Config:
        use_enum_values = True


class GetSymptomsResponse(BaseModel):
    symptoms: Optional[List] = []
    status: Optional[str] = ""


class CommonAllergies(str, Enum):
    Food = 'Food'
    InsectBite = 'Insect Bite'
    DustPollen = 'Dust/Pollen'
    DrugMedicine = 'Drug/Medicine'


class PreExistingConditions(str, Enum):
    Diabetes = 'Diabetes'
    HeartCondition = 'Heart Condition'
    BloodPressure = 'Blood Pressure'
    HighCholestrol = 'High Cholestrol'
    KidneyDisease = 'Kidney Disease'
    LungDisease = 'Lung Disease'
    Cancer = 'Cancer'
    Dementia = 'Dementia'


class FamilyHistory(str, Enum):
    Diabetes = 'Diabetes'
    HeartDisease = 'Heart Disease'
    BloodPressure = 'Blood Pressure'
    KidneyDisease = 'Kidney Disease'
    BleedingDisorder = 'Bleeding Disorder'
    Cancer = 'Cancer'
    Stroke = 'Stroke'


class PatientHealthHistoryView(BaseModel):
    patient_id: str
    common_allergies: Optional[List[CommonAllergies]] = []
    pre_existing_conditions: Optional[List[PreExistingConditions]] = []
    family_history: Optional[List[FamilyHistory]] = []
    any_additional_information: Optional[str]


class PatientHealthHistoryViewUser(BaseModel):
    common_allergies: Optional[List[CommonAllergies]] = []
    pre_existing_conditions: Optional[List[PreExistingConditions]] = []
    family_history: Optional[List[FamilyHistory]] = []
    any_additional_information: Optional[str]


class AddPatientHealthHistoryResponseView(BaseModel):
    patient_id: str
    status: str


class GetPatientHealthHistoryView(BaseModel):
    patient_id: str


class ReportUploader(str, Enum):
    Admin = 'admin'
    Patient = 'patient'


class ReportType(str, Enum):
    Blood = 'blood'
    ECG = 'ECG'
    Urine = 'urine'
    X_Ray = 'x-ray'
    CT_Scan = 'ct scan'
    Ultrasound = 'ultrasound'


class UploadReportRequestView(BaseModel):
    caseid: str | None
    report_name: str
    report_type: ReportType
    lab_name: str
    report_uploaded_by: ReportUploader
    report_image_encoded: str
    report_generated_date: str

    class Config:
        use_enum_values = True


class ReportView(BaseModel):
    report_id: str
    userid: str
    report_name: str
    report_type: ReportType
    lab_name: str
    report_uploaded_by: ReportUploader
    report_generated_date: str
    report_uploaded_date: str
    report_image_url: str


class GetPatientReportView(BaseModel):
    caseid: str | None


class GetPatientReportResponseView(BaseModel):
    caseid: str | None
    reports: Optional[List]


class UpdateReportRequestView(BaseModel):
    caseid: str | None
    report_id: str
    report_name: Optional[str]
    report_type: Optional[ReportType]
    lab_name: Optional[str]
    report_uploaded_by: Optional[ReportUploader]
    report_image_encoded: Optional[str]
    report_generated_date: Optional[str]

    class Config:
        use_enum_values = True


class UpdateReportResponseView(BaseModel):
    fields_updated: Optional[Dict]
    status: str


class DeleteReportRequestView(BaseModel):
    caseid: str | None
    report_id: str
    user: ReportUploader

    class Config:
        use_enum_values = True


class DeleteReportResponseView(BaseModel):
    report_deleted_info: Optional[Dict]
    status: str


class PatientFeedbackForDoctor(BaseModel):
    caseid: str | None
    rating: float
    feedback: Optional[str]


class PatientFeedbackForDoctorResponse(BaseModel):
    patientid: str
    doctorid: str
    caseid: str | None
    rating: float
    date: str
    feedback: Optional[str]


class GetDoctorFeedback(BaseModel):
    doctorid: str


class GetDoctorFeedbackResponseView(BaseModel):
    total_feedback: Optional[List]
    status: Optional[str]


class SearchAllClinicView(BaseModel):
    specialist: str
    city: Optional[City]
    lat: Optional[float]
    lon: Optional[float]

    # search_date: Optional[str] = None
    # clinicid: Optional[str] = None

    class Config:
        use_enum_values = True


class SearchClinicIdView(BaseModel):
    specialist: str
    clinicid: str
    search_date: str

    class Config:
        use_enum_values = True


class SearchClinicIdResponse(BaseModel):
    search_result: Optional[Dict] = {}
    search_status: Optional[str] = ""


class SortingType(str, Enum):
    asc = 'asc'
    desc = 'desc'


class GetAppointmentView(BaseModel):
    date: Optional[str] = None
    name: Optional[str] = None
    mobile: Optional[str] = None
    appointment_status: Optional[str] = None
    skip: Optional[int] = 0
    doc_id: Optional[str] = None
    case_id: Optional[str] = None
    exclude_id: Optional[str] = None
    sort: Optional[Dict] = None
    appointment_id: Optional[str] = None
    patient_id: Optional[str] = None


class SlotBlockingView(BaseModel):
    slot_id: str
    block_slot: str
    doctor_id: str


class SlotBlockingResponse(BaseModel):
    slot_id: str
    created_at: datetime
    expiry_at: float


class CheckSlotStatus(BaseModel):
    slot_id: str
    block_slot: str
    doctor_id: str
    slot_status: str


class ResponseMemberAppointmentListCopy(BaseModel):
    appointment_id: str
    caseid: str | None
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: datetime
    slot_duration: Optional[int] = 0
    end_date: datetime
    appointment_type: str
    appointment_for: str
    patient: Optional[List[dict]] = []
    patient_name: Optional[str] = ""
    clinic: Optional[dict] = ''
    doctor: Optional[dict] = {}
    additional_notes: Optional[str] | None = ''
    meeting_link: Optional[str] = {}
    appointment_status: Optional[str] = ''
    booking_cost: Optional[str] = ''


class GetSymptomsById(BaseModel):
    symptom_id: str


class DeleteSymptomsImageResponse(BaseModel):
    symptom_id: str
    symptom_name: str
    status: str


class GetAppointmentById(BaseModel):
    appointment_id: str


class AppointmentBookingDetailsCopy(BaseModel):
    appointment_id: str
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: datetime
    is_active: bool = True
    is_confirmed: bool
    payment: Optional[str]
    caseid: Optional[str]
    patient_id: str
    booked_by: Optional[str]


class AppointmentBookingDetailsCopy1(BaseModel):
    appointment_id: str
    appointment_type: AppointmentType
    appointment_for: PatientRelationType
    symptoms: List[str]
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: Optional[str]
    appointment_slot: Optional[str]
    is_active: bool = True
    is_confirmed: bool
    payment: Optional[str]
    caseid: Optional[str]
    patient_id: str
    booked_by: Optional[str]


class CheckDoctorAvailableSlot(BaseModel):
    doctorid: str
    clinicid: str
    search_date: str


class CheckMappingForClinic(BaseModel):
    mappingid: str


class JitsiMeetingInfo(BaseModel):
    meeting_id: str
    meeting_link: str
    meeting_codes: List = []
    appointment_id: str
    case_id: str | None
    doctor_id: str
    patient_ids: List = []
    appointment_slot: datetime
    # doctor_joining_link: Optional[str] = ''
    # patient_joining_link: Optional[str] = ''
    doctor_meeting_code: Optional[str] = ''
    # role: Optional[int] = None
    # role_name: Optional[str] = None
    # patient_name: Optional[str] = None
    # doctor_name: Optional[str] = None
    slot_duration: Optional[int] = None
    patient_details: List = []
    doctor_details: dict = {}
    end_date: datetime
class MeetingJoiningInfo(BaseModel):
    meeting_id: str
    meeting_link: str
    meeting_code: str
    appointment_id: str
    case_id: str | None
    doctor_id: str
    patientid: str
    appointment_slot: datetime
    # doctor_joining_link: Optional[str] = ''
    # patient_joining_link: Optional[str] = ''
    doctor_meeting_code: Optional[str] = ''
    role: Optional[int] = None
    role_name: Optional[str] = None
    patient_name: Optional[str] = None
    doctor_name: Optional[str] = None
    slot_duration: Optional[int] = None
    patients_details: List = []
    doctor_details: dict = {}


class CheckJitsiMeetingCode(BaseModel):
    meeting_code: str


class CheckJitsiMeetingAppointmentId(BaseModel):
    appointment_id: str


class PatientVitalsHeartRate(BaseModel):
    heart_rate: int
    reading_date: Optional[str]


class PatientVitalsWeight(BaseModel):
    weight: float
    reading_date: Optional[str]


class PatientVitalsBloodPressure(BaseModel):
    systole: int
    diastole: int
    reading_date: Optional[str]


class PatientVitalsHeight(BaseModel):
    height: float
    reading_date: Optional[str]


class PatientVitalsTemperature(BaseModel):
    temperature: float
    reading_date: Optional[str]


class PatientVitalsHba1c(BaseModel):
    hba1c: float
    reading_date: Optional[str]


class PatientVitalsHeartRateReadingId(BaseModel):
    reading_id: str
    heart_rate: Optional[int]
    reading_date: Optional[str]


class PatientVitalsWeightReadingId(BaseModel):
    reading_id: str
    weight: Optional[float]
    reading_date: Optional[str]


class PatientVitalsBloodPressureReadingId(BaseModel):
    reading_id: str
    systole: Optional[int]
    diastole: Optional[int]
    reading_date: Optional[str]


class PatientVitalsHeightReadingId(BaseModel):
    reading_id: str
    height: Optional[float]
    reading_date: Optional[str]


class PatientVitalsTemperatureReadingId(BaseModel):
    reading_id: str
    temperature: Optional[float]
    reading_date: Optional[str]


class PatientVitalsHba1cReadingId(BaseModel):
    reading_id: str
    hba1c: Optional[float]
    reading_date: Optional[str]


class PatientVitalsInfoDateReadingId(BaseModel):
    heart_rate_readings: Optional[PatientVitalsHeartRateReadingId]
    weight_readings: Optional[PatientVitalsWeightReadingId]
    blood_pressure_readings: Optional[PatientVitalsBloodPressureReadingId]
    height_readings: Optional[PatientVitalsHeightReadingId]
    temperature_readings: Optional[PatientVitalsTemperatureReadingId]
    hba1c_readings: Optional[PatientVitalsHba1cReadingId]


class PatientVitalsInfoDate(BaseModel):
    heart_rate_readings: Optional[PatientVitalsHeartRate]
    weight_readings: Optional[PatientVitalsWeight]
    blood_pressure_readings: Optional[PatientVitalsBloodPressure]
    height_readings: Optional[PatientVitalsHeight]
    temperature_readings: Optional[PatientVitalsTemperature]
    hba1c_readings: Optional[PatientVitalsHba1c]


class PatientVitalsInfo(BaseModel):
    heart_rate: Optional[int] = None
    weight: Optional[float] = None
    blood_pressure: Optional[str] = None
    bmi: Optional[float] = None
    temperature: Optional[float] = None
    hba1c: Optional[float] = None


class PatientVitalsInfoUserId(BaseModel):
    userid: str
    heart_rate: Optional[int] = None
    weight: Optional[float] = None
    blood_pressure: Optional[str] = None
    bmi: Optional[float] = None
    temperature: Optional[float] = None
    hba1c: Optional[float] = None


class PatientVitalsInfoWithBMI(BaseModel):
    userid: str
    heart_rate: Optional[List]
    weight: Optional[List]
    blood_pressure: Optional[List]
    height: Optional[List]
    bmi: Optional[float] = None
    temperature: Optional[List]
    hba1c: Optional[List]


class UploadPrescriptionRequestView(BaseModel):
    caseid: str | None
    prescription_name: str
    prescription_image_encoded: str
    prescription_date: str


class PrescriptionView(BaseModel):
    prescription_id: str
    prescription_name: str
    prescription_image_url: str
    prescription_date: datetime
    prescription_upload_date: datetime
    prescription_update_date: datetime


class UpdatePrescriptionRequestView(BaseModel):
    caseid: str | None
    prescription_id: str
    prescription_name: Optional[str]
    prescription_image_encoded: Optional[str]
    prescription_date: Optional[str]


class DeletePrescriptionRequestView(BaseModel):
    caseid: str | None
    prescription_id: str


class GetPatientPrescription(BaseModel):
    caseid: str | None
    prescriptions: Optional[List]


class PayloadDict(BaseModel):
    primary_userid: str
    relative_userid: str
    relation_type: str
    permission: bool


class AddPolicyPayloadResponse(BaseModel):
    primary_userid: str
    relative_userid: str
    relation_type: str
    permission: bool
    updated_by: str


class RelativePermData(BaseModel):
    RelativeUserId: str
    RelativeAPIPermsData: dict
    RelationType: Optional[str]


class PrimaryMemberPolicyData(BaseModel):
    PrimaryMemberUserId: str
    PrimaryMemberPermData: List[dict]


class AddFamilyView(BaseModel):
    FamilyMembersUserIds: List[str]
    FamilyID: str


class AddFamilyResponse(BaseModel):
    FamilyMembersUserIds: List[str]
    FamilyID: str
    FamilyPermsData: List[dict]


class FamilyCodeGen(BaseModel):
    user_id: str
    unique_code: str
    created_at: datetime
    expiry_at: float


class CaretakerAccessCodeGen(BaseModel):
    user_id: str
    unique_code: str
    relation_id: str
    consent_duration: int
    created_at: datetime
    expiry_at: float


class RelativeType(str, Enum):
    Child = 'Child'
    Spouse = 'Spouse'


class AddMemberToFamilyView(BaseModel):
    relative_id: str
    relation_type: RelativeType


class AddFamilyIDRelative(BaseModel):
    user_id: str
    family_id: str


class AddFamilyIDRelativeResponse(BaseModel):
    family_id: str
    members: List[str]


class DeleteVirtualSlotsView(BaseModel):
    doctorid: str


class DeleteVirtualSlotsResponse(BaseModel):
    doctorid: str
    status: str


class AddRelativeToFamilyView(BaseModel):
    relative_code: str
    relation_type: str


class CreateNewFamilyView(BaseModel):
    relative_code: str
    relation_type: str


class CreateNewFamilyResponse(BaseModel):
    family_key: str
    familyid: str
    created_at: str
    created_by: str


class CreateNewRelativeResponse(BaseModel):
    mappingid: str
    userid: str
    familyid: str
    added_on: str


class DataAccessView(BaseModel):
    access_to: str
    months_of_access: float


class DataAccessResponse(BaseModel):
    access_key: str
    access_from: str
    access_to: str
    expiry_in: str


class GetRelativeToken(BaseModel):
    relative_userid: str


class GetRelativeTokenResponse(BaseModel):
    bearer_token: str
    token_type: str


class AddRelationResp(BaseModel):
    relation_key: str
    relative_a: str
    relative_b: str
    relation_type: str


class AdminAddRelativeToFamilyView(BaseModel):
    userid: str
    relative_code: str
    relation_type: str


class PatientProfileImageView(BaseModel):
    userid: str
    image_id: str
    profile_image_url: str
    update_date: datetime


class UploadPatientProfileImages(BaseModel):
    profile_image_encoded: str


class UploadPatientProfileImagesByAdmin(BaseModel):
    userid: str
    profile_image_encoded: str


class CheckIFAccessRequest(BaseModel):
    access_from: str
    access_to: str


class AdminFamilyCodeGenRequest(BaseModel):
    userid: str


class AdminGetUserFamilyRequest(BaseModel):
    userid: str


class RemoveFamilyMemberRequest(BaseModel):
    relative_userid: str


class AdminRemoveFamilyMemberRequest(BaseModel):
    userid: str
    relative_userid: str


class PatientVitalsInfoDateCopy(BaseModel):
    # caseid : str
    heart_rate_readings: Optional[PatientVitalsHeartRate]
    weight_readings: Optional[PatientVitalsWeight]
    blood_pressure_readings: Optional[PatientVitalsBloodPressure]
    height_readings: Optional[PatientVitalsHeight]
    temperature_readings: Optional[PatientVitalsTemperature]
    hba1c_readings: Optional[PatientVitalsHba1c]
    extra_notes: Optional[List]


class FamilyDoctorAdd(BaseModel):
    doctor_id: str


class FamilyDoctorAddByAdmin(BaseModel):
    user_id: str
    doctor_id: str


class FamilyDoctorAddView(BaseModel):
    user_id: str
    doctor_id: str
    user_name: str
    doctor_name: str


class DoctorAppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    symptoms: List[str]
    clinicid: str
    appointment_slot: str
    patient_id: str
    additional_notes: Optional[str] | None = ''


class DoctorAppointmentBookingResponse(BaseModel):
    appointment_id: str
    patient_name: str
    caseid: str | None
    appointment_slot: str
    reason_for_visit: List[str]
    service_provider: List[dict]
    payment: str


class DoctorClinicAvailableSlot(BaseModel):
    clinicid: str
    search_date: str


class PatientDetails(BaseModel):
    patient_firstname: str
    patient_lastname: str
    patient_dob: str
    patient_email: str
    patient_mobile: str
    patient_gender: str


class RelativeData(BaseModel):
    firstname: str
    lastname: str
    dob: str
    email: str
    mobile: str
    gender: str
    appointment_for: PatientRelationType


class MentalHealthAppointmentBookingDetails(BaseModel):
    appointment_id: str
    appointment_type: AppointmentType
    appointment_for: Optional[MentalHealthPatientRelationType]
    children_included: bool = False
    symptoms: Optional[List] = []
    symptoms_audio_clip: Optional[str]
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: str
    appointment_slot: datetime
    is_active: bool = True
    is_confirmed: bool
    payment: Optional[str]
    caseid: Optional[str] | None
    patient_id: str
    patients: List[str]
    booked_by: str
    created_at: datetime
    end_date: Optional[datetime]


class MentalHealthSelfAppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    appointment_for: MentalHealthPatientRelationType
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: str
    appointment_slot: Optional[str]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]


class MentalHealthOthersAppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    appointment_for: MentalHealthPatientRelationType
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: str
    appointment_slot: Optional[str]
    patients: List[PatientDetails]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]


class MentalHealthGroupAppointmentBooking(BaseModel):
    appointment_type: AppointmentType
    patients: List[PatientDetails]
    children_included: bool
    additional_notes: Optional[str] | None = ''
    clinicid: Optional[str]
    doctorid: str
    appointment_slot: Optional[str]
    is_active: bool = True
    is_confirmed: bool = True
    payment: Optional[str]


class MentalHealthAppointmentBookingResponse(BaseModel):
    patient_details: List[dict]
    caseid: str | None
    appointment_slot: str
    service_provider: List[dict]
    payment: str


class PatientId(BaseModel):
    patient_id: str


class NotificationPushMessage(BaseModel):
    appointment_id: str
    caseid: str | None
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: str
    appointment_type: str
    appointment_for: str
    patient: Optional[dict] = {}
    clinic: Optional[dict] = ''
    doctor: Optional[dict] = {}
    additional_notes: Optional[str] | None = ''
    meeting_link: Optional[str] = {}
    meeting_code: Optional[str] = ''


class AppDownloadLink(BaseModel):
    mobile_number: str


class AppointmentConfirmationToAdmin(BaseModel):
    appointment_id: str
    doctor_name: str
    doctor_specialization: str
    doctor_details: dict
    patient_name: str
    appointment_date: str
    appointment_time: str
    booking_person_name: Optional[str] = ''
    is_rescheduled_appointment: Optional[bool] = False


class PatientsRelatives(BaseModel):
    patient_id: Optional[str]
    relation: Optional[str] = None


class AppointmentCancellationModel(BaseModel):
    appointment_id: str


class RequestPrescriptionList(BaseModel):
    patient_id: Optional[str] = ''


class ConsentRecord(BaseModel):
    relation_id: str
    consent_duration: str
    to_remove: bool


class DeleteFamilyRelation(BaseModel):
    relation_id: str


class LogoutRequest(BaseModel):
    device_id: Optional[str]


class UIUsersView(BaseModel):
    name: Optional[str] = None
    mobile: Optional[str] = None
    email: Optional[str] = None
    sort_order: Optional[SortingType] = None
    isRegistered: bool = None
    page_no: int = 1
    page_size: int = None


class PatientProfileMerge(BaseModel):
    user_ids: List[str]
    firstname: str
    lastname: str
    email: str
    mobile: str
