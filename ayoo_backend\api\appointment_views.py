from fastapi import Depends, <PERSON><PERSON><PERSON>er, HTTPException
from .patient_models import AppointmentBookingByAdmin, AppointmentType
from .services.auth import get_user_from_token
from .DAOs.appointmentDAO import AppointmentDAO, UpcomingAppointmentsQueryFields, UpcomingAppointmentsSort, GetAppointmentById, upcoming_appointment_aggregation
from .DAOs.userDAO import UserDA<PERSON>
from .services.paginators import LimitOffsetPaginator
from .DTOs.appointmentDTO import UpcomingAppointmentDTO, CancelAppointmentPayload, RescheduleAppointmentPayload
from .ayoo_utils import check_if_past_24_hrs_prior, check_if_past_6_hrs_prior, check_if_past_1_hr_prior
from .DataModels.appointment import Appointment, AppointmentStatus, Status
from .patient_controller import PatientController
from .jitsi_meet import JitsiMeetController
from .firebase_controller import FireBaseNotificationController
from .mongodb import mongodb_conn
from .database import get_db
from uuid import uuid4
from datetime import datetime


appointments_router = APIRouter(prefix = "/users", dependencies=[Depends(get_user_from_token)])



@appointments_router.get("/upcoming_appointments")
async def get_upcoming_appointments(upcoming_appointment_query_fields: UpcomingAppointmentsQueryFields = Depends(),
                                     user_id = Depends(get_user_from_token),
                                       paginator: LimitOffsetPaginator = Depends(),
                                         sort: UpcomingAppointmentsSort = Depends()): #-> list[UpcomingAppointmentDTO]:
    upcoming_appointment_query_fields.set_user_id(user_id)
 #   appointments = AppointmentDAO().list(skip = paginator.offset, limit = paginator.limit, filter = upcoming_appointment_query_fields, sort = sort)
    pipeline = upcoming_appointment_aggregation
    pipeline[-4]["$match"] = upcoming_appointment_query_fields.apply_filters(upcoming_appointment_query_fields, AppointmentDAO())
    pipeline[-3]["$skip"] = paginator.offset
    pipeline[-2]["$limit"] = paginator.limit
    appointments = [Appointment(**x) for x in AppointmentDAO().execute_pipeline(pipeline)]
    # print([x.appointment_id for x in appointments])
    try:
      return UpcomingAppointmentDTO.create_from_appointment_list(appointments)
    except Exception as e:
       return HTTPException(400, str(e))


@appointments_router.post("/cancel_appointment")
async def cancel_appointment(payload: CancelAppointmentPayload, appointment_id, user_id = Depends(get_user_from_token)):
    appointment = AppointmentDAO().list(filter = GetAppointmentById(appointment_id = appointment_id))[0]
    if appointment.status.status != "Booked":
        raise HTTPException(400, "Appointment is not in 'Booked' status!!!!")
    if check_if_past_24_hrs_prior(appointment.appointment_slot):
        raise HTTPException(400, "Cannot cancel within 24 hours of appointment slot!!!!")
    if not appointment.user_cancellation_allowed:
        raise HTTPException(400, "User not allowed to cancel this appointment!!!!")
    modified_status = Status(status = AppointmentStatus.cancelled,Reason = payload.reason, comment = payload.comment)
    appointment.status = modified_status
    appointment.is_active = False
    appointment.is_confirmed = False
    try:

        mongodb_conn["ayoo"]["Notifications"].update_many({"object_id": appointment_id},
                                                       {
                                                           "$set": {"notification_status": True}
                                                       })
        PatientController(get_db(), mongodb_conn).handle_notification_for_cancelled_appointment(appointment_id)
        object_id = appointment.id
        del appointment.id
        return AppointmentDAO().update(_id = object_id, _obj = appointment)
    except Exception as e:
        raise HTTPException(400, str(e))

@appointments_router.post("/reschedule_appointment")
async def reschedule_appointment(payload: RescheduleAppointmentPayload, appointment_id, user_id = Depends(get_user_from_token)):
    user = UserDAO().get(user_id)
    appointment: Appointment = AppointmentDAO().list(filter = GetAppointmentById(appointment_id = appointment_id))[0]
    if appointment.status.status != "Booked":
        raise HTTPException(400, "Appointment not in 'Booked' status!!!!")
    if check_if_past_6_hrs_prior(appointment.appointment_slot):
        raise HTTPException(400, "Cannot reschedule within 6 hours of appointment slot!!!!")
    if not appointment.user_reschedule_allowed:
        raise HTTPException(400, "User cannot reschedule an already rescheduled appointment!!!!")
    object_id = appointment.id
    del appointment.id
    duration = appointment.end_date - appointment.appointment_slot
    new_appointment = Appointment(**appointment.dict())
    new_appointment.appointment_id = str(uuid4())
    new_appointment.appointment_type = payload.appointment_type
    new_appointment.appointment_slot = payload.appointment_slot
    new_appointment.end_date = new_appointment.appointment_slot + duration
    new_appointment.user_reschedule_allowed = False
    new_appointment.user_cancellation_allowed = False
    new_appointment.previous_appointments.append({
        "appointment_id": appointment_id,
        "status": AppointmentStatus.reschedule,
        "reason": payload.reason,
        "comment": payload.comment,
        "appointment_slot": appointment.appointment_slot,
        "booked_by": appointment.booked_by,
        "appointment_type": appointment.appointment_type,
        "updated_by": user_id,
        "booked_by_name": appointment.booked_by_name,
        "updated_by_name": " ".join([user.firstname, user.lastname]),
        "rescheduled_date": datetime.now()

    })
    modified_status = Status(status = AppointmentStatus.reschedule, Reason = payload.reason, comment = payload.comment, next_appointment_id = new_appointment.appointment_id)
    appointment.status = modified_status
    appointment.payment = 0
    appointment.is_active = False
    appointment.is_confirmed = False
    mongodb_conn["ayoo"]["Notifications"].update_many({"object_id": appointment_id},
                                                       {
                                                           "$set": {"notification_status": True}
                                                       })
    patient_controller = PatientController(get_db(), mongodb_conn)

    create = AppointmentDAO().create(_obj = new_appointment)

    if payload.appointment_type == AppointmentType.Virtual:
        jitsi_ctrl = JitsiMeetController(db=get_db(), mongo=mongodb_conn)
        jitsi_ctrl.create_meeting_link(
            appointment_id=new_appointment.appointment_id,
            case_id=new_appointment.caseid,
            doctorid=str(new_appointment.doctorid),
            patient_ids=[str(new_appointment.patient_id)],
            appointment_slot=new_appointment.appointment_slot
        )

    notifications = patient_controller.manage_notifications_for_booked_appointments(appointment_id = new_appointment.appointment_id, is_rescheduled_appointment = True, previous_appointment_id=appointment_id)

    update = AppointmentDAO().update(_id = object_id, _obj = appointment)
    create["appointment_id"] = new_appointment.appointment_id
    return {"create": create, "update": update, "notifications": notifications}

@appointments_router.get("/toggle_type")
async def toggle_type(appointment_id, user_id = Depends(get_user_from_token)):
    user = UserDAO().get(user_id)
    appointment: Appointment = AppointmentDAO().list(filter = GetAppointmentById(appointment_id = appointment_id))[0]
    if appointment.status.status != "Booked":
        raise HTTPException(400, "Appointment not in 'Booked' status!!!!")
    if check_if_past_1_hr_prior(appointment.appointment_slot):
        raise HTTPException(400, "Cannot change appointment type within 1 hour of appointment slot!!!!")
    object_id = appointment.id
    del appointment.id
    appointment.toggle_type()

    pc = PatientController(db = get_db(), mongo = mongodb_conn)
    is_doctor_available = pc.check_for_block_and_available_slots_in_toggle(
        doctor_id=appointment.doctorid,
        appointment_type=appointment.appointment_type,
        appointment_slot=appointment.appointment_slot,
        end_time=appointment.end_date)

    if not is_doctor_available:
        raise HTTPException(status_code=409, detail=f'Cannot toggle: Doctor is not available for {appointment.appointment_type} consultation')

    update = AppointmentDAO().update(_id = object_id, _obj = appointment)
    jitsi_ctrl = JitsiMeetController(db=get_db(), mongo=mongodb_conn)
    if appointment.appointment_type == AppointmentType.Virtual:
        if jitsi_ctrl.check_meeting_using_appointment_id(appointment_id = appointment.appointment_id) is None:
            jitsi_ctrl.create_meeting_link(
                appointment_id=appointment.appointment_id, case_id=appointment.caseid,
                doctorid=str(appointment.doctorid),
                patient_ids=[str(appointment.patient_id)],
                appointment_slot=appointment.appointment_slot
            )

        jitsi_ctrl.remove_meeting_info(appointment_id=appointment_id)

    pc = PatientController(db = get_db(), mongo = mongodb_conn)
    notifications = pc.manage_notifications_for_booked_appointments(appointment_id = appointment.appointment_id, is_rescheduled_appointment = True, previous_appointment_id=appointment_id)
    return {"update": update, "notifications": notifications}


@appointments_router.get("/appointment/{appointment_id}")
async def get_appointment_details(appointment_id: str):
    appointment = AppointmentDAO().list(filter = GetAppointmentById(appointment_id = appointment_id))
    appointment_details = UpcomingAppointmentDTO.create_from_appointment_list(appointment)
    return appointment_details[0]
    
