import random
import uuid
import datetime
from datetime import timedelta
import calendar
import copy
import enum
import time
from typing import Optional

import sqlalchemy.exc
import json
from sqlalchemy.orm import scoped_session
from sqlalchemy import or_

from . import dbmodels
from ayoo_backend.api.api_configs import WEB_URL_PATH
from ayoo_backend.api.aws_msg_email_generator import AWSEmailAndMsgSender
from ayoo_backend.api.aws_s3 import AWSS3Client
from ayoo_backend.api.chat_models import SaveChatModel, GetChat, SaveChat, CloseChat, GetUsersAllChat, BroadcastMessage
from ayoo_backend.api.dbmodels import DBAdmin
from ayoo_backend.api.doctor_controller import Doctor<PERSON>ontroller
from ayoo_backend.api.firebase_controller import FireBaseNotificationController
from ayoo_backend.api.firebase_models import ChatEvent
from ayoo_backend.api.view_controller import UserController, AdminController


from fastapi import WebSocket, HTTPException

from .text_local_service.text_local_controller import TextLocal<PERSON>ontroller


class ChatController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def get_count(self):
        current_datetime = datetime.datetime.now()
        seven_days_ago = current_datetime - timedelta(days=7)
        midnight_time = seven_days_ago.replace(hour=0, minute=0, second=0, microsecond=0)
        output_format = "%Y-%m-%d"
        # Format the date and time in the required format
        formatted_date_time = midnight_time.strftime(output_format)
        #,"6706af22-36f8-496a-836d-75c83479dd37"
        resp=list(self.mongo_db['ChatMessages'].find({"user_two_id": {"$nin": ["268dcfcf-e3ad-4d51-b505-4cd5283344f6","caa4bde2-821a-4bb1-952d-e226eca712dd","46fadf37-51b4-47ec-9610-361884263d89","3782522e-0b50-42dd-b5ca-a9ae96f7ecec"]}}))
        resp1=list(self.mongo_db['Appointments'].find({"patient_id": {"$nin": ["268dcfcf-e3ad-4d51-b505-4cd5283344f6","caa4bde2-821a-4bb1-952d-e226eca712dd","46fadf37-51b4-47ec-9610-361884263d89","3782522e-0b50-42dd-b5ca-a9ae96f7ecec"]}}))
        # print(resp)
        total_count=0
        # logger.info(formatted_date_time)
        #print(type(formatted_date_time))
        dr={}
        chat_list=[]
        for res in resp:

            date=res['chat_history'][0]['date']
            
            date=date.strftime(output_format)
            if (res['chat_meta']['chat_type'] =="Welcome Message" and  date>=formatted_date_time and res['user_two_id'] not in chat_list if 'user_two_id' in res else False):
                # logger.info(date)
                if date in dr.keys():
                    dr[date]+=1
                    #print(dr[date])
                else:
                    dr[date]=1
                total_count+=1
            if 'user_two_id' in res:
                chat_list.append(res['user_two_id'])
        user_count=total_count
        total_count = 0

        li=[]
        date_format = "%Y-%m-%d"
        
        current_date = datetime.datetime.strptime(current_datetime.strftime(output_format), date_format)

        last_7_days_dates = [current_date - timedelta(days=i) for i in range(7)]
        formatted_dates = [date.strftime(date_format) for date in last_7_days_dates]

        for date in formatted_dates:
            if date not in dr:
                dr[date]=0
        dr = dict(sorted(dr.items()))

        for key,values in dr.items():
            dic={}
            dic['date']=key
            dic['user_count']=values
            li.append(dic)
        #print(li)

        dr={}
        for res in resp1:

            date=res['created_at']
            
            date=date.strftime(output_format)
            if (date>=formatted_date_time):
                # logger.info(date)
                if date in dr.keys():
                    dr[date]+=1
                else:
                    dr[date]=1
                total_count+=1
        app_count=total_count

        li1=[]
        

        for date in formatted_dates:
            if date not in dr:
                dr[date]=0
        dr = dict(sorted(dr.items()))
        for key,values in dr.items():
            dic={}

            dic['date']=key
            dic['app_count']=values
            li1.append(dic)
        #print(li1)

        dic={}
        dic['appointment']=li1
        dic['user']=li
        dic['user_count']=user_count
        dic['appointment_count']=app_count
        
        return dic


    def upload_attachment(self, attachment_encoded: str):
        try:
            s3_instance = AWSS3Client()
            image_id = str(uuid.uuid4())
            generated_url, msg = s3_instance.upload_chat_attachment_to_s3(
                image_str=str(attachment_encoded), image_id=str(image_id))
            if generated_url is None:
                return None, msg
            attachment_info = dict(
                image_id=image_id,
                attachement_url=generated_url,
            )
            return attachment_info, 'Upload success'

        except Exception as e:
            raise Exception(f'Attachment upload error: {str(e)}')

    def get_user_details(self, chat_data: SaveChatModel):
        try:
            ctrl = UserController(db=self.db, otp_generator=None)
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

            sender_type = ''
            recipient_type = ''

            sender_name = ''
            recipient_name = ''

            user_details = ctrl.get_user_details(userid=chat_data.sender_id, mongo=self.mongo)

            if user_details is not None:
                sender_type = 'Patient'
                sender_name = user_details['firstname']

                doctor_details = doctor_ctrl.get_doctor_by_id(doctorid=chat_data.recipient_id)
                if doctor_details is not None:
                    recipient_type = 'Doctor'
                    recipient_name = doctor_details.firstname
            else:
                user_details = ctrl.get_user_details(userid=chat_data.recipient_id, mongo=self.mongo)
                if user_details is not None:
                    recipient_type = 'Patient'
                    recipient_name = user_details['firstname']

                doctor_details = doctor_ctrl.get_doctor_by_id(doctorid=chat_data.sender_id)
                if doctor_details is not None:
                    sender_type = 'Doctor'
                    sender_name = doctor_details.firstname

            user_data = dict(sender_name=sender_name, sender_type=sender_type, recipient_name=recipient_name,
                             recipient_type=recipient_type)

            return user_data, 'User Details Found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while saving chat message'

    def get_user_and_admin_details(self, chat_data: SaveChatModel):
        try:
            ctrl = UserController(db=self.db, otp_generator=None)
            admin_ctrl = AdminController(db=self.db, mongo=self.mongo)

            sender_type = ''
            recipient_type = ''

            sender_name = ''
            recipient_name = ''

            user_details = ctrl.get_user_details(userid=chat_data.sender_id, mongo=self.mongo)
            if user_details is not None:
                sender_type = 'Patient'
                sender_name = user_details['firstname']

                admin_details = admin_ctrl.get_admin_by_id(userid=chat_data.recipient_id)
                if admin_details is not None:
                    recipient_type = 'Admin'
                    recipient_name = admin_details.email
            else:
                user_details = ctrl.get_user_details(userid=chat_data.recipient_id, mongo=self.mongo)
                if user_details is not None:
                    recipient_type = 'Patient'
                    recipient_name = user_details['firstname']

                admin_details = admin_ctrl.get_admin_by_id(userid=chat_data.sender_id)
                if admin_details is not None:
                    sender_type = 'Admin'
                    sender_name = admin_details.email

            user_data = dict(sender_name=sender_name, sender_type=sender_type, recipient_name=recipient_name,
                             recipient_type=recipient_type)

            return user_data, 'User Details Found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while saving chat message'

    def check_existing_open_case(self, patient_id: str, doctor_id: str):
        try:
            val = self.mongo_db['Appointments'].find_one({"$and": [
                {"patient_id": str(patient_id)},
                {"doctorid": str(doctor_id)},
                {"is_active": True}]})
            if val is not None:
                return val['caseid']

            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

            if 'psychiatrist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_doctor"] == doctor_id and case['is_open'] == True),
                    None
                )

                if mental_health_case is not None:
                    return mental_health_case['case_id']

            if 'therapist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["therapist_prescriptions"] if
                     case["case_doctor"] == doctor_id and case['is_open'] == True),
                    None
                )
                if mental_health_case is not None:
                    return mental_health_case['case_id']

            return None

        except Exception as e:
            return str(e)

    def save_chat(self, chat_data: SaveChatModel):

        try:
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            mongo_collection = self.mongo_db['ChatMessages']

            user_data, data_msg = self.get_user_details(chat_data=chat_data)

            if (user_data['sender_type'] == 'Patient' and user_data['recipient_type'] == 'Doctor') or (
                    user_data['recipient_type'] == 'Patient' and user_data['sender_type'] == 'Doctor'):
                save_to_existing_chat_if_exists, msg = self.add_to_existing_chat(chat_data=chat_data)

                if save_to_existing_chat_if_exists is None:
                    case_id = None
                    if (user_data['recipient_type'] == 'Patient' and user_data['sender_type'] == 'Doctor'):
                        isFamilyDoctor = self.mongo_db['UserCollection'].find_one({"$and": [
                            {"family_doctor": str(chat_data.sender_id)},
                            {"userid": str(chat_data.recipient_id)}]})

                        if not isFamilyDoctor:
                            return None, 'Doctor is not a family doctor. Chat not allowed.'

                    if (user_data['sender_type'] == 'Patient' and user_data['recipient_type'] == 'Doctor'):

                        isCaseOpen = self.check_existing_open_case(patient_id=chat_data.sender_id,
                                                                   doctor_id=chat_data.recipient_id)

                        if isCaseOpen is None:
                            return None, 'No open case with doctor. Chat not allowed.'
                        case_id = isCaseOpen

                    uid = str(uuid.uuid4())

                    attachment_info = None
                    if chat_data.attachment_encoded:
                        attachment_info, msg = self.upload_attachment(chat_data.attachment_encoded)
                        if attachment_info is None:
                            return None, msg

                    save_chat_msg = SaveChat(chat_id=uid,
                                             user_one_id=chat_data.sender_id,
                                             user_two_id=chat_data.recipient_id,
                                             user_one_details={'user_type': user_data['sender_type'],
                                                               'user_name': user_data['sender_name']},
                                             user_two_details={'user_type': user_data['recipient_type'],
                                                               'user_name': user_data['recipient_name']},
                                             chat_history=[dict(
                                                 msg_sender=chat_data.sender_id,
                                                 date=datetime.datetime.now(),
                                                 message=chat_data.message,
                                                 attachment_info=attachment_info)],
                                             chat_meta={
                                                 'chat_type': 'Chat Message',
                                                 'case_id': case_id
                                             }
                                             )

                    mongo_collection.insert_one(dict(save_chat_msg))

                    data_to_return = {'msg': 'Chat Saved', 'chat_id': uid}

                else:
                    data_to_return = save_to_existing_chat_if_exists

                new_chat_msg = ChatEvent(chat_id=data_to_return['chat_id'],
                                         from_user_id=chat_data.sender_id,
                                         from_user_name=user_data['sender_name'],
                                         to_user_id=chat_data.recipient_id,
                                         to_user_name=user_data['recipient_name'],
                                         event_date=datetime.datetime.now(),
                                         chat_message=chat_data.message,
                                         remarks='None')
                frbs_ctrl.chat_message_received(chat_event=new_chat_msg)

                return data_to_return, 'Success'
            else:
                return None, f'Chat is not allowed'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while saving chat message'

    def add_to_existing_chat(self, chat_data: SaveChatModel):

        try:

            mongo_collection = self.mongo_db['ChatMessages']
            check_if_chat_exists = mongo_collection.find_one(
                {"$or": [{
                    "$and": [{"user_one_id": chat_data.sender_id}, {"user_two_id": chat_data.recipient_id},
                             {"chat_open": True}]
                }, {
                    "$and": [{"user_one_id": chat_data.recipient_id}, {"user_two_id": chat_data.sender_id},
                             {"chat_open": True}]
                }
                ]})
            if not check_if_chat_exists:
                return None, 'Chat is not allowed as the chat does not exist or the chat is closed'

            attachment_info = None
            if chat_data.attachment_encoded:
                attachment_info, msg = self.upload_attachment(chat_data.attachment_encoded)
                if attachment_info is None:
                    return None, msg

            chat_to_append = dict(msg_sender=chat_data.sender_id,
                                  date=datetime.datetime.now(),
                                  message=chat_data.message,
                                  attachment_info=attachment_info)
            chats = check_if_chat_exists['chat_history']
            chats.insert(0, chat_to_append)
            mongo_collection.find_one_and_update({"chat_id": check_if_chat_exists['chat_id']}, {
                "$set": {"chat_history": chats
                         }
            })

            return {'msg': 'Chat Saved', 'chat_id': check_if_chat_exists['chat_id']}, 'Chat save success'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting chat messages'

    def get_chat_by_id(self, chat_data: GetChat):

        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            mongo_collection = self.mongo_db['ChatMessages']
            check_if_chat_exists = mongo_collection.find_one(
                {"chat_id": chat_data.chat_id}
            )
            if check_if_chat_exists is not None:
                doctorid = None
                if check_if_chat_exists['user_one_details']['user_type'] == 'Doctor':
                    doctorid = check_if_chat_exists['user_one_id']

                if check_if_chat_exists['user_two_details']['user_type'] == 'Doctor':
                    doctorid = check_if_chat_exists['user_two_id']

                service_provider = {}

                doctor_details, msg = doctor_ctrl.get_by_id(
                    doctorid=doctorid)
                if doctor_details:
                    service_provider = {
                        'user_type': 'Doctor',
                        'user_name': doctor_details['firstname'],
                        'firstname': doctor_details['firstname'],
                        'lastname': doctor_details['lastname'],
                        'graduation': doctor_details['graduation'],
                        'masters': doctor_details['masters'],
                        'specialization': doctor_details['specialization'],
                        'profile_image': {
                            'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                            'profile_image_url': doctor_details[
                                'profile_image_url'] if 'profile_image_url' in doctor_details else None
                        }
                    }

                return dict(
                    chat_id=check_if_chat_exists['chat_id'],
                    user_one_id=check_if_chat_exists['user_one_id'],
                    user_two_id=check_if_chat_exists['user_two_id'],
                    user_one_details=service_provider if check_if_chat_exists['user_one_details'][
                                                             'user_type'] == 'Doctor' else check_if_chat_exists[
                        'user_one_details'],
                    user_two_details=service_provider if check_if_chat_exists['user_two_details'][
                                                             'user_type'] == 'Doctor' else check_if_chat_exists[
                        'user_two_details'],
                    chat_meta=check_if_chat_exists['chat_meta'],
                    chat_count=len(check_if_chat_exists['chat_history']),
                    chat_history=(check_if_chat_exists['chat_history'])[chat_data.start_index:chat_data.end_index]
                ), 'Chat Found'
            else:
                return None, 'No chat msg'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting chat messages'

    def get_all_chats_of_user(self, chat_data: GetUsersAllChat):

        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            mongo_collection = self.mongo_db['ChatMessages']
            chats = mongo_collection.find(
                {"$or": [{
                    "user_one_id": chat_data.user_id
                }, {
                    "user_two_id": chat_data.user_id}]
                }
            )
            all_chats = []
            if len(list(chats.clone())):
                for chat in chats:
                    doctorid = None
                    if chat['user_one_details']['user_type'] == 'Doctor':
                        doctorid = chat['user_one_id']

                    if chat['user_two_details']['user_type'] == 'Doctor':
                        doctorid = chat['user_two_id']

                    service_provider = {}

                    doctor_details, msg = doctor_ctrl.get_by_id(
                        doctorid=doctorid)
                    if doctor_details:
                        service_provider = {
                            'user_type': 'Doctor',
                            'user_name': doctor_details['firstname'],
                            'firstname': doctor_details['firstname'],
                            'lastname': doctor_details['lastname'],
                            'graduation': doctor_details['graduation'],
                            'masters': doctor_details['masters'],
                            'specialization': doctor_details['specialization'],
                            'profile_image': {
                                'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                                'profile_image_url': doctor_details[
                                    'profile_image_url'] if 'profile_image_url' in doctor_details else None
                            }
                        }

                    if chat['user_one_details']['user_type'] == 'Patient':
                        user_ctrl = UserController(db=self.db, otp_generator=None)

                        patient_id = chat['user_one_id']
                        user_details = user_ctrl.get_user_by_id(patient_id)

                        chat['user_one_details']['firstname'] = user_details.firstname
                        chat['user_one_details']['lastname'] = user_details.lastname

                        get_picture = self.mongo_db['PatientImage'].find_one(dict(userid=patient_id))
                        if get_picture:
                            chat['user_one_details']['profile_image'] = {
                                "image_id": get_picture['image_id'],
                                "profile_image_url": get_picture['profile_image_url']
                            }
                        else:
                            chat['user_one_details']['profile_image'] = {
                                "image_id": None,
                                "profile_image_url": None
                            }
                    if chat['user_two_details']['user_type'] == 'Patient':
                        user_ctrl = UserController(db=self.db, otp_generator=None)

                        patient_id = chat['user_two_id']
                        user_details = user_ctrl.get_user_by_id(patient_id)

                        chat['user_two_details']['firstname'] = user_details.firstname
                        chat['user_two_details']['lastname'] = user_details.lastname

                        get_picture = self.mongo_db['PatientImage'].find_one(dict(userid=patient_id))
                        if get_picture:
                            chat['user_two_details']['profile_image'] = {
                                "image_id": get_picture['image_id'],
                                "profile_image_url": get_picture['profile_image_url']
                            }
                        else:
                            chat['user_two_details']['profile_image'] = {
                                "image_id": None,
                                "profile_image_url": None
                            }

                    if chat['user_one_details']['user_type'] == 'Admin':
                        chat['user_one_details']['firstname'] = 'Ayoo'
                        chat['user_one_details']['lastname'] = 'Admin'
                        chat['user_one_details']['profile_image'] = {
                            "image_id": None,
                            "profile_image_url": None
                        }
                    if chat['user_two_details']['user_type'] == 'Admin':
                        chat['user_two_details']['firstname'] = 'Ayoo'
                        chat['user_two_details']['lastname'] = 'Admin'
                        chat['user_two_details']['profile_image'] = {
                            "image_id": None,
                            "profile_image_url": None
                        }

                    # Send only recipient chats for the given user
                    if chat_data.user_id == chat['user_one_id']:
                        msg_sender = chat['user_one_id']
                        msg_receiver = chat['user_two_id']
                        msg_sender_details = service_provider if chat['user_one_details']['user_type'] == 'Doctor' else \
                            chat['user_one_details']
                        msg_receiver_details = service_provider if chat['user_two_details'][
                                                                       'user_type'] == 'Doctor' else chat[
                            'user_two_details']
                    else:
                        msg_sender = chat['user_two_id']
                        msg_receiver = chat['user_one_id']
                        msg_sender_details = service_provider if chat['user_two_details'][
                                                                     'user_type'] == 'Doctor' else chat[
                            'user_two_details']
                        msg_receiver_details = service_provider if chat['user_one_details'][
                                                                       'user_type'] == 'Doctor' else \
                            chat['user_one_details']
                    all_chats.append({
                        'chat_id': chat['chat_id'],
                        'recipient_id': msg_receiver,
                        'recipient_details': msg_receiver_details,
                        'chat_history': chat['chat_history'],
                        'chat_meta': chat['chat_meta'],
                        'chat_open': chat['chat_open']
                    })

                return all_chats, 'Chat Found'
            else:
                return None, 'No chat msg'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting chat messages'

    def close_chat(self, chat_data: CloseChat):

        try:
            mongo_collection = self.mongo_db['ChatMessages']
            check_if_chat_exists = mongo_collection.find_one(dict(chat_id=chat_data.chat_id))
            if check_if_chat_exists is not None:
                mongo_collection.find_one_and_update({'chat_id': chat_data.chat_id}, {'$set': {'chat_open': False}})
                return {'msg': 'Chat Closed'}, 'Chat is not allowed anymore for this chat id'
            else:
                return 'Chat Not Found', 'No chat msg'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while closing chat'

    def get_ayoo_support_id(self):

        try:
            admins = []
            get_admin = self.db.query(dbmodels.DBAdmin).all()
            for admin in get_admin:
                admins.append({
                    'admin_id': admin.userid,
                    'admin_email': admin.email
                })
            return admins, 'Admin found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while saving chat message'

    def save_chat_with_ayoo_support(self, chat_data: SaveChatModel):

        try:
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            mongo_collection = self.mongo_db['ChatMessages']

            user_data, data_msg = self.get_user_and_admin_details(chat_data=chat_data)
            if (user_data['sender_type'] == 'Patient' and user_data['recipient_type'] == 'Admin') or (
                    user_data['recipient_type'] == 'Patient' and user_data['sender_type'] == 'Admin'):
                save_to_existing_chat_if_exists, msg = self.add_to_existing_chat(chat_data=chat_data)

                if save_to_existing_chat_if_exists is None:

                    uid = str(uuid.uuid4())

                    attachment_info = None
                    if chat_data.attachment_encoded:
                        attachment_info, msg = self.upload_attachment(chat_data.attachment_encoded)
                        if attachment_info is None:
                            return None, msg

                    save_chat_msg = SaveChat(chat_id=uid,
                                             user_one_id=chat_data.sender_id,
                                             user_two_id=chat_data.recipient_id,
                                             user_one_details={'user_type': user_data['sender_type'],
                                                               'user_name': user_data['sender_name']},
                                             user_two_details={'user_type': user_data['recipient_type'],
                                                               'user_name': user_data['recipient_name']},
                                             chat_history=[dict(
                                                 msg_sender=chat_data.sender_id,
                                                 date=datetime.datetime.now(),
                                                 message=chat_data.message,
                                                 attachment_info=attachment_info)],
                                             chat_meta={
                                                 'chat_type': 'Ayoo Support'
                                             }
                                             )

                    mongo_collection.insert_one(dict(save_chat_msg))

                    data_to_return = {'msg': 'Chat Saved', 'chat_id': uid}

                else:
                    data_to_return = save_to_existing_chat_if_exists

                new_chat_msg = ChatEvent(chat_id=data_to_return['chat_id'],
                                         from_user_id=chat_data.sender_id,
                                         from_user_name=user_data['sender_name'],
                                         to_user_id=chat_data.recipient_id,
                                         to_user_name=user_data['recipient_name'],
                                         event_date=datetime.datetime.now(),
                                         chat_message=chat_data.message,
                                         remarks='None')
                frbs_ctrl.chat_message_received(chat_event=new_chat_msg)

                return data_to_return, 'Success'
            else:
                return None, f'Chat is not allowed'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while saving chat message'

    def broadcast_message(self, chat_data: BroadcastMessage, admin_id: str):

        try:
            if chat_data.broadcast_to == 'Selected' and chat_data.user_id is None:
                raise Exception('User IDs are required')

            ctrl = UserController(db=self.db, otp_generator=None)
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            admin_ctrl = AdminController(db=self.db, mongo=self.mongo)

            admin_details = admin_ctrl.get_admin_by_id(userid=admin_id)
            if admin_details is None:
                raise Exception('Invalid admin')

            sender = {
                'user_type': 'Admin',
                'user_name': admin_details.email
            }

            if chat_data.broadcast_to == 'All':

                get_all_doctors = self.db.query(dbmodels.DBDoctor).all()
                if get_all_doctors is None:
                    raise Exception('No doctor exist')

                for doctor in get_all_doctors:
                    service_provider = {}

                    doctor_details, msg = doctor_ctrl.get_by_id(doctorid=doctor.doctorid)

                    if doctor_details:
                        service_provider = {
                            'user_type': 'Doctor',
                            'user_name': doctor_details['firstname'],
                            'firstname': doctor_details['firstname'],
                            'lastname': doctor_details['lastname'],
                            'graduation': doctor_details['graduation'],
                            'masters': doctor_details['masters'],
                            'specialization': doctor_details['specialization'],
                            'profile_image': {
                                'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                                'profile_image_url': doctor_details[
                                    'profile_image_url'] if 'profile_image_url' in doctor_details else None
                            }
                        }

                    self.send_broadcast_to_user(sender=sender, recipient=service_provider,
                                                msg=chat_data.message, admin_id=admin_id,
                                                recipient_id=doctor.doctorid,
                                                attachment_encoded=chat_data.attachment_encoded)

                get_all_patients = self.db.query(dbmodels.DBUser).all()
                if get_all_patients is None:
                    raise Exception('No patient exist')

                for patient in get_all_patients:
                    user_details = ctrl.get_user_details(userid=patient.userid, mongo=self.mongo)
                    patient_details = {}
                    if user_details is not None:
                        patient_details = {
                            'user_type': 'Patient',
                            'user_name': user_details['firstname']
                        }

                    self.send_broadcast_to_user(sender=sender, recipient=patient_details,
                                                msg=chat_data.message, admin_id=admin_id,
                                                recipient_id=patient.userid,
                                                attachment_encoded=chat_data.attachment_encoded)

            elif chat_data.broadcast_to == 'Doctors':
                get_all_doctors = self.db.query(dbmodels.DBDoctor).all()
                if get_all_doctors is None:
                    raise Exception('No doctor exist')

                for doctor in get_all_doctors:
                    service_provider = {}

                    doctor_details, msg = doctor_ctrl.get_by_id(doctorid=doctor.doctorid)

                    if doctor_details:
                        service_provider = {
                            'user_type': 'Doctor',
                            'user_name': doctor_details['firstname'],
                            'firstname': doctor_details['firstname'],
                            'lastname': doctor_details['lastname'],
                            'graduation': doctor_details['graduation'],
                            'masters': doctor_details['masters'],
                            'specialization': doctor_details['specialization'],
                            'profile_image': {
                                'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                                'profile_image_url': doctor_details[
                                    'profile_image_url'] if 'profile_image_url' in doctor_details else None
                            }
                        }

                    self.send_broadcast_to_user(sender=sender, recipient=service_provider,
                                                msg=chat_data.message, admin_id=admin_id,
                                                recipient_id=doctor.doctorid,
                                                attachment_encoded=chat_data.attachment_encoded)

            elif chat_data.broadcast_to == 'Patients':
                get_all_patients = self.db.query(dbmodels.DBUser).all()
                if get_all_patients is None:
                    raise Exception('No patient exist')

                for patient in get_all_patients:
                    user_details = ctrl.get_user_details(userid=patient.userid, mongo=self.mongo)
                    patient_details = {}
                    if user_details is not None:
                        patient_details = {
                            'user_type': 'Patient',
                            'user_name': user_details['firstname']
                        }

                    self.send_broadcast_to_user(sender=sender, recipient=patient_details,
                                                msg=chat_data.message, admin_id=admin_id,
                                                recipient_id=patient.userid,
                                                attachment_encoded=chat_data.attachment_encoded)

            elif chat_data.broadcast_to == 'Selected':
                for id in chat_data.user_id:
                    recipient = {}
                    doctor_details, msg = doctor_ctrl.get_by_id(doctorid=id)
                    if doctor_details:

                        recipient = {
                            'user_type': 'Doctor',
                            'user_name': doctor_details['firstname'],
                            'firstname': doctor_details['firstname'],
                            'lastname': doctor_details['lastname'],
                            'graduation': doctor_details['graduation'],
                            'masters': doctor_details['masters'],
                            'specialization': doctor_details['specialization'],
                            'profile_image': {
                                'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                                'profile_image_url': doctor_details[
                                    'profile_image_url'] if 'profile_image_url' in doctor_details else None
                            }
                        }

                    else:
                        user_details = ctrl.get_user_details(userid=id, mongo=self.mongo)
                        if user_details is not None:
                            recipient = {
                                'user_type': 'Patient',
                                'user_name': user_details['firstname']
                            }

                    self.send_broadcast_to_user(sender=sender, recipient=recipient,
                                                msg=chat_data.message, admin_id=admin_id,
                                                recipient_id=id,
                                                attachment_encoded=chat_data.attachment_encoded)
            return {
                "msg": "Broadcast Sent"
            }

        except Exception as e:
            raise HTTPException(status_code=409, detail=(f'Error occurred as {str(e)} while sending broadcast message'))

    def welcome_msg_to_old_users(self, admin_id: str):

        try:
            ctrl = UserController(db=self.db, otp_generator=None)
            admin_ctrl = AdminController(db=self.db, mongo=self.mongo)
            admin_details = admin_ctrl.get_admin_by_id(userid=admin_id)
            if admin_details is None:
                return None, 'Invalid admin'

            sender = {
                'user_type': 'Admin',
                'user_name': admin_details.email
            }

            get_all_patients = self.db.query(dbmodels.DBUser).all()
            if get_all_patients is None:
                return None, 'No patient exist'
            for patient in get_all_patients:

                check_if_chat_exists = self.mongo_db['ChatMessages'].find_one(
                    {
                        "$and": [{"user_one_id": admin_id}, {"user_two_id": patient.userid},
                                 {"chat_open": False},
                                 {"chat_meta.chat_type": "Welcome Message"}]
                    })
                if check_if_chat_exists is None:

                    user_details = ctrl.get_user_details(userid=patient.userid, mongo=self.mongo)
                    patient_details = {}
                    if user_details is not None:
                        patient_details = {
                            'user_type': 'Patient',
                            'user_name': user_details['firstname']
                        }

                    uid = str(uuid.uuid4())
                    save_chat_msg = SaveChat(chat_id=uid,
                                             user_one_id=admin_id,
                                             user_two_id=patient.userid,
                                             user_one_details=sender,
                                             user_two_details=patient_details,
                                             chat_history=[dict(
                                                 msg_sender=admin_id,
                                                 date=datetime.datetime.now(),
                                                 message='Welcome to Ayoo Care',
                                                 attachment_info=None)],
                                             chat_meta=dict(
                                                 chat_type='Welcome Message'
                                             ),
                                             chat_open=False
                                             )

                    self.mongo_db['ChatMessages'].insert_one(dict(save_chat_msg))

            return {
                       "msg": "Welcome msg sent"
                   }, 'Success'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while sending welcome message'

    def welcome_msg_to_new_user(self, userid: str, registered_by:str='Self'):

        try:
            ctrl = UserController(db=self.db, otp_generator=None)
            admin_details = self.db.query(dbmodels.DBAdmin).filter(DBAdmin.email == '<EMAIL>').one_or_none()
            admin_id = admin_details.userid
            if admin_details is None:
                return None, 'Invalid admin'

            sender = {
                'user_type': 'Admin',
                'user_name': admin_details.email
            }

            check_if_chat_exists = self.mongo_db['ChatMessages'].find_one(
                {
                    "$and": [{"sender_id": admin_id}, {"recipient_id": userid},
                             {"chat_open": False},
                             {"chat_meta.chat_type": "Welcome Message"}]
                })
            if check_if_chat_exists is None:
                user_details = ctrl.get_user_details(userid=userid, mongo=self.mongo)
                patient_details = {}
                if user_details is not None:
                    patient_details = {
                        'user_type': 'Patient',
                        'user_name': user_details['firstname']
                    }

                # Send Email

                aws_mail_ctrl = AWSEmailAndMsgSender()
                text_local_controller = TextLocalController()

                if registered_by=='Self':
                    sms_text = f'Dear {user_details["firstname"]}, Welcome to Ayoo.care! You can download our App from https://play.google.com/store/apps/details?id=com.ayoo.care&hl=en_US&pli=1 or https://apps.apple.com/us/app/ayoo-care/id1586813797'
                    mail_text = f'''
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body {{
                                font-family: Arial, sans-serif;
                                line-height: 1.6;
                                background-color: #f9f9f9;
                                margin: 0;
                                padding: 0;
                            }}
                    
                            .container {{
                                max-width: 600px;
                                margin: 0 auto;
                                padding: 20px;
                                border-radius: 5px;
                                background-color: #ffffff;
                                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                            }}
                    
                            .logo {{
                                text-align: center;
                                margin-bottom: 20px;
                            }}
                    
                            .logo img {{
                                min-width: 125px;
                                max-width: 200px;
                                height: auto;
                            }}
                    
                            .content {{
                                padding: 20px 0;
                                border-top: 1px solid #f2f2f2;
                                border-bottom: 1px solid #f2f2f2;
                            }}
                    
                            .download-buttons {{
                                text-align: center;
                                margin-top: 20px;
                            }}
                    
                            .download-buttons a {{
                                display: inline-block;
                                margin: 10px;
                            }}
                    
                            .footer {{
                                text-align: center;
                                margin-top: 20px;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="logo">
                                <img src="https://dev.ayoo.care/images/ayooLogo.jpg" alt="Ayoo.care Logo">
                            </div>
                            <div class="content">
                                <h2>Welcome to Ayoo.care!</h2>
                                <p>Dear <strong>{user_details['firstname']}</strong>,</p>
                                <p>We're thrilled to have you join our community of individuals dedicated to their well-being and self-care journey. This platform is designed to provide you with the resources, tools, and support you need to lead a healthier and happier life.</p>
                                <p>At Ayoo.care, we believe that taking care of your well-being should be convenient, enjoyable, and empowering. Whether you're seeking tips for stress management, looking to improve your fitness routine, or interested in exploring mindfulness practices, we've got you covered.</p>
                                <p>To get started, download the Ayoo.care app from the following app stores:</p>
                                <div class="download-buttons">
                                    <a href="https://apps.apple.com/us/app/ayoo-care/id1586813797" target="_blank">
                                        <img src="https://www.ayoo.care/images/appstore.png" alt="App Store">
                                    </a>
                                    <a href="https://play.google.com/store/apps/details?id=com.ayoo.care&hl=en_US&pli=1" target="_blank">
                                        <img src="https://www.ayoo.care/images/playstore.png" alt="Google Play Store">
                                    </a>
                                </div>
                            <div class="footer">
                                <p>If you have any questions or need assistance, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                            </div>
                        </div>
                    </body>
                    </html>
                    '''
                else:
                    sms_text = f'Dear {user_details["firstname"]}, Welcome to Ayoo.care! Your account has been registered with us. To set your password and access your account, click the link: {WEB_URL_PATH}forgotPassword. You can download our App from https://play.google.com/store/apps/details?id=com.ayoo.care&hl=en_US&pli=1 or https://apps.apple.com/us/app/ayoo-care/id1586813797'
                    mail_text= f'''
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <style>
                                body {{
                                    font-family: Arial, sans-serif;
                                    line-height: 1.6;
                                    background-color: #f9f9f9;
                                    margin: 0;
                                    padding: 0;
                                }}
                        
                                .container {{
                                    max-width: 600px;
                                    margin: 0 auto;
                                    padding: 20px;
                                    border-radius: 5px;
                                    background-color: #ffffff;
                                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                                }}
                        
                                .logo {{
                                    text-align: center;
                                    margin-bottom: 20px;
                                }}
                        
                                .logo img {{
                                    min-width: 150px;
                                    max-width: 200px;
                                    height: auto;
                                }}
                        
                                .content {{
                                    padding: 20px 0;
                                    border-top: 1px solid #f2f2f2;
                                    border-bottom: 1px solid #f2f2f2;
                                }}
                        
                                .button {{
                                    text-align: center;
                                    margin-top: 20px;
                                }}
                        
                                .button a {{
                                    display: inline-block;
                                    padding: 10px 20px;
                                    background-color: #007bff;
                                    color: #ffffff;
                                    text-decoration: none;
                                    border-radius: 5px;
                                }}
                        
                                .footer {{
                                    text-align: center;
                                    margin-top: 20px;
                                }}
                            </style>
                        </head>
                        
                        <body>
                            <div class="container">
                                <div class="logo"> <img src="https://dev.ayoo.care/images/ayooLogo.jpg" alt="Ayoo.care Logo"> </div>
                                <div class="content">
                                    <h2>Welcome to Ayoo.care!
                                    </h2>
                                    <p>Dear {user_details['firstname']},</p>
                                    <p>We're excited to welcome you to Ayoo.care! Your account has been created. Below are your login details:</p>
                                    <p><strong>Email:</strong> {user_details['email']}</p>
                                    <p>To set your password and access your account, click the button below:
                                    </p>
                                    <div class="button"> <a href="{WEB_URL_PATH}forgotPassword" target="_blank">Set Your Password</a>
                                    </div>
                                    <p>If you have any questions or
                                        need assistance, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                                </div>
                                <div class="footer">
                                    <p>This email was sent by Ayoo.care. © 2023 Ayoo.care</p>
                                </div>
                            </div>
                        </body>
                        
                        </html>
                    '''

                aws_mail_ctrl.send_welcome_msg_to_users(welcome_message_on_mail=mail_text,
                                                        welcome_message_on_mobile=sms_text,
                                                        mobile=user_details['mobile'],
                                                        email=user_details['email'])

                # text_local_controller.send_sms(template_name='WelcomeMessage', var_list=[])


                # Save Chat Message
                uid = str(uuid.uuid4())
                save_chat_msg = SaveChat(chat_id=uid,
                                         user_one_id=admin_id,
                                         user_two_id=userid,
                                         user_one_details=sender,
                                         user_two_details=patient_details,
                                         chat_history=[dict(
                                             msg_sender=admin_id,
                                             date=datetime.datetime.now(),
                                             message='Welcome to Ayoo Care',
                                             attachment_info=None)],
                                         chat_meta=dict(
                                             chat_type='Welcome Message'
                                         ),
                                         chat_open=False
                                         )

                self.mongo_db['ChatMessages'].insert_one(dict(save_chat_msg))

            return {
                       "msg": "Welcome msg sent"
                   }, 'Success'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while sending welcome message'

    def send_broadcast_to_user(self, sender, recipient, msg: str, admin_id: str, recipient_id: str,
                               attachment_encoded: str = None):
        try:

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            mongo_collection = self.mongo_db['ChatMessages']

            check_if_chat_exists = mongo_collection.find_one(
                {
                    "$and": [{"user_one_id": admin_id}, {"user_two_id": recipient_id},
                             {"chat_open": False},
                             {"chat_meta.chat_type": "Broadcast"}]
                })

            if check_if_chat_exists:
                attachment_info = None
                if attachment_encoded:
                    attachment_info, msg = self.upload_attachment(attachment_encoded)
                    if attachment_info is None:
                        raise Exception(msg)
                chat_to_append = dict(msg_sender=admin_id,
                                      date=datetime.datetime.now(),
                                      message=msg,
                                      attachment_info=attachment_info)
                chats = check_if_chat_exists['chat_history']
                chats.insert(0, chat_to_append)
                mongo_collection.find_one_and_update({"chat_id": check_if_chat_exists['chat_id']}, {
                    "$set": {"chat_history": chats
                             }
                })
                chat_id = check_if_chat_exists['chat_id']
            else:

                uid = str(uuid.uuid4())
                attachment_info = None
                if attachment_encoded:
                    attachment_info, msg = self.upload_attachment(attachment_encoded)
                    if attachment_info is None:
                        raise Exception(msg)

                save_chat_msg = SaveChat(chat_id=uid,
                                         user_one_id=admin_id,
                                         user_two_id=recipient_id,
                                         user_one_details=sender,
                                         user_two_details=recipient,
                                         chat_history=[dict(
                                             msg_sender=admin_id,
                                             date=datetime.datetime.now(),
                                             message=msg,
                                             attachment_info=attachment_info)],
                                         chat_meta=dict(
                                             chat_type='Broadcast'
                                         ),
                                         chat_open=False
                                         )

                self.mongo_db['ChatMessages'].insert_one(dict(save_chat_msg))
                chat_id = uid

            new_chat_msg = ChatEvent(chat_id=chat_id,
                                     from_user_id=admin_id,
                                     from_user_name=sender['user_name'],
                                     to_user_id=recipient_id,
                                     to_user_name=recipient['user_name'],
                                     event_date=datetime.datetime.now(),
                                     chat_message=msg,
                                     remarks='None')
            frbs_ctrl.chat_message_received(chat_event=new_chat_msg)

            return True

        except Exception as e:
            raise Exception(f'{str(e)}')

    def check_if_chat_is_support_chat(self, chat_data: SaveChatModel):
        try:
            user_data, data_msg = self.get_user_and_admin_details(chat_data=chat_data)
            if user_data['sender_type'] == 'Admin' or user_data['recipient_type'] == 'Admin':
                return True

            return False
        except Exception as e:
            return False


class ConnectionManager(ChatController):
    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)
        self.connections = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.connections[user_id] = websocket

    def disconnect(self, user_id: str):
        if user_id in self.connections:
            del self.connections[user_id]

    async def send_personal_message(self, chat_data: SaveChatModel):
        # async def send_personal_message(self, sender_id: str, recipient_id: str, message: str,
        #                                 attachment_encoded: str = None):

        is_support_chat = self.check_if_chat_is_support_chat(chat_data=chat_data)

        recipient = self.connections.get(chat_data.recipient_id)

        if is_support_chat:
            save_chat_to_db, msg = self.save_chat_with_ayoo_support(chat_data=chat_data)
        else:
            save_chat_to_db, msg = self.save_chat(chat_data=chat_data)

        if save_chat_to_db is None:
            sender = self.connections.get(chat_data.sender_id)
            await sender.send_json({
                'message': "Error occurred while sending chat",
                'error': msg
            })
        if recipient and save_chat_to_db is not None:
            # await recipient.send_text(message)
            await recipient.send_json({
                'sender_id': chat_data.sender_id,
                'message': chat_data.message,
                'attachment_encoded': chat_data.attachment_encoded
            })

    # async def broadcast(self, message: str, attachment_encoded: str):
    #     for connection in self.connections.values():
    #         # await connection.send_text(message, attachment_encoded)
    #         await connection.send_json({
    #             'message': message,
    #             'attachment_encoded': attachment_encoded
    #         })
