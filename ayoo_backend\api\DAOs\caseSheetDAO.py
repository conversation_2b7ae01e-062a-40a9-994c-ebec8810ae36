import uuid
from typing import List, Optional

from pymongo import DESCEN<PERSON><PERSON>, ASCENDING, ReturnDocument

from .doctorDAO import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Doctor<PERSON><PERSON><PERSON><PERSON>ields
from .baseDAO import BaseMongoDAO, BaseMongoQueryFields, BaseMongoSort, SortOrder
from ..DataModels.appointment import AppointmentStatus
from ..DataModels.case_sheet import CaseSheet, ReferralInformation, PrescriptionType, LabTests, ClinicalHistory, \
    ReferCaseView, ReferralUpdateWithDoctorInfo, ReferringPersonTypes, UpdateToReferee, UpdateToReferrer, Prescriptions, \
    ConsultationType, UpdatePrescription, DoctorNotes, pdf_keys, ShareCaseSheet
from fastapi import Query, HTTPException
from datetime import datetime, timedelta
from pydantic import validator
from ..database import get_db
from ..dbmodels import DB<PERSON>ser, DBDoctor,DBRelatives
from ..file_upload_service.file_upload_controller import fileUploadContoller
from ..file_upload_service.file_upload_models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e<PERSON>, RequestForm
from ..firebase_controller import FireBaseNotificationController
from ..firebase_models import PrescriptionEvent
from ..mental_health_controller import MentalHealthController
from ..mongodb import mongodb_conn
from ..pdf_gen import PDF_Gen
from ..validators import BaseValidator, ValidationError
from .appointmentDAO import AppointmentDAO, CaseSheetQueryFields, CaseSheetAppointmentQueryFields, ReferralQueryFields
from ..view_controller import UserController
from ..payment.payment_controller import remove_custom_fees


class GetByAppointmentId(BaseMongoQueryFields):
    appointment_id: str


class CaseIDSort(BaseMongoSort):
    sort_by_appointment_slot: SortOrder = Query(SortOrder.asc)


class GetByPatientIdAndDoctorId(BaseMongoQueryFields):
    patient_id: str
    doctor_id: str

    def apply_filters(self, query_object, dao, *args, **kwargs):
        return {
            "doctorid": self.doctor_id,
            "patient_id": self.patient_id,
            "$or": [
                {"status.status": AppointmentStatus.booked.value},
                {"status.status": AppointmentStatus.completed.value},
            ]
        }


class GetByPatientIdAndDoctorIdSort(BaseMongoSort):
    appointment_slot: SortOrder = SortOrder.desc.value


class CaseSheetDAO(BaseMongoDAO):
    collection = "CaseSheet"
    _model_class = CaseSheet

    def get_session_count(self, case_id, *args, **kwargs):
        try:
            pipeline = [
                {
                    "$match": {
                        "case_id": case_id
                    }
                },
                {
                    "$group": {
                        "_id": "$appointment_id"
                    }
                },
                {
                    "$count": "distinct_appointment_count"
                }
            ]

            result = list(self.db[self.collection].aggregate(pipeline))
            if not len(result):
                return 1
            return result[0].get('distinct_appointment_count', 0) + 1
        except Exception as e:
            raise Exception(f'Session count error: {str(e)}')

    def get_prescription_count(self, case_id: str, appointment_id: str, patient_id: str, *args, **kwargs):
        try:
            return self.db[self.collection].count_documents({
                'case_id': case_id,
                'appointment_id': appointment_id,
                'patient_id': patient_id
            })
        except Exception as e:
            raise Exception(f'Prescription count error: {str(e)}')

    def get_appointment_details(self, appointment_id: str, patient_id: str):
        try:
            appointment_data = AppointmentDAO().list(
                skip=0, limit=1, filter=CaseSheetQueryFields(appointment_id=appointment_id, patient_id=patient_id)
            )
            if not len(appointment_data):
                # Check if patient is in the patients array for group appointments
                appointment_with_patient = self.db['Appointments'].find_one({
                    'appointment_id': appointment_id,
                    'patients': patient_id
                })
                
                if appointment_with_patient is None:
                    raise Exception('Invalid appointment ID')
                
                # Convert MongoDB document to dict format similar to AppointmentDAO().list() output
                return {k: v for k, v in appointment_with_patient.items() if k != '_id'}
                
            return appointment_data[0].dict()
        except Exception as e:
            raise Exception(f'Appointment details fetch error: {str(e)}')

    def get_date_open_of_a_case(self, case_id: str):
        try:
            appointment_data = AppointmentDAO().list(
                skip=0, limit=1,
                filter=CaseSheetAppointmentQueryFields(caseid=case_id),
                sort=CaseIDSort()
            )
            if not len(appointment_data):
                raise Exception('Invalid case ID')
            return appointment_data[0].dict().get('created_at')
        except Exception as e:
            raise Exception(f'Get case open date error: {str(e)}')

    def fetch_appointment_id(self, case_id: str):
        try:

            resp = self.db['Appointments'].find_one(
                {
                    "caseid": case_id,
                    "appointment_slot": {"$lte": datetime.now()},
                    "is_active": True,
                    "is_confirmed": True,
                    "status.status": {'$in': ['Booked', 'Completed']}
                },
                {
                    "_id": 0,
                    "appointment_slot": 1,
                    "caseid": 1,
                    "is_active": 1,
                    "is_confirmed": 1,
                    "appointment_type": 1,
                    "appointment_id": 1
                },
                sort=[("appointment_slot", DESCENDING)]
            )
            if resp is not None:
                resp['case_open_date'] = self.get_date_open_of_a_case(
                    case_id=case_id)
                resp['referred_by_doctor_id'] = None
                resp['referred_by_name'] = None
                referral_details = ReferCaseDAO().get_referred_by_info(case_id=case_id)
                if referral_details not in [None, {}]:
                    resp['referred_by_doctor_id'] = referral_details.get(
                        'referred_by')
                    resp['referred_by_name'] = referral_details.get(
                        'referred_by_name')

            return {'msg': f'Appointment Details {"Found" if resp is not None else "Not Found"}',
                    'appointment_details': resp if resp is not None else {}}
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Appointment ID fetch error: {str(e)}')

    def check_if_case_is_open(self, appointment_id: str, patient_id: str):
        try:
            appointment_data = AppointmentDAO().list(
                filter=CaseSheetAppointmentQueryFields(
                    appointment_id=appointment_id, patient_id=patient_id),
                sort=CaseIDSort()
            )
            
            # If no appointment found with direct patient_id match, check patients array for group appointments
            if not len(appointment_data):
                appointments_with_patient = self.db['Appointments'].find_one({
                    'appointment_id': appointment_id,
                    'patients': patient_id
                })
                
                if appointments_with_patient is None:
                    raise Exception('Invalid appointment ID')
                
                if appointments_with_patient.get('case_open') is False:
                    raise Exception(f'Case ID: {appointments_with_patient.get("caseid")} is closed')
                return True
                
            if appointment_data[0].case_open is False:
                raise Exception(
                    f'Case ID: {appointment_data[0].caseid} is closed')
            return True
        except Exception as e:
            raise Exception(f'Check for case open error: {str(e)}')

    def check_if_crud_allowed(self, appointment_id: str, patient_id: str, key: str):
        try:

            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            if key not in pdf_keys and existing_doc.get('case_sheet_in_edit_mode') is False:
                raise Exception('Changes are not allowed for this case sheet')

            if key in pdf_keys and existing_doc.get('prescription_fields_in_edit_mode') is False:
                raise Exception(
                    'Changes are not allowed for the prescription fields in this case sheet')

        except Exception as e:
            raise Exception(str(e))

    def get_one(self, appointment_id: str, patient_id: Optional[str] = None, *args, **kwargs):
        try:
            _object = self.db[self.collection].find_one({
                'appointment_id': appointment_id,
                'patient_id': patient_id},
                sort=[("appointment_slot", DESCENDING),
                      ("prescription_count", DESCENDING)])
        except Exception as e:
            raise Exception(str(e))
        if _object:
            return _object
        raise Exception(
            f"Case sheet data not found for this appointment id, add some data first.")

    def get_all(self, case_id,patient_id, *args, **kwargs):
        try:
            _object = list(self.db[self.collection].find(
                filter={'case_id': case_id,'patient_id':patient_id},
                sort=[('appointment_slot', -1), ('prescription_count', -1)],
                projection={'_id': False}
            ).clone())
            records = []
            lab_tests = []

            if len(_object) > 0:
                lab_tests = self.fetch_lab_tests(patient_id=_object[0].get('patient_id'), case_id=case_id,
                                                 appointment_no=None)
            for obj in _object:
                session_no = obj.get('session_no')
                prescription_no = obj.get('prescription_count')
                filtered_lab_tests = [test for test in lab_tests if
                                      test.get('appointment_no') == session_no and test.get(
                                          'prescription_no') == prescription_no]
                
        

                
                data_dict = dict(
                    appointment_id=obj.get('appointment_id'),
                    appointment_slot=obj.get('appointment_slot'),
                    session_no=session_no,
                    prescription_count=prescription_no,
                    appointment_type=obj.get('appointment_type'),
                    consultation_type=obj.get('consultation_type'),
                    is_couple_or_family_therapy=obj.get('is_couple_or_family_therapy'),
                    chief_complain=obj.get('chief_complain'),
                    doctor_notes=obj.get('doctor_notes'),
                    substance_use=obj.get('substance_use'),
                    assessment=obj.get('assessment'),
                    medication=obj.get('medication'),
                    lab_tests=filtered_lab_tests,
                    work_up_plan_or_recommendation=obj.get(
                        'work_up_plan_or_recommendation'),
                    therapy_recommendation=obj.get('therapy_recommendation'),
                    follow_up=obj.get('follow_up'),
                    prescription_preview=obj.get('prescription_preview'),
                    is_prescription_issued=obj.get('is_prescription_issued'),
                    is_prescription_issued_for_current_record=obj.get(
                        'is_prescription_issued_for_current_record'),
                    case_sheet_in_edit_mode=obj.get('case_sheet_in_edit_mode'),
                    prescription_fields_in_edit_mode=obj.get(
                        'prescription_fields_in_edit_mode'),
                    is_case_sheet_submitted=obj.get('is_case_sheet_submitted'),
                    case_sheet_submit_date=obj.get('case_sheet_submit_date'),
                    prescription_allowed_to_patient=obj.get(
                        'prescription_allowed_to_patient'),
                    prescription_issue_date=obj.get('prescription_issue_date'),
                    prescription_s3_object_key=obj.get(
                        'prescription_s3_object_key'),
                    prescription_s3_object_url=obj.get(
                        'prescription_s3_object_url'),
                    is_reissue_prescription=obj.get('is_reissue_prescription'),
                    reissue_reason=obj.get('reissue_reason'),
                    is_modified_prescription=obj.get(
                        'is_modified_prescription'),
                    modify_reason=obj.get('modify_reason'),
                    date_created=obj.get('created_at')
                )

                removed_records = {}

                if obj.get('is_reissue_prescription'):
                    removed_records = {'chief_complain': [],
                                       'substance_use': [],
                                       'assessment': [],
                                       'medication': [],
                                       'lab_tests': [],
                                       'work_up_plan_or_recommendation': {},
                                       'therapy_recommendation': {},
                                       'follow_up': {}}

                records.append({**data_dict, **removed_records})
        except Exception as e:
            raise Exception(str(e))
        if len(_object) > 0:
            appointment = self.db['Appointments'].find_one(
                    {
                        "caseid": case_id,
                    }
                )
            appointment_for = appointment.get("appointment_for") if appointment else None
          
            patient_ids =appointment.get("patients")
            db = get_db()
            caretaker_id = patient_ids[0]
            
            patient_details = db.query(DBUser).filter(
                DBUser.userid.in_(patient_ids)
            ).with_entities(
                DBUser.userid,
                DBUser.firstname, 
                DBUser.lastname,
            ).all()
                        
            relatives_details = db.query(DBRelatives).filter(
                DBRelatives.caretaker_id == caretaker_id
            ).with_entities(
                DBRelatives.relativeid,
                DBRelatives.firstname,
                DBRelatives.lastname
            ).all()
            patients_list = [
                    {
                        "patient_id": str(patient.userid),
                        'full_name': f"{patient.firstname or ''} {patient.lastname or ''}"
                    } 
                    for patient in patient_details
            ]
            relatives_list = [
                {
                    "patient_id": relative.relativeid,
                    'full_name': f"{relative.firstname or ''} {relative.lastname or ''}"
                } 
                for relative in relatives_details if relative.relativeid in patient_ids
            ] if relatives_details else []
                        
            return {
                'status': 'Success',
                'msg': 'Case sheets fetched successfully',
                'case_id': case_id,
                'patient_id': patient_id,
                'patient_ids':patients_list + relatives_list,
                'is_case_open': _object[0].get('is_open'),
                'appointment_for':appointment_for,
                'date_open': _object[0].get('date_open'),
                'date_closed': _object[0].get('date_closed'),
                'case_doctor': _object[0].get('case_doctor'),
                'doctor_name': _object[0].get('doctor_name'),
                'prescription_type': _object[0].get('prescription_type'),
                'case_summary': _object[0].get('case_summary'),
                'case_sheets': records
            }
        else:
            return {
                'status': 'Success',
                'msg': 'No case sheet found',
            }


    def update_multiple(self, filter, update):
        try:
            _object = self.db[self.collection].update_many(
                filter=filter, update=update)
        except Exception as e:
            raise Exception(f'Update error: {str(e)}')

    def create_case_schema(self, appointment_id: str, patient_id: str, prescription_type: PrescriptionType,
                           consultation_type: ConsultationType = 'Appointment', *args, **kwargs):
        try:
            appointment_count = self.db[self.collection].count_documents(
                {'appointment_id': appointment_id, 'patient_id': patient_id})
            if appointment_count == 0 and consultation_type == 'Appointment':
                appointment_data = self.db['Appointments'].find_one(
                                        {
                                            'appointment_id': appointment_id,
                                            '$or': [
                                                {'patients': {'$in': [patient_id]}},  # Check if patient_id is in patients array
                                                {'patient_id': patient_id}  # Also check primary patient
                                            ]
                                        }
                                    )
                date_open = self.get_date_open_of_a_case(
                    case_id=appointment_data.get('caseid'))
                case_id = appointment_data.get('caseid')
                session_count = self.get_session_count(case_id=case_id)
                prescription_count = self.get_prescription_count(
                    case_id=case_id,
                    appointment_id=appointment_id,
                    patient_id=patient_id
                )

                data = dict(
                    case_sheet_id=str(uuid.uuid4()),
                    appointment_id=appointment_id,
                    appointment_slot=appointment_data.get('appointment_slot'),
                    patient_id=patient_id,
                    case_id=case_id,
                    appointment_for=appointment_data.get('appointment_for'),
                    is_open=appointment_data.get('case_open'),
                    date_open=date_open,
                    date_closed=None,
                    session_no=session_count,
                    prescription_count=prescription_count + 1,
                    case_doctor=appointment_data.get('doctorid'),
                    doctor_name=appointment_data.get('doctor_name'),
                    prescription_type=prescription_type,
                    appointment_type=appointment_data.get('appointment_type'),
                    is_couple_or_family_therapy=appointment_data.get('is_couple_or_family_therapy'),
                    chief_complain=[],
                    doctor_notes=[],
                    substance_use=[],
                    assessment=[],
                    medication=[],
                    work_up_plan_or_recommendation=[],
                    therapy_recommendation=[],
                    follow_up={},
                    case_summary={},

                    consultation_type=consultation_type,
                    prescription_preview=False,
                    is_prescription_issued=False,
                    is_prescription_issued_for_current_record=False,
                    case_sheet_in_edit_mode=True,
                    prescription_fields_in_edit_mode=True,
                    is_case_sheet_submitted=False,
                    case_sheet_submit_date=None,
                    prescription_allowed_to_patient=False,
                    prescription_issue_date=None,
                    prescription_s3_object_key=None,
                    prescription_s3_object_url=None,
                    is_reissue_prescription=False,
                    reissue_reason=None,
                    is_modified_prescription=False,
                    modify_reason=None,
                    created_at=datetime.now()
                )

                _id = self.db[self.collection].insert_one(data).inserted_id
                return {"status": "Success", "_id": str(_id)}
        except Exception as e:
            raise Exception(str(e))

    def add_to_array(self, appointment_id: str,prescription_type: PrescriptionType, key: str, value,patient_id:Optional[str] =None ,
                     *args,
                     **kwargs):
        try:
            
            if key == 'doctor_notes' and patient_id is not None:
                appointment = self.db['Appointments'].find_one(
                    {
                        "appointment_id": appointment_id,
                    },
                )
                patient_array = appointment.get("patients") 
                primary_patient = appointment.get("patient_id")
                for patient_id in patient_array:
                    if patient_id == primary_patient:
                        self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
                    self.create_case_schema(appointment_id=appointment_id, patient_id=patient_id, prescription_type=prescription_type)
                    self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)
                    existing_doc_patient = self.get_one(appointment_id=appointment_id, patient_id=patient_id)
                    _output = self.db[self.collection].update_one(
                        {
                            'appointment_id': appointment_id,
                            'patient_id': patient_id,
                            'prescription_count': existing_doc_patient.get('prescription_count')
                        },
                        {
                            "$push": {key: {"$each": [value.dict()], "$position": 0}},
                            '$set': {"updated_at": datetime.now()}
                        }
                    )
                        
            else:
                    
                    self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
                    self.create_case_schema(
                        appointment_id=appointment_id, patient_id=patient_id, prescription_type=prescription_type)
                    self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)

                    existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)
                    value.set_created_at(datetime.now())
                    _output = self.db[self.collection].update_one(
                    {'appointment_id': appointment_id,
                    'patient_id': patient_id,
                    'prescription_count': existing_doc.get('prescription_count')},
                    {
                        "$push": {key: {"$each": [value.dict()], "$position": 0}},
                        '$set': {"updated_at": datetime.now()}
                    }
                )
            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": value.dict()
                }
            else:
                raise Exception(
                    'Failed to add the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def set_object(self, appointment_id: str, patient_id: str, prescription_type: PrescriptionType, key: str, value,
                   *args,
                   **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.create_case_schema(
                appointment_id=appointment_id, patient_id=patient_id, prescription_type=prescription_type)
            self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)

            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            value.set_created_at(datetime.now())

            _output = self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    "$set": {
                        key: value.dict(),
                        "updated_at": datetime.now()
                    }
                }
            )
            if _output.matched_count:

                if 'close_case' in kwargs and kwargs.get('close_case') is True:
                    # check if case is transferred
                    if value.transfer_case is True:
                        if value.transfer_details.notes in [None, '']:
                            value.transfer_details.notes = 'Case Transferred'

                        if value.transfer_details.referral_type == 'Internal':
                            ReferCaseDAO().refer_case_to_ayoo_doctor(referral_data=value.transfer_details)

                        elif value.transfer_details.referral_type == 'External':
                            ReferCaseDAO().refer_case_to_external_doctor(referral_data=value.transfer_details,
                                                                         close_case=True)
                        else:
                            raise Exception('Invalid transfer type')

                    case_details = self.get_appointment_details(
                        appointment_id=appointment_id, patient_id=patient_id)
                    set_query = {
                        'is_open': False,
                        'date_closed': datetime.now(),
                        'updated_at': datetime.now()
                    }
                    self.update_multiple(filter={'case_id': case_details.get(
                        'caseid')}, update={'$set': set_query})
                    self.db['Appointments'].update_many({'caseid': case_details.get('caseid')},
                                                        {'$set': {'case_open': False}})
                    patient_id = case_details.get("patient_id")
                    case_id = case_details.get("caseid")
                    remove_custom_fees(patient_id, case_id)

                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": value.dict()
                }
            else:
                raise Exception(
                    'Failed to add the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def patch_array(self, appointment_id: str, patient_id: str, index: int, key: str, value, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            created_at = existing_doc.get(key, [])[index].get("created_at")

            value.set_updated_at(datetime.now())
            value.set_created_at(created_at)

            _output = self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    "$set": {
                        f'{key}.{index}': value.dict(),
                        "updated_at": datetime.now()
                    }
                }
            )

            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": value.dict()
                }
            else:
                raise Exception(
                    'Failed to update the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def patch_object(self, appointment_id: str, patient_id: str, key: str, value, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            if existing_doc.get(key) in [{}, None]:
                raise Exception('No data found to update')

            created_at = existing_doc.get(key, {}).get("created_at")

            value.set_updated_at(datetime.now())
            value.set_created_at(created_at)

            _output = self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    "$set": {
                        f'{key}': value.dict(),
                        "updated_at": datetime.now()
                    }
                }
            )

            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": value.dict()
                }
            else:
                raise Exception(
                    'Failed to update the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def delete_from_array(self, appointment_id: str, patient_id: str, index: int, key: str, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            key_doc = existing_doc.get(key, [])

            key_doc.pop(index)
            _output = self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    "$set": {
                        "updated_at": datetime.now(),
                        f"{key}": key_doc
                    }
                }
            )

            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    }
                }
            else:
                raise Exception(
                    'Failed to delete the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def delete_object(self, appointment_id: str, patient_id: str, key: str, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(appointment_id=appointment_id, patient_id=patient_id, key=key)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            created_at = existing_doc.get(key, {}).get("created_at")
            if created_at is None:
                raise Exception('No record found to delete')

            _output = self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    "$set": {
                        "updated_at": datetime.now(),
                        f"{key}": {}
                    }
                }
            )

            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    }
                }
            else:
                raise Exception(
                    'Failed to delete the record as no record matched')

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def get_doctor_details(self, doctor_id: str):
        try:
            doctor_profile = DoctorProfileDAO().get(_id=str(doctor_id))
            doctor_info = DoctorDAO().get(_id=str(doctor_id), key_name='doctorid')
            service_provider = {
                'doctor_firstname': doctor_profile.firstname,
                'doctor_lastname': doctor_profile.lastname,
                'profile_name': doctor_info.profilename,
                'graduation': doctor_info.graduation,
                'masters': doctor_info.masters,
                'fellowship': doctor_info.fellowship,
                'specialization': doctor_info.specialization,
                'practice_area': doctor_info.practice_area,
                'interest_area': doctor_info.interest_area,
                'consultation_symptoms': doctor_info.consultation_symptoms,
                'consulting_duration_virtual': doctor_profile.consulting_duration_virtual,
                'consulting_duration_clinic': doctor_profile.consulting_duration_clinic,
                'consulting_fees_virtual': doctor_profile.consulting_fees_virtual,
                'consulting_fees_clinic': doctor_profile.consulting_fees_clinic,
                'profile_image': {
                    'image_id': doctor_info.image_id,
                    'profile_image_url': doctor_info.profile_image_url
                },
                'license_no': doctor_info.license,
                'signature_img': doctor_info.signature
            }
            return service_provider
        except Exception as e:
            Exception(
                f'Error occurred while fetching doctor details: {str(e)}')

    def create_lab_test_records(self, appointment_id: str, patient_id: str, prescription_type: PrescriptionType,
                                value: LabTests, *args,
                                **kwargs):
        try:
            self.create_case_schema(
                appointment_id=appointment_id, patient_id=patient_id, prescription_type=prescription_type)

            appointment_data = self.get_appointment_details(
                appointment_id=appointment_id, patient_id=patient_id)

            session_count = self.get_session_count(
                case_id=appointment_data.get('caseid', ''))
            session_count -= 1  # session count returns incremented result

            prescription_no = self.get_prescription_count(case_id=appointment_data.get('caseid', ''),
                                                          appointment_id=appointment_id, patient_id=patient_id)

            lab_test = CreateUploadRequest(fileName=value.lab_test_name,
                                           fileTag="578f236d-4533-4a48-b6d3-dbfbf8543823",
                                           caseId=appointment_data.get(
                                               'caseid', ''),
                                           doctorId=appointment_data.get(
                                               'doctorid', ''),
                                           requestForm=None,
                                           appointment_no=session_count,
                                           prescription_no=prescription_no,
                                           appointment_id=appointment_id
                                           )
            file_upload_request, msg = fileUploadContoller().file_upload_request(
                userid=patient_id,
                body=[lab_test])

            file_request_id = file_upload_request[0]

            if file_upload_request is None:
                raise Exception(msg)
            lab_tests = self.fetch_lab_tests(patient_id=patient_id, case_id=appointment_data.get('caseid', ''),
                                             appointment_no=None)

            filtered_lab_tests = [test for test in lab_tests if
                                  test.get('appointment_no') == session_count and test.get(
                                      'prescription_no') == prescription_no and str(test.get('request_id')) == str(
                                      file_request_id)]

            return {
                'msg': file_upload_request,
                'details': msg,
                'lab_tests': filtered_lab_tests[0]
            }
        except Exception as e:
            raise Exception(f'Error while adding lab tests: {str(e)}')

    def request_lab_tests(self, appointment_id: str, patient_id: str, prescription_type: PrescriptionType,
                          value: LabTests, *args,
                          **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.create_case_schema(
                appointment_id=appointment_id, patient_id=patient_id, prescription_type=prescription_type)

            self.check_if_crud_allowed(
                appointment_id=appointment_id, patient_id=patient_id, key='lab_tests')

            return self.create_lab_test_records(appointment_id=appointment_id, patient_id=patient_id,
                                                prescription_type=prescription_type,
                                                value=value)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f'{str(e)}')

    def update_lab_tests(self, appointment_id: str, patient_id: str, record_id: str, value: LabTests, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(
                appointment_id=appointment_id, patient_id=patient_id, key='lab_tests')
            case_sheet_data = self.get_one(appointment_id=appointment_id, patient_id=patient_id)
            if case_sheet_data.get('case_sheet_in_edit_mode') is False:
                raise Exception('Changes are not allowed for this case sheet')

            file_upload_request, msg = fileUploadContoller().update_file_name_of_lab_tests(
                appointment_no=case_sheet_data.get('session_no'), recordId=record_id, lab_test_name=value.lab_test_name
            )

            if file_upload_request is None:
                raise Exception(msg)

            return {
                'msg': file_upload_request,
                'details': msg
            }
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Error while updating lab tests: {str(e)}')

    def delete_lab_tests(self, appointment_id: str, patient_id: str, record_id: str, *args, **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            self.check_if_crud_allowed(
                appointment_id=appointment_id, patient_id=patient_id, key='lab_tests')
            case_sheet_data = self.get_one(appointment_id=appointment_id, patient_id=patient_id)
            if case_sheet_data.get('case_sheet_in_edit_mode') is False:
                raise Exception('Changes are not allowed for this case sheet')

            file_upload_request, msg = fileUploadContoller().delete_file_record_of_lab_tests(
                appointment_no=case_sheet_data.get('session_no'), recordId=record_id
            )

            if file_upload_request is None:
                raise Exception(msg)

            return {
                'msg': file_upload_request,
                'details': msg
            }
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Error while deleting lab tests: {str(e)}')

    def fetch_lab_tests(self, patient_id: str, case_id: str, appointment_no: Optional[int] = None,
                        prescription_no: Optional[int] = 1):
        try:
            user_locker = self.db["UserLocker"]

            if appointment_no is None:
                pipeline = [
                    {
                        "$match": {
                            "userid": patient_id,
                            "caseId": case_id,
                            "fileTag": "578f236d-4533-4a48-b6d3-dbfbf8543823",
                        },
                    },
                    {
                        "$project": {
                            "appointment_no": "$appointment_no",
                            "prescription_no": "$prescription_no",
                            "record_id": "$recordId",
                            "request_id": "$requestId",
                            "test_name": "$fileName",
                            "request_date": "$requestDate",
                            "file_paths": "$filePaths",
                            "report_upload_date": "$uploadedAt",
                            "_id": 0,
                        },
                    },
                    {
                        "$sort": {"appointment_no": -1, "prescription_no": -1, "requestDate": -1},
                    },
                ]
                result = list(user_locker.aggregate(pipeline))
            else:

                pipeline = [
                    {
                        "$match": {
                            "userid": patient_id,
                            "caseId": case_id,
                            "fileTag": "578f236d-4533-4a48-b6d3-dbfbf8543823",
                            "appointment_no": appointment_no,
                            "prescription_no": prescription_no,
                        },
                    },
                    {
                        "$project": {
                            "test_name": "$fileName",
                            "request_date": "$requestDate",
                            "_id": 0,
                        },
                    },
                    {
                        "$sort": {"requestDate": -1},
                    },
                ]
                result = list(user_locker.aggregate(pipeline))
            return result

        except Exception as e:
            raise Exception(f'Error while fetching lab tests: {str(e)}')

    def get_previous_prescription(self, current_session_no: int, case_id: str):
        try:
            previous_doc = self.db[self.collection].find_one(
                {'case_id': case_id,
                 'session_no': current_session_no - 1})
            if previous_doc is None:
                raise Exception('No old prescription found')
            previous_prescriptions = previous_doc.get('prescriptions', [])
            if len(previous_prescriptions) == 0:
                return None
            return previous_prescriptions[0]
        except Exception as e:
            raise Exception(str(e))

    def generate_pdf_for_prescription(self, appointment_id: str, patient_id: str, preview: bool = False, *args,
                                      **kwargs):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            pdf_ctrl = PDF_Gen(db=get_db(), mongo=mongodb_conn)

            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            expire_prescription = False

            patient_id = str(existing_doc.get('patient_id'))
            case_id = str(existing_doc.get('case_id'))
            case_type = existing_doc.get('prescription_type')
            patient_details = MentalHealthController(get_db(), mongodb_conn).get_patient_details(
                patientid=existing_doc.get('patient_id'))

            if case_type == 'Therapy':
                recommendations_data = existing_doc.get('therapy_recommendation', [])
            else:
                expire_prescription = True
                recommendations_data = existing_doc.get('work_up_plan_or_recommendation', [])

            recommendations = [item for item in recommendations_data if item.get('allowed_to_patient')]

            assessment = [item for item in existing_doc.get(
                'assessment') if item.get('allowed_to_patient')]

            medication = existing_doc.get('medication')

            lab_tests = self.fetch_lab_tests(patient_id=patient_id, case_id=case_id,
                                             appointment_no=existing_doc.get(
                                                 'session_no'),
                                             prescription_no=existing_doc.get('prescription_count'))

            follow_up = existing_doc.get('follow_up')

            referrals = ReferCaseDAO().get_referral_info(
                case_id=existing_doc.get('case_id'), doctor_id=existing_doc.get('case_doctor'))

            pdf_data_present = True
            if not recommendations and not assessment and not medication and not follow_up and not lab_tests:
                pdf_data_present = False

            # if existing_doc.get('session_no', 1) == 1 and pdf_data_present is False and existing_doc.get(
            #         'is_prescription_issued') is False:
            #     raise Exception(
            #         f'At least one of the fields from assessment, medications, recommendations, follow up or lab test is required before issuing the prescription')

            pdf_data = dict(
                patient_id=patient_id,
                case_id=case_id,
                case_type=case_type,
                appointment_id=appointment_id,
                appointment_type=existing_doc.get('appointment_type'),
                appointment_slot=existing_doc.get('appointment_slot'),
                preview=not preview,
                patient_details=patient_details,
                doctor_details=self.get_doctor_details(
                    doctor_id=existing_doc.get('case_doctor')),
                assessment=assessment,
                lab_tests=lab_tests,
                medication=medication,
                recommendations=recommendations,
                follow_up=follow_up,
                referrals=referrals
            )
            if preview is True and existing_doc.get('prescription_issue_date') is not None:
                file_date = str(existing_doc.get('prescription_issue_date'))
            else:
                file_date = str(datetime.now().date())

            pdf_url = pdf_ctrl.gen_pdf(dic=pdf_data,
                                       doc_id=file_date + f'-{existing_doc.get("prescription_count")}.pdf')

            if existing_doc.get('consultation_type') in ['Modify', 'Re-Issue']:
                is_case_sheet_submitted = True
                case_sheet_submit_date = datetime.now()
                case_sheet_in_edit_mode = False
            else:
                is_case_sheet_submitted = False
                case_sheet_submit_date = None
                case_sheet_in_edit_mode = True

            db_update_dict = {
                'prescription_preview': preview,
                'is_prescription_issued': True,
                'is_prescription_issued_for_current_record': False,

                'case_sheet_in_edit_mode': case_sheet_in_edit_mode,
                'prescription_fields_in_edit_mode': False,
                'is_case_sheet_submitted': is_case_sheet_submitted,
                'case_sheet_submit_date': case_sheet_submit_date,

                'prescription_allowed_to_patient': True,
                'prescription_issue_date': datetime.now(),

                'prescription_s3_object_key': pdf_url.get('s3_object_key', None),
                'prescription_s3_object_url': pdf_url.get('s3_url', None),

                'is_reissue_prescription': True if existing_doc.get('consultation_type') == 'ReIssue' else False,
                'reissue_reason': existing_doc.get('reissue_reason', None),

                'is_modified_prescription': True if existing_doc.get('consultation_type') == 'Modify' else False,
                'modify_reason': existing_doc.get('modify_reason', None)
                # **pdf_url
            }
            if preview is True:
                self.db[self.collection].update_one(
                    {'appointment_id': appointment_id,
                     'patient_id': patient_id,
                     'prescription_count': existing_doc.get('prescription_count')
                     }, {
                        '$set': {
                            'updated_at': datetime.now(),
                            'prescription_preview': preview
                        }
                    })

            else:
                if expire_prescription:
                    self.expire_prescription(appointment_id=appointment_id)
                db_update_dict['is_prescription_issued_for_current_record'] = True
                db_update_dict['prescription_s3_object_key'] = db_update_dict.get(
                    'prescription_s3_object_key') if pdf_data_present else None
                db_update_dict['prescription_s3_object_url'] = db_update_dict.get(
                    'prescription_s3_object_url') if pdf_data_present else None

                self.db[self.collection].update_one(
                    {'appointment_id': appointment_id,
                     'patient_id': patient_id,
                     'prescription_count': existing_doc.get('prescription_count')},
                    {

                        '$set': {
                            'updated_at': datetime.now(),
                            **db_update_dict
                        }
                    }
                )
                frbs_ctrl = FireBaseNotificationController(
                    db=get_db(), mongo=mongodb_conn)

                prescription_notif = PrescriptionEvent(case_id=case_id,
                                                       patient_id=patient_id,
                                                       patient_name=patient_details.get(
                                                           'firstname') + ' ' + patient_details.get('lastname'),
                                                       upload_date=datetime.now(),
                                                       remarks="None")

                frbs_ctrl.prescription_upload(
                    prescription_event=prescription_notif)

            db_update_dict['is_pdf_data_present'] = pdf_data_present
            return db_update_dict
        except Exception as e:
            raise Exception(f'Error while creating pdf: {str(e)}')

    def generate_prescription(self, appointment_id: str, patient_id: str, preview: bool = False):
        try:
            appointment_data = AppointmentDAO().list(
                skip=0, limit=1, filter=CaseSheetQueryFields(appointment_id=appointment_id)
            )
            prescriptions = []
            for _patient in appointment_data.get('patients', []):
                _upload = self.generate_pdf_for_prescription(
                    appointment_id=appointment_id,
                    patient_id=_patient,
                    preview=preview
                )
                prescriptions.append({
                    'patient_id':_patient,
                    'prescriptions': _upload
                })

            return {
                'msg': 'Prescriptions generated',
                'response': prescriptions
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def expire_prescription(self, appointment_id: str):
        try:
            existing_doc = self.get_one(appointment_id=appointment_id)
            appointment_slot = existing_doc.get('appointment_slot')
            session_no = existing_doc.get('session_no')
            prescription_count = existing_doc.get('prescription_count')
            case_id = existing_doc.get('case_id')

            prev_record = self.db[self.collection].find_one(
                {
                    "case_id": case_id,
                    "$or": [
                        {"appointment_slot": {"$lt": appointment_slot}},
                        {
                            "appointment_slot": appointment_slot,
                            "session_no": {"$lt": session_no}
                        },
                        {
                            "appointment_slot": appointment_slot,
                            "session_no": session_no,
                            "prescription_count": {"$lt": prescription_count}
                        }
                    ]
                },
                sort=[("appointment_slot", DESCENDING),
                      ("session_no", DESCENDING),
                      ("prescription_count", DESCENDING)]
            )
            pdf_ctrl = PDF_Gen(db=get_db(), mongo=mongodb_conn)
            if prev_record and prev_record.get('prescription_s3_object_key') not in [None, '']:
                updated_pdf = pdf_ctrl.add_expired_watermark(
                    prescription_s3_object_key=prev_record.get('prescription_s3_object_key'))
                if 's3_object_key' in updated_pdf:
                    self.db[self.collection].find_one_and_update({
                        'appointment_id': prev_record.get('appointment_id'),
                        'session_no': prev_record.get('session_no'),
                        'prescription_count': prev_record.get('prescription_count')
                    }, {
                        '$set': {
                            'updated_at': datetime.now(),
                            'prescription_s3_object_key': updated_pdf.get('s3_object_key'),
                            'prescription_s3_object_url': updated_pdf.get('s3_url'),
                        }
                    }
                    )

            return prev_record
        except Exception as e:
            raise Exception(str(e))

    def expire_current_prescription(self, appointment_id: str):
        try:
            record = self.get_one(appointment_id=appointment_id)
            if not record or not record.get('prescription_s3_object_key'):
                return None

            # Skip adding expire watermark if case type is Therapy
            if record.get('prescription_type') == 'Therapy':
                return record

            pdf_ctrl = PDF_Gen(db=get_db(), mongo=mongodb_conn)
            updated_pdf = pdf_ctrl.add_expired_watermark(
                prescription_s3_object_key=record.get('prescription_s3_object_key'))

            if 's3_object_key' in updated_pdf:
                self.db[self.collection].find_one_and_update({
                    'appointment_id': record.get('appointment_id'),
                    'prescription_count': record.get('prescription_count')
                }, {
                    '$set': {
                        'updated_at': datetime.now(),
                        'prescription_s3_object_key': updated_pdf.get('s3_object_key'),
                        'prescription_s3_object_url': updated_pdf.get('s3_url'),
                    }
                })

            return record
        except Exception as e:
            raise Exception(f"Error applying expired watermark to prescription: {str(e)}")

    def submit_case_sheet(self, appointment_id: str, patient_id: str):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            if not existing_doc.get('is_prescription_issued_for_current_record'):
                self.generate_pdf_for_prescription(
                    appointment_id=appointment_id, patient_id=patient_id, preview=False)

            self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {

                    '$set': {
                        'updated_at': datetime.now(),
                        'is_case_sheet_submitted': True,
                        'case_sheet_submit_date': datetime.now(),
                        'case_sheet_in_edit_mode': False,
                        'prescription_fields_in_edit_mode': False,
                    }
                }
            )

            return {
                'msg': 'Case sheet submitted'
            }
        except Exception as e:
            raise Exception(str(e))

    def submit_case_sheet_button(self, appointment_id: str):
        try:
            appointment_data = AppointmentDAO().list(
                skip=0, limit=1, filter=CaseSheetQueryFields(appointment_id=appointment_id)
            )
            for _patient in appointment_data.get('patients', []):
                self.submit_case_sheet(appointment_id=appointment_id, patient_id=_patient)

            return {
                'msg':'Case sheet submitted'
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def update_list_dates(self, list_object: List, *args, **kwargs):
        try:
            if not len(list_object):
                return []

            for entity in list_object:

                if 'key_name' in kwargs and kwargs.get('key_name') == 'medication':
                    if entity.get('sos') is False:
                        start_date = datetime.strptime(
                            entity.get('start_date'), '%Y-%m-%d').date()
                        created_at = entity.get('created_at').date()
                        time_diff_to_start_med = start_date - created_at
                        if time_diff_to_start_med == 0:
                            entity['start_date'] = datetime.today().date()
                        else:
                            entity['start_date'] = datetime.today().date(
                            ) + timedelta(days=time_diff_to_start_med.days)
                        entity['end_date'] = str(
                            datetime.today().date() + timedelta(days=int(entity.get('duration_in_days'))))
                        entity['start_date'] = str(entity.get('start_date'))

                entity['created_at'] = datetime.now()
                entity['updated_at'] = None

            return list_object

        except Exception as e:
            raise Exception(
                f'Error occurred while updating dates for list object: {e}')

    def update_dict_dates(self, dict_object: {}, *args, **kwargs):
        try:
            if dict_object in [{}, None]:
                return {}

            dict_object['created_at'] = datetime.now()
            dict_object['updated_at'] = None
            return dict_object

        except Exception as e:
            raise Exception(
                f'Error occurred while updating dates for dict object: {e}')

    def update_lab_tests_dates(self, appointment_id: str, patient_id: str, case_id: str, session_no: int,
                               prescription_count: int, prescription_type: str):
        try:
            all_lab_tests = self.fetch_lab_tests(patient_id=patient_id, case_id=case_id, appointment_no=session_no,
                                                 prescription_no=prescription_count)

            for test in all_lab_tests:
                self.create_lab_test_records(appointment_id=appointment_id, patient_id=patient_id,
                                             prescription_type=PrescriptionType(
                                                 prescription_type),
                                             value=LabTests(
                                                 lab_test_name=test.get(
                                                     'test_name')
                                             ))

        except Exception as e:
            raise Exception(f'Error occurred while cloning lab tests: {e}')

    def re_issue_prescription(self, appointment_id: str, patient_id: str, data: UpdatePrescription, existing_doc: {}):
        try:
            new_doc_data = {
                **existing_doc,
                'case_sheet_id': str(uuid.uuid4()),
                'appointment_type': 'Virtual',
                'prescription_count': existing_doc.get('prescription_count', 1) + 1,
                'prescription_preview': False,
                'is_prescription_issued': True if existing_doc.get('is_prescription_issued') not in [None,
                                                                                                     ''] else False,
                'is_prescription_issued_for_current_record': False,
                'case_sheet_in_edit_mode': False,
                'prescription_fields_in_edit_mode': False,
                'is_case_sheet_submitted': False,
                'case_sheet_submit_date': None,
                'prescription_allowed_to_patient': False,
                'prescription_issue_date': None,
                'prescription_s3_object_key': None,
                'prescription_s3_object_url': None,
                'is_reissue_prescription': True,
                'reissue_reason': data.update_reason,
                'is_modified_prescription': False,
                'modify_reason': None,
                'created_at': datetime.now(),
                'updated_at': None
            }

            del new_doc_data['_id']

            self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    '$set': {'case_sheet_in_edit_mode': False,
                             'prescription_fields_in_edit_mode': False,
                             'updated_at': datetime.now()}
                }
            )
            self.db[self.collection].insert_one(new_doc_data)

            self.update_lab_tests_dates(patient_id=existing_doc.get('patient_id'),
                                        appointment_id=appointment_id,
                                        case_id=existing_doc.get('case_id'),
                                        session_no=existing_doc.get(
                                            'session_no', 1),
                                        prescription_count=existing_doc.get(
                                            'prescription_count', 1),
                                        prescription_type=existing_doc.get(
                                            'prescription_type')
                                        )

            return self.generate_pdf_for_prescription(appointment_id=appointment_id, patient_id=patient_id,
                                                      preview=False)

        except Exception as e:
            raise Exception(
                f'Error while re-issuing the prescription: {str(e)}')

    def modify_prescription(self, appointment_id: str, patient_id: str, data: UpdatePrescription, existing_doc: {}):
        try:
            new_doc_data = {
                **existing_doc,
                'case_sheet_id': str(uuid.uuid4()),
                'appointment_type': 'Virtual',
                'prescription_count': existing_doc.get('prescription_count', 1) + 1,
                'prescription_preview': False,
                'is_prescription_issued': True if existing_doc.get('is_prescription_issued') not in [None,
                                                                                                     ''] else False,
                'is_prescription_issued_for_current_record': False,
                'case_sheet_in_edit_mode': True,
                'prescription_fields_in_edit_mode': True,
                'is_case_sheet_submitted': False,
                'case_sheet_submit_date': None,
                'prescription_allowed_to_patient': False,
                'prescription_issue_date': None,
                'prescription_s3_object_key': None,
                'prescription_s3_object_url': None,
                'is_reissue_prescription': False,
                'reissue_reason': None,
                'is_modified_prescription': True,
                'modify_reason': data.update_reason,
                'created_at': datetime.now(),
                'updated_at': None
            }

            del new_doc_data['_id']

            self.db[self.collection].update_one(
                {'appointment_id': appointment_id,
                 'patient_id': patient_id,
                 'prescription_count': existing_doc.get('prescription_count')},
                {
                    '$set': {'case_sheet_in_edit_mode': False,
                             'prescription_fields_in_edit_mode': False,
                             'updated_at': datetime.now()}
                }
            )
            self.db[self.collection].insert_one(new_doc_data)

            self.update_lab_tests_dates(patient_id=existing_doc.get('patient_id'),
                                        appointment_id=appointment_id,
                                        case_id=existing_doc.get('case_id'),
                                        session_no=existing_doc.get(
                                            'session_no', 1),
                                        prescription_count=existing_doc.get(
                                            'prescription_count', 1),
                                        prescription_type=existing_doc.get(
                                            'prescription_type')
                                        )

        except Exception as e:
            raise Exception(
                f'Error while modifying the prescription: {str(e)}')

    def re_issue_and_modify_prescription(self, appointment_id: str, patient_id: str, data: UpdatePrescription):
        try:
            self.check_if_case_is_open(appointment_id=appointment_id, patient_id=patient_id)
            existing_doc = self.get_one(appointment_id=appointment_id, patient_id=patient_id)

            is_reissue = True if data.update_type == 'ReIssue' else False
            is_modify = True if data.update_type == 'Modify' else False

            doctor_notes = DoctorNotes(
                created_at=datetime.now(),
                notes=data.update_reason,
                is_reissue_notes=is_reissue,
                is_modify_notes=is_modify
            )

            existing_doc['doctor_notes'] = dict(doctor_notes),

            existing_doc['chief_complain'] = self.update_list_dates(
                list_object=existing_doc.get('chief_complain', []), key_name='chief_complain')

            existing_doc['substance_use'] = self.update_list_dates(
                list_object=existing_doc.get('substance_use', []), key_name='substance_use')

            existing_doc['medication'] = self.update_list_dates(
                list_object=existing_doc.get('medication', []), key_name='medication')

            existing_doc['assessment'] = self.update_list_dates(
                list_object=existing_doc.get('assessment', []))

            existing_doc['work_up_plan_or_recommendation'] = self.update_list_dates(
                list_object=existing_doc.get('work_up_plan_or_recommendation', []))

            existing_doc['therapy_recommendation'] = self.update_list_dates(
                list_object=existing_doc.get('therapy_recommendation', []))

            existing_doc['follow_up'] = self.update_dict_dates(
                dict_object=existing_doc.get('follow_up', {}))

            existing_doc['consultation_type'] = data.update_type

            re_issue_response = None

            if is_reissue:
                existing_doc['is_reissue_prescription'] = True
                existing_doc['reissue_reason'] = data.update_reason

                re_issue_response = self.re_issue_prescription(appointment_id=appointment_id, patient_id=patient_id,
                                                               data=data,
                                                               existing_doc=existing_doc)

                response_text = 'Case sheet re-issued'

            elif is_modify:

                existing_doc['is_modified_prescription'] = True
                existing_doc['modify_reason'] = data.update_reason

                self.modify_prescription(appointment_id=appointment_id, patient_id=patient_id, data=data,
                                         existing_doc=existing_doc)
                response_text = 'Case sheet modified'
            else:
                raise Exception(
                    'Invalid update type. Allowed values: ReIssue, Modify')

            return {
                'msg': response_text,
                'is_reissue': is_reissue,
                're_issue_response': re_issue_response,
                'is_modify': is_modify,
            }

        except Exception as e:
            raise Exception(f'Case sheet reissue-modify error: {str(e)}')

    def get_clinical_history_of_patient(self, patient_id: str):
        try:
            pipeline = [
                {"$match": {"patient_id": patient_id}},
                {"$sort": {"session_no": -1, "prescription_count": -1}},
                {"$group": {
                    "_id": "$case_id",
                    "case_data": {"$first": "$$ROOT"}
                }},
                {"$replaceRoot": {"newRoot": "$case_data"}},
                {"$project": {"_id": 0}}
            ]

            result = list(self.db[self.collection].aggregate(pipeline))
            clinical_records = [ClinicalHistory(**res) for res in result]

            for data in clinical_records:
                if data.case_summary.get('transfer_case'):
                    transfer_doctor = data.case_summary.get(
                        'transfer_details', {}).get('referred_to_doctor', '')
                    transfer_doctor_detail = self.get_doctor_details(
                        doctor_id=transfer_doctor)
                    data.case_summary = {key: data.case_summary.get(key) for key in
                                         ['case_closure_diagnosis', 'case_closure_doctor_notes',
                                          'case_closure_recommendation']}
                    data.case_transferred_to = {'doctor_id': transfer_doctor,
                                                'doctor_name': transfer_doctor_detail.get('profile_name')}

            user_data = UserController(get_db(), mongodb_conn).get_user_details(
                userid=patient_id, mongo=mongodb_conn)
            if user_data is None:
                user_data = UserController(get_db(), mongodb_conn).get_relative_details(userid=patient_id,
                                                                                        mongo=mongodb_conn)
                if user_data is None:
                    raise Exception('Invalid patient ID')

            return {
                'patient_id': patient_id,
                'clinical_records': clinical_records,
                'patient_details': user_data
            }

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Clinical history error : {str(e)}')

    def clear_follow_ups(self, case_id):
        if case_id is None:
            case_id = ""
        query = {
            "case_id": case_id + "44444444444444444"  # temporary disabling clear follow up with this sm
        }

        try:
            return self.db[self.collection].update_many(query, {"$set": {"follow_up": {}}})
        except Exception as e:
            raise HTTPException(500, {"status": "Failed", "details": str(e)})

    def fetch_follow_up_appointments(self, patient_id: str = None, relationship: str | None = None):
        try:
            query = {
                "follow_up.next_appointment": {"$gte": datetime.now() - timedelta(days=60)},
                "is_open": True
            }
            if patient_id is not None:
                query["patient_id"] = patient_id

            result = list(self.db[self.collection].find(query).clone())
            follow_up_appointments = []

            from ayoo_backend.api.patient_models import UIUsersView
            from ayoo_backend.api.OtpGenerator import OtpGenerator
            all_users = UserController(db=get_db(), otp_generator=OtpGenerator, mongo=mongodb_conn).get_all_users(
                sort_filter_data=UIUsersView(page_no=1))

            user_data = all_users.get('user_data')
            user_dict = {user['userid']: user for user in user_data}

            for data in result:
                prescription_type = data.get('prescription_type')
                if prescription_type == 'MedicalHealth':
                    prescription_type = 'Medical Health'
                patient = user_dict.get(data.get('patient_id'), {})
                if patient:
                    dict_to_append = dict(
                        case_id=data.get('case_id'),
                        patient_id=data.get('patient_id'),
                        patient_name=f"{patient.get('firstname', '')} {patient.get('lastname', '')}",
                        relationship=relationship,
                        doctor_id=data.get('case_doctor'),
                        doctor_name=data.get('doctor_name'),
                        appointment_id=data.get('appointment_id'),
                        next_appointment_date=data.get(
                            'follow_up', {}).get('next_appointment'),
                        appointment_type=data.get('follow_up', {}).get(
                            'appointment_type', 'Virtual'),
                        care_type=prescription_type,
                        patient_details=patient
                    )
                    follow_up_appointments.append(dict_to_append)

            return {
                'follow_up_appointments': follow_up_appointments
            }

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Follow up appointments fetch error : {str(e)}')

    def share_case_sheet_to_patient(self, data: ShareCaseSheet):
        try:
            from ayoo_backend.api.aws_msg_email_generator import AWSEmailAndMsgSender
            aws_mail_ctrl = AWSEmailAndMsgSender()

            recipient = data.email
            email_body = f'Please find attached the requested prescription.'

            if data.email_admin:

                from ayoo_backend.api.api_configs import current_env
                if current_env == 'prod':
                    recipient = '<EMAIL>'
                else:
                    recipient = '<EMAIL>'
                email_body = f"Please send the attached PDF to the patient, {data.patient_name} via whatsapp. Patient's contact number: {data.patient_phone}."

            response = aws_mail_ctrl.send_pdf_on_mail(pdf_url=data.pdf_url,
                                                      case_id=data.case_id,
                                                      recipient=recipient,
                                                      email_body=email_body)

            return {
                'msg': 'PDF sent via email',
                'response': response
            }
        except Exception as e:
            raise HTTPException(
                status_code=409, detail=f'Error occurred while sharing prescription PDF: {str(e)}')

    def get_case_by_doctor_id_and_patient_id(self, patient_id: str, doctor_id: str):
        query = GetByPatientIdAndDoctorId(
            patient_id=patient_id,
            doctor_id=doctor_id
        )
        sort = CaseIDSort(sort_by_appointment_slot=SortOrder.desc)
        return AppointmentDAO().list(limit=1, filter=query, sort=sort)

    def get_all_active_cases(self, patient_id: str):
        return list(self.db[self.collection].find(
            {
                "patient_id": patient_id,
                "is_open": True
            }
        ))


class ReferCaseDAO(BaseMongoDAO):
    collection = "ReferCase"
    _model_class = ReferralInformation

    def check_open_case_with_a_doctor(self, patient_id: str, doctor_id: str):
        try:
            filters = CaseSheetAppointmentQueryFields(
                patient_id=patient_id, doctorid=doctor_id, case_open=True)

            appointment_data = AppointmentDAO().list(
                filter=filters,
                sort=CaseIDSort()
            )
            if len(appointment_data) and appointment_data[0].dict().get('case_open', True) is True:
                return True, {
                    'case_id': appointment_data[0].caseid,
                    'doctor_name': appointment_data[0].doctor_name,
                    'case_open': appointment_data[0].case_open
                }

            referral_data = ReferCaseDAO().list(
                filter=ReferralQueryFields(
                    patient_id=patient_id, referred_to=doctor_id)
            )
            if len(referral_data):
                return True, {
                    'case_id': referral_data[0].referred_case_id,
                    'doctor_name': referral_data[0].referred_to_name,
                    'case_open': True
                }

            return False, 'No open case found'
        except Exception as e:
            raise Exception(f'Case open check error: {str(e)}')

    def get_doctors_available_for_referral(self, patient_id: str):
        try:
            all_open_cases = AppointmentDAO().list(
                filter=CaseSheetAppointmentQueryFields(
                    patient_id=patient_id, case_open=True),
                sort=CaseIDSort()
            )
            active_case_doctors = [
                active_case.doctorid for active_case in all_open_cases]
            referral_cases = ReferCaseDAO().list(
                filter=ReferralQueryFields(patient_id=patient_id)
            )
            referral_doctors = [value for referred_case in referral_cases for value in
                                (referred_case.referred_by, referred_case.referred_to)]

            active_doctors_with_patient = list(
                set(active_case_doctors) | set(referral_doctors))

            all_doctors = DoctorDAO().list(filter=DoctorQueryFields())
            all_active_doctors = [
                active_doctor for active_doctor in all_doctors if active_doctor.is_active]

            available_doctors = [doctor for doctor in all_active_doctors if
                                 doctor.doctorid not in active_doctors_with_patient]

            return {
                'msg': 'Doctor list fetched',
                'available_doctors': available_doctors
            }

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Fetch doctors to refer error : {str(e)}')

    def get_referral_info(self, case_id: str, doctor_id: str):
        try:
            refer_list = list(self.db[self.collection].find(
                {'existing_case_id': case_id},
                {'_id': 0}
            ))
            for case in refer_list:
                if 'updates' in case:
                    for record in case.get('updates', []):
                        # record['update_allowed'] = (record.get('updates_from_doctor_id', '') == doctor_id)
                        record['update_allowed'] = False
                        if record.get('updates_from_doctor_id', '') == doctor_id:
                            date_recorded_notes = record.get('date_recorded')
                            case_info = CaseSheetDAO().get_all(case_id=case_id)
                            if 'case_sheets' in case_info:
                                latest_record = case_info.get('case_sheets')[0]
                                if latest_record.get('is_case_sheet_submitted') in [None,
                                                                                    False] and date_recorded_notes > latest_record.get(
                                    'date_created'):
                                    record['update_allowed'] = True

            return refer_list

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Fetch referral details error : {str(e)}')

    def get_referred_by_info(self, case_id: str):
        try:
            return self.db[self.collection].find_one(
                {'referred_case_id': case_id},
                {'_id': 0}
            )

        except Exception as e:
            raise Exception(f'Fetch referred by details error : {str(e)}')

    def get_updates_made_to_referrer(self, case_id: str):
        try:
            referred_case_details = self.db[self.collection].find_one(
                {'referred_case_id': case_id},
                {'_id': 0}
            )
            if referred_case_details is not None and 'updates' in referred_case_details:
                for record in referred_case_details.get('updates', []):
                    # record['update_allowed'] = (record.get('updates_from_doctor_id', '') == referred_case_details.get('referred_to'))

                    doctor_id = referred_case_details.get('referred_to')

                    record['update_allowed'] = False
                    if record.get('updates_from_doctor_id', '') == doctor_id:
                        date_recorded_notes = record.get('date_recorded')
                        case_info = CaseSheetDAO().get_all(case_id=case_id)
                        if 'case_sheets' in case_info:
                            latest_record = case_info.get('case_sheets')[0]
                            if latest_record.get('is_case_sheet_submitted') in [None,
                                                                                False] and date_recorded_notes > latest_record.get(
                                'date_created'):
                                record['update_allowed'] = True
            return referred_case_details

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Fetch updates to referrer error : {str(e)}')

    def refer_case_to_ayoo_doctor(self, referral_data: ReferCaseView):
        try:
            appointment_case_sheet = CaseSheetDAO().get_one(
                appointment_id=referral_data.appointment_id)
            appointment_no = appointment_case_sheet.get('session_no', 1)

            patient_id = appointment_case_sheet.get('patient_id')
            # if patient_id in [None, '']:
            #     appointment_data = AppointmentDAO().get(_id=referral_data.current_case_id, key_name='caseid')
            #     patient_id = appointment_data.patient_id

            case_exists, case_check_msg = ReferCaseDAO().check_open_case_with_a_doctor(
                patient_id=patient_id, doctor_id=referral_data.referred_to_doctor)
            if case_exists is True:
                raise Exception(
                    f'A case {case_check_msg.get("case_id", "")} is already open with doctor: {case_check_msg.get("doctor_name")}')

            # create case
            new_case_id = MentalHealthController(
                get_db(), mongodb_conn).generate_case_id()

            referral_notes = ReferralUpdateWithDoctorInfo(
                note_id=str(uuid.uuid4()),
                appointment_no=appointment_no,
                notes=referral_data.notes,
                date=str(datetime.now().date()),
                updates_from_doctor_id=appointment_case_sheet.get('case_doctor'),
                updates_from_doctor_name=appointment_case_sheet.get('doctor_name'),
                updates_from_type=ReferringPersonTypes.Referrer,
                date_recorded=datetime.now()
            )

            doctor_info = CaseSheetDAO().get_doctor_details(
                doctor_id=referral_data.referred_to_doctor)

            referred_dict = ReferralInformation(patient_id=appointment_case_sheet.get('patient_id'),
                                                existing_case_id=referral_data.current_case_id,
                                                referred_case_id=new_case_id,
                                                referral_type='Internal',
                                                referred_by=appointment_case_sheet.get(
                                                    'case_doctor'),
                                                referred_to=referral_data.referred_to_doctor,
                                                referred_by_name=appointment_case_sheet.get(
                                                    'doctor_name'),
                                                referred_to_name=doctor_info.get(
                                                    'profile_name'),
                                                # referral_for=referral_data.referral_for,
                                                updates=[referral_notes],
                                                date_referred=datetime.now(),
                                                created_at=datetime.now())

            return ReferCaseDAO().create(_obj=referred_dict)

        except Exception as e:
            raise Exception(f'Refer case error : {str(e)}')

    def refer_case_to_external_doctor(self, referral_data: ReferCaseView, close_case: bool = False):
        try:
            existing_case_info = CaseSheetDAO().get_all(
                case_id=referral_data.current_case_id)
            appointment_case_sheet = CaseSheetDAO().get_one(
                appointment_id=referral_data.appointment_id)
            appointment_no = appointment_case_sheet.get('session_no', 1)
            referral_notes = ReferralUpdateWithDoctorInfo(
                note_id=str(uuid.uuid4()),
                appointment_no=appointment_no,
                notes=referral_data.notes,
                date=str(datetime.now().date()),
                updates_from_doctor_id=existing_case_info.get('case_doctor'),
                updates_from_doctor_name=existing_case_info.get('doctor_name'),
                updates_from_type=ReferringPersonTypes.Referrer,
                date_recorded=datetime.now()
            )

            referred_dict = ReferralInformation(patient_id=existing_case_info.get('patient_id'),
                                                existing_case_id=referral_data.current_case_id,
                                                referred_case_id=None,
                                                referral_type='External',
                                                is_case_transferred=close_case,
                                                referred_by=existing_case_info.get(
                                                    'case_doctor'),
                                                referred_to=None,
                                                referred_by_name=existing_case_info.get(
                                                    'doctor_name'),
                                                referred_to_name=referral_data.external_referral_doctor_name,
                                                # referral_for=referral_data.referral_for,
                                                updates=[referral_notes],
                                                date_referred=datetime.now(),
                                                created_at=datetime.now())

            return ReferCaseDAO().create(_obj=referred_dict)

        except Exception as e:
            raise Exception(f'Refer case error : {str(e)}')

    def refer_existing_case(self, referral_data: ReferCaseView):
        try:
            if referral_data.notes in [None, '']:
                raise Exception('Enter notes for referral')
            if referral_data.referral_type == 'Internal':
                return self.refer_case_to_ayoo_doctor(referral_data=referral_data)
            else:
                return self.refer_case_to_external_doctor(referral_data=referral_data)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f'{str(e)}')

    def update_to_referee(self, referral_data: UpdateToReferee):
        try:
            existing_case_info = CaseSheetDAO().get_all(
                case_id=referral_data.current_case_id)

            appointment_case_sheet = CaseSheetDAO().get_one(
                appointment_id=referral_data.appointment_id)
            appointment_no = appointment_case_sheet.get('session_no', 1)

            if appointment_case_sheet.get('is_case_sheet_submitted'):
                raise Exception('Case sheet has been submitted, hence editing note is not allowed')

            referral_notes = ReferralUpdateWithDoctorInfo(
                note_id=str(uuid.uuid4()),
                appointment_no=appointment_no,
                notes=referral_data.notes,
                date=str(datetime.now().date()),
                updates_from_doctor_id=existing_case_info.get('case_doctor'),
                updates_from_doctor_name=existing_case_info.get('doctor_name'),
                updates_from_type=ReferringPersonTypes.Referrer,
                date_recorded=datetime.now()
            )

            _output = self.db[self.collection].update_one(
                {"existing_case_id": referral_data.current_case_id,
                 "referred_case_id": referral_data.referee_case_id},
                {
                    "$push": {"updates": {"$each": [referral_notes.dict()], "$position": 0}},
                    '$set': {"updated_at": datetime.now()}
                }
            )
            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": referral_notes.dict()
                }
            else:
                raise Exception(
                    'Failed to add the record as no record matched')

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Update referee doctor error : {str(e)}')

    def update_to_referrer(self, referral_data: UpdateToReferrer):
        try:
            existing_case_info = CaseSheetDAO().get_all(
                case_id=referral_data.current_case_id)

            appointment_case_sheet = CaseSheetDAO().get_one(
                appointment_id=referral_data.appointment_id)
            appointment_no = appointment_case_sheet.get('session_no', 1)

            if appointment_case_sheet.get('is_case_sheet_submitted'):
                raise Exception('Case sheet has been submitted, hence editing note is not allowed')

            referral_notes = ReferralUpdateWithDoctorInfo(
                note_id=str(uuid.uuid4()),
                appointment_no=appointment_no,
                notes=referral_data.notes,
                date=str(datetime.now().date()),
                updates_from_doctor_id=existing_case_info.get('case_doctor'),
                updates_from_doctor_name=existing_case_info.get('doctor_name'),
                updates_from_type=ReferringPersonTypes.Referee,
                date_recorded=datetime.now()
            )

            _output = self.db[self.collection].update_one(
                {"existing_case_id": referral_data.referrer_case_id,
                 "referred_case_id": referral_data.current_case_id},
                {
                    "$push": {"updates": {"$each": [referral_notes.dict()], "$position": 0}},
                    '$set': {"updated_at": datetime.now()}
                }
            )
            if _output.matched_count:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": referral_notes.dict()
                }
            else:
                raise Exception(
                    'Failed to add the record as no record matched')

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Update referee doctor error : {str(e)}')

    def update_referral_notes(self, note_id: str, referral_data: UpdateToReferee, doctor_id: str):
        try:
            appointment_case_sheet = CaseSheetDAO().get_one(
                appointment_id=referral_data.appointment_id)
            appointment_no = appointment_case_sheet.get('session_no', 1)
            if appointment_case_sheet.get('is_case_sheet_submitted'):
                raise Exception('Case sheet has been submitted, hence editing note is not allowed')

            _output = self.db[self.collection].update_one(
                {"updates": {
                    "$elemMatch": {
                        "note_id": note_id,
                        "updates_from_doctor_id": doctor_id,
                        "appointment_no": str(appointment_no)
                    }}
                }, {"$set": {
                    # "updates.$.appointment_no":appointment_no,
                    "updates.$.notes": referral_data.notes,
                    "updates.$.date": str(datetime.now().date()),
                    "updates.$.date_recorded": datetime.now()
                }
                }
            )
            if _output:
                return {
                    "status": "Success",
                    "details": {
                        "matched_count": _output.matched_count,
                        "modified_count": _output.modified_count
                    },
                    "data_updated": referral_data.dict()
                }
            else:
                raise Exception(
                    'Failed to add the record as no record matched')

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f'Update referral notes error : {str(e)}')
