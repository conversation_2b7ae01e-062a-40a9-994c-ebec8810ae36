from .baseDTO import GetModelDTO, BaseModel, BaseD<PERSON>
from typing import List, Optional
from datetime import date, timedelta, datetime
from ..DAOs.clinicDAO import <PERSON><PERSON><PERSON>
from ..DAOs.doctorDAO import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GetDoctor
from ..DAOs.userDAO import <PERSON><PERSON><PERSON><PERSON>
from ..DAOs.relativeDAO import RelativeDAO, RelativeListQueryFields
from ..DAOs.jitsiDAO import JitsiDA<PERSON>
from ..DAOs.appointmentDAO import AppointmentDAO, SlotCancellingAppointments
from ..DAOs.clinicDoctorDAO import ClinicDoctorMapDAO, DoctorsForClinic
from ..DAOs.slotDAO import <PERSON>lotDAO, SlotListingQueryFilter, SlotQueryFields
from ..DAOs.specializationDAO import SpecializationDAO, GetAreasForSpecialization
from ..DataModels.virtual_slot import VirtualSlot, DayObject, AvailabilityTypes
from ..DataModels.slots import Slot, SlotStatus
from ..DataModels.appointment import Appointment
from ..DAOs.virtualSlotDAO import <PERSON>SlotDAO, GetSlotsByDay
from pydantic import validator, root_validator, Field
from fastapi import HTTPException
import time
from typing import Optional, List
from ..services.slots import modify_slot_list
from uuid import uuid4

time_format =  "%I:%M %p"

class SlotObject(BaseModel):
    start_time: str
    end_time: str
    break_start: str | None = None
    break_end: str | None = None

    @classmethod
    @root_validator(pre = False)
    def validate_break(cls, values):
        if values.get("break_start") is not None:
            if values.get("break_end") is None:
                raise ValueError("Break duration is invalid!!!!!")
        elif values.get("break_end") is not None:
            raise ValueError("Break duration is invalid!!!!!!")
        return values

    @classmethod
    def create_from_day_object_list(cls, day_object_list: List[DayObject] = [], *args, **kwargs):
        if len(day_object_list) == 0:
            return None
        if len(day_object_list) == 1:
            _obj = day_object_list[0]
            return cls(start_time = _obj.starts_at, end_time = _obj.ends_at)
        elif len(day_object_list) == 2:
            break_start = None
            break_end = None
            start_times = sorted([time.strptime(_obj.starts_at, time_format) for _obj in day_object_list])
            end_times = sorted([time.strptime(_obj.ends_at, time_format) for _obj in day_object_list])
            if start_times[-1]>end_times[0]:
                break_end = time.strftime(time_format, start_times[-1])
                break_start = time.strftime(time_format, end_times[0])
            return cls(start_time = time.strftime(time_format, start_times[0]),
                        end_time = time.strftime(time_format, end_times[-1]),
                          break_start =  break_start,
                            break_end =  break_end
            )
        raise HTTPException(500, {"details": "Too many Objects, cannot determine slot and break durations!!!!!"})

class DaySlots(BaseModel):
    InClinic: Optional[SlotObject]
    Virtual: Optional[SlotObject]

    @classmethod
    def create_from_slot_list(cls, slot_list, *args, **kwargs):
        virtual_slots = [_obj for _obj in slot_list if _obj.availability_type == AvailabilityTypes.virtual]
        in_clinic_slots = [_obj for _obj in slot_list if _obj.availability_type == AvailabilityTypes.in_clinic]
        return cls(InClinic = SlotObject.create_from_day_object_list(in_clinic_slots), Virtual = SlotObject.create_from_day_object_list(virtual_slots))

class AdminSlotDTO(GetModelDTO):
    start_date: date
    end_date: date
    doctorid: str
    monday: Optional[DaySlots]
    tuesday: Optional[DaySlots]
    wednesday: Optional[DaySlots]
    thursday: Optional[DaySlots]
    friday: Optional[DaySlots]
    saturday: Optional[DaySlots]
    sunday: Optional[DaySlots]

    @classmethod
    def create_from_model(cls, _obj: VirtualSlot, *args, **kwargs):
        return cls(
            start_date = _obj.get_start_date(),
            end_date = _obj.get_end_date(),
            doctorid = _obj.doctorid,
            monday = DaySlots.create_from_slot_list(_obj.monday),
            tuesday = DaySlots.create_from_slot_list(_obj.tuesday),
            wednesday = DaySlots.create_from_slot_list(_obj.wednesday),
            thursday = DaySlots.create_from_slot_list(_obj.thursday),
            friday = DaySlots.create_from_slot_list(_obj.friday),
            saturday = DaySlots.create_from_slot_list(_obj.saturday),
            sunday = DaySlots.create_from_slot_list(_obj.sunday),
        )


class SlotList(BaseDTO):
    consultation_fees: int | None
    available_slots: List[str]

class BookingSlotListingDTO(BaseDTO):
    doctorid: str
    firstname: str
    lastname: str
    gender: str
    bio: List[str]
    graduation: str
    masters: str
    doctortype: str
    specialization: str
    specialization_field: str | None
    languages: List[str]
    practice_areas: List[str] = Field(alias = "practice_area")
    interest_area: List[str] = Field(alias = "interest_area")
    consultation_symptoms: List[str] = Field(alias = "consultation_symptoms")
    availability_slotid: str
    doctor_profile_picture: dict
    consultation_durations: List[int]
    slots: dict[str, SlotList]

    @staticmethod
    def get_weekday(_date, lower = False):
        weekday = _date.strftime('%A')
        if lower:
            weekday = weekday.lower()
        return weekday

    @staticmethod
    def get_doctor(doctor_id, all_doctors):
        for doctor_data in all_doctors:
            if doctor_data.doctorid == doctor_id:
                return doctor_data
        return None
    
    @staticmethod
    def get_doctor_profile(doctor_id, doctor_profiles):
        for doctor_data in doctor_profiles:
            if doctor_data.doctorid == doctor_id:
                return doctor_data
        return None
    
    @staticmethod
    def generate_slots_for_a_duration(slot_list, duration, search_date):
        now = datetime.now()
        final_slots = []
        slot_gap = duration
        if slot_gap == 60:
            slot_gap = 30
        if slot_gap == 90:
            slot_gap = 30

        for slot in slot_list:
            start_time: datetime = slot.get("start_time")
            end_time = start_time + timedelta(minutes = duration)
            while end_time <= slot.get("end_time"):
                if datetime.combine(search_date.date(), start_time.time()) >= now:
                    final_slots.append(start_time.strftime(time_format))
                start_time += timedelta(minutes = slot_gap)
                end_time += timedelta(minutes = slot_gap)
        if final_slots == []:
            raise ValueError("Empty slots!!!!")
        return final_slots
     
    @staticmethod
    def generate_slots(slot_list, duration_list, doctor_data, availability_type, search_date):
        slots = {}
        for duration in duration_list:
            consultation_fees = BookingSlotListingDTO.get_consultation_fees(doctor_data, availability_type, duration)
            if consultation_fees is not None:
                try:
                    actual_duration = duration
                    if duration == 85:
                        actual_duration = 90
                    slots[str(duration)] = SlotList(consultation_fees = consultation_fees, available_slots = BookingSlotListingDTO.generate_slots_for_a_duration(slot_list, actual_duration, search_date))
                except ValueError as e:
                    slots[str(duration)] = SlotList(consultation_fees = consultation_fees, available_slots = [])

            else:
                slots[str(duration)] = SlotList(consultation_fees = consultation_fees, available_slots = [])

        if slots == {}:
            raise ValueError("Empty slots!!!!")
        for k, v in slots.items():
            if v.available_slots != []:
                break
        else:
            raise ValueError("No slots available for the doctor!!!!!!")
        return slots

    @staticmethod
    def get_consultation_duration_object_list(doctor_data, availability_type):
        key = {
            "Virtual": "virtual",
            "InClinic": "clinic"
        }.get(availability_type)
        return doctor_data.get(f"{key}_consultation_and_fees")
        
    @staticmethod
    def get_consultation_durations(doctor_data, availability_type, add_null = True):
        duration_list = BookingSlotListingDTO.get_consultation_duration_object_list(doctor_data, availability_type)
        if not add_null:
            duration_list = list(filter(lambda x: x.get("fees") is not None, duration_list))
        return [x.get("slot_duration") for x in duration_list]
        
    @staticmethod
    def get_consultation_fees(doctor_data, availability_type, duration):
        duration_list = BookingSlotListingDTO.get_consultation_duration_object_list(doctor_data, availability_type)
        for item in duration_list:
            if item.get("slot_duration") == duration:
                return item.get("fees")
        return None
    



    @classmethod
    def do(cls, search_date, specialization = None, availability_type = "Virtual", clinic_id = None, user_id = None, *args, **kwargs):

        if clinic_id == "":
            clinic_id = None
        
        if user_id == "":
            user_id = None

        if specialization == "":
            specialization = None
        
        specializations = SpecializationDAO().list(0, 1000, GetAreasForSpecialization())
        specialization_mapping = {x.specialization:x.specialization_field for x in specializations}

        weekday = cls.get_weekday(search_date, lower = True)
        day_end = search_date + timedelta(hours = 23, minutes = 59, seconds = 59)

        all_doctors = DoctorDAO().list(0, 100, DoctorQueryFields(specialization = specialization))

        if clinic_id is not None:
            clinic_doctors = ClinicDoctorMapDAO().list(0, 50, DoctorsForClinic(clinicid = clinic_id))
            clinic_doctor_ids = [x.doctorid for x in clinic_doctors]
            all_doctors = [x for x in all_doctors if x.doctorid in clinic_doctor_ids]
        
        doctor_ids = [x.doctorid for x in all_doctors]


        doctor_slot_list = VirtualSlotDAO().list(0, 20, GetSlotsByDay(doctorids = doctor_ids, lower_limit = search_date, upper_limit = day_end, weekday = weekday, availability_type = availability_type))

        doctor_slots_dict = {}
        for slot in doctor_slot_list:
            if slot.doctorid not in doctor_slots_dict:
                doctor_slots_dict[slot.doctorid] = []
            if availability_type == "InClinic":
                doctor_slots_dict[slot.doctorid].extend(filter(lambda x: x.availability_type == availability_type, getattr(slot, weekday.lower())))
            elif availability_type == "Virtual":
                all_slots = getattr(slot, weekday.lower())
                for _slot in all_slots:
                    _slot.availability_type = "Virtual"
                doctor_slots_dict[slot.doctorid].extend(all_slots)
            

        # Fetch all adhoc_slots, blocks and appointments
        
        adhocs = SlotDAO().list(0, 1000, SlotListingQueryFilter(doctorids = doctor_ids, start_time = search_date, end_time = day_end, sub_type = availability_type, status = SlotStatus.active))
        adhoc_slot_dict = {}
        block_dict = {}
        for slot in adhocs:
            if slot.slot_type == "BLOCK":
                if slot.doctor_id not in block_dict:
                    block_dict[slot.doctor_id] = []
                block_dict[slot.doctor_id].append(slot)
            elif slot.slot_type == "SLOT":
                if slot.doctor_id not in adhoc_slot_dict:
                    adhoc_slot_dict[slot.doctor_id] = []
                adhoc_slot_dict[slot.doctor_id].append(slot)
        
        appointments: List[Appointment] = AppointmentDAO().list(0, 1000, SlotCancellingAppointments(doctor_ids = doctor_ids, start_time = search_date, end_time = day_end, user_id = user_id))

        for appointment in appointments:
            if appointment.doctorid not in block_dict:
                block_dict[appointment.doctorid] = []
            block = Slot(slot_type = "BLOCK", sub_type = appointment.appointment_type, doctor_id = appointment.doctorid, status = "active", start_time = appointment.appointment_slot, end_time = appointment.end_date)
            block_dict[appointment.doctorid].append(block)
        
        
        modified_slots_dict = {}
        
        #Get doctor Profiles

        doctor_profiles = DoctorProfileDAO().retrieve_from_list(doctor_ids, "doctorid")


        for doctor_id in doctor_ids:
            slots = doctor_slots_dict.get(doctor_id, [])
            blocks = block_dict.get(doctor_id, [])
            adhoc_slots = adhoc_slot_dict.get(doctor_id, [])
            modified_slots = modify_slot_list([x.dict() for x in slots],search_date, blocks, adhoc_slots)
            modified_slots_dict[doctor_id] = modified_slots

        doctor_ids = list(modified_slots_dict.keys())

        final_list = []
        for doctor in doctor_ids:
            if doctor not in modified_slots_dict:
                continue
            doctor_data = cls.get_doctor(doctor, all_doctors).dict()
            doctor_profile = cls.get_doctor_profile(doctor, doctor_profiles).__dict__
            specialization_field = specialization_mapping.get(doctor_data.get("specialization"))
            try:
                final_list.append(cls(**{
                    **doctor_data,
                    **doctor_profile,
                    "specialization_field": specialization_field,
                    "availability_slotid": str(uuid4()),
                    "doctor_profile_picture":{
                        "image_id": doctor_data.get("image_id"),
                        "profile_image_url": doctor_data.get("profile_image_url")
                    },
                    "consultation_durations": cls.get_consultation_durations(doctor_data, availability_type, add_null = False),
                    "slots": BookingSlotListingDTO.generate_slots(modified_slots_dict[doctor], BookingSlotListingDTO.get_consultation_durations(doctor_data, availability_type), doctor_data, availability_type, search_date)
                }))
                if "couple" in specialization.lower():
                    if 90 not in final_list[-1].consultation_durations:
                        final_list.pop()
                    else :
                        final_list[-1].consultation_durations = [90]
                        for k, v in final_list[-1].slots.items():
                            if k != "90":
                                final_list[-1].slots[k] = {"available_slots": [], "consultation_fees": None}
                elif "family" in specialization.lower():
                    if 85 not in final_list[-1].consultation_durations:
                        final_list.pop()
                    else:
                        final_list[-1].consultation_durations = [85]
                        for k, v in final_list[-1].slots.items():
                            if k != "85":
                                final_list[-1].slots[k] = {"available_slots": [], "consultation_fees": None}
                else:
                    final_list[-1].consultation_durations = [x for x in final_list[-1].consultation_durations if x in [20, 30, 60]]
                    for k, v in final_list[-1].slots.items():
                        if k not in ["20", "30", "60"]:
                            final_list[-1].slots[k] = {"available_slots": [], "consultation_fees": None}

                
            except Exception as e:
                print()
                # print(f"error adding doctor {doctor} in Slot listing response due to the error: \n{str(e)}")
        if len(final_list):
            return [x.dict() for x in final_list], None
        return None, "There are no available appointments for selected date or date range"


    @classmethod
    def get_slots_for_doctor(cls, doctor_id, search_date = datetime.today(), availability_type: AvailabilityTypes = AvailabilityTypes.virtual, user_id = None, *args, **kwargs):
        weekday = cls.get_weekday(search_date, lower = True)
        day_end = search_date + timedelta(hours = 23, minutes = 59, seconds = 59)
        doctor_virtual_slot = VirtualSlotDAO().list(filter = GetSlotsByDay(doctorids = [doctor_id], lower_limit = search_date, upper_limit = day_end, weekday = weekday.lower(), availability_type = availability_type))
        if doctor_virtual_slot == []:
            raise HTTPException(400, "No Slot found for doctor_id {} on {} !!!!!".format(doctor_id, search_date.strftime("%d-%m-%y")))
        doctor_virtual_slot = doctor_virtual_slot[0]
        specializations = SpecializationDAO().list(0, 1000, GetAreasForSpecialization())
        specialization_mapping = {x.specialization:x.specialization_field for x in specializations}
        doctor_data = DoctorDAO().list(filter = GetDoctor(doctorid = doctor_id))
        if doctor_data == []:
            raise HTTPException(400, f"No Doctor data found for doctor_id {doctor_id} !!!!")
        doctor_data = doctor_data[0].dict()
        doctor_profile = DoctorProfileDAO().get(_id = doctor_id).__dict__
        ad_hocs: List[Slot] = SlotDAO().list(0, 1000, filter = SlotListingQueryFilter(doctorids = [doctor_id], start_time = search_date, end_time = day_end, status = SlotStatus.active, sub_type =  availability_type))
        appointments: List[Appointment] = AppointmentDAO().list(0, 1000, SlotCancellingAppointments(doctor_ids = [doctor_id], start_time = search_date, end_time = day_end, user_id = user_id))
        slots = [x for x in filter(lambda x: x.slot_type == "SLOT", ad_hocs )]
        blocks = [x for x in filter(lambda x: x.slot_type == "BLOCK", ad_hocs)]
        for appointment in appointments:
            blocks.append(
                Slot(slot_type = "BLOCK", sub_type = appointment.appointment_type, doctor_id = appointment.doctorid, status = "active", start_time = appointment.appointment_slot, end_time = appointment.end_date)
            )
        doctor_main_slots = []
        if availability_type == "InClinic":
            doctor_main_slots.extend(filter(lambda x: x.availability_type == availability_type, getattr(doctor_virtual_slot, weekday.lower())))
        elif availability_type == "Virtual":
            all_slots = getattr(doctor_virtual_slot, weekday.lower())
            for _slot in all_slots:
                _slot.availability_type = "Virtual"
            doctor_main_slots.extend(all_slots)

        doctor_main_slots = [x.dict() for x in doctor_main_slots]
        modified_slots = modify_slot_list(slot_list = doctor_main_slots, slot_date = search_date, block_list = blocks, adhoc_slot_list = slots)
         
        try:

            return cls(**{
            **doctor_data, **doctor_profile,
            "specialization_field": specialization_mapping.get(doctor_data.get("specialization")),
            "availability_slotid": str(uuid4()),
            "doctor_profile_picture": {
                "image_id": doctor_data.get("image_id"),
                "profile_image_url": doctor_data.get("profile_image_url")
            },
            #"consultation_durations": [],
            "consultation_durations": cls.get_consultation_durations(doctor_data, availability_type, add_null = False),
            "slots": BookingSlotListingDTO.generate_slots(modified_slots, BookingSlotListingDTO.get_consultation_durations(doctor_data, availability_type), doctor_data, availability_type, search_date)
        })
        except ValueError as e:
            raise HTTPException(400, str(e))



        

        

        



