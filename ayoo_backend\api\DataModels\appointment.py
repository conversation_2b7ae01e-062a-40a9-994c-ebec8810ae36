from .basemodel import BaseMongoModel, BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum
from .payment import PaymentStatus
from pydantic import Field


class AppointmentType(str, Enum):
    virtual = "Virtual"
    in_clinic = "InClinic"


class FollowUpType(str, Enum):
    paid = "Paid"
    free = "Free"


class AppointmentStatus(str, Enum):
    pending = "Pending"
    abandoned = "Abandoned"
    cancelled = "Cancelled"
    completed = "Completed"
    discarded = "Discarded"
    failed = "Failed"
    reschedule = "Reschedule"
    no_show = "No Show"
    initiated = "Initiated"
    initiate = "Initiate"
    booked = "Booked"


class Status(BaseModel):
    status: AppointmentStatus
    reason: str = Field(None, alias="Reason")
    comment: str = None
    next_appointment_id: Optional[str]


class Appointment(BaseMongoModel):
    additional_notes: Optional[str] = ''
    appointment_for: str
    appointment_id: str
    appointment_slot: datetime
    appointment_type: AppointmentType
    booked_by: str
    booked_by_name: str
    care_type: Optional[str]
    case_open: Optional[bool]
    caseid: str | None
    children_included: bool = False
    clinicid: str = None
    created_at: datetime
    doctor_name: str
    doctorid: str
    end_date: datetime
    follow_up_type: FollowUpType = None
    is_active: bool
    is_confirmed: bool
    is_first_appointment: bool | None
    patient_id: str
    patients: List
    payment: int
    payment_status: PaymentStatus
    status: Status
    symptoms: List[str]
    user_reschedule_allowed: bool = True
    user_cancellation_allowed: bool = True
    symptoms_audio_clip: str
    booked_via: Optional[str]
    previous_appointments: Optional[List[dict]] = Field(default_factory=list)
    extension: Optional[bool]
    is_extended: Optional[str]
    is_couple_or_family_therapy: bool = False

    def toggle_type(self, *args, **kwargs):
        if self.appointment_type == AppointmentType.virtual:
            self.appointment_type = AppointmentType.in_clinic
            return self
        elif self.appointment_type == AppointmentType.in_clinic:
            self.appointment_type = AppointmentType.virtual
            return self
        raise Exception("Something went wrong!!!!!")
